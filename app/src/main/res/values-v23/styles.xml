<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="SplashTheme" parent="Theme.AppCompat.Light.NoActionBar">

        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="android:navigationBarColor">@color/colorDark100</item>
        <item name="bottomSheetDialogTheme">@style/AppBottomSheetDialogTheme</item>

    </style>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">

        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/grey800</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDarko</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorControlHighlight</item>
        <item name="bottomSheetDialogTheme">@style/AppBottomSheetDialogTheme</item>
        <item name="android:navigationBarColor">@color/colorDark100</item>
        <item name="android:popupMenuStyle">@style/PopupMenu</item>
        <item name="android:textAppearanceLargePopupMenu">@style/PopupMenuTextAppearanceLarge</item>
        <item name="android:textAppearanceSmallPopupMenu">@style/PopupMenuTextAppearanceSmall</item>

    </style>

    <style name="OmniSearchChipStyle" parent="Widget.MaterialComponents.Chip.Choice">
        <item name="chipBackgroundColor">@color/omnisearch_background_color_selector</item>
        <item name="chipStrokeColor">@color/omnisearch_stroke_color_selector</item>
        <item name="android:textColor">@color/omnisearch_text_color_selector</item>
        <item name="chipStrokeWidth">1dp</item>
    </style>

    <style name="itemTextStyle.AppTheme" parent="@android:style/TextAppearance.Widget.IconMenu.Item">
        <item name="android:textColor">@color/grey800</item>
        <item name="android:textSize">10sp</item>
    </style>

    <!-- Popup Menu Style -->
    <style name="PopupMenu" parent="@style/Widget.AppCompat.Light.PopupMenu">
        <item name="android:popupBackground">@android:color/white</item>
        <item name="android:textColor">@color/grey800</item>
        <item name="android:textSize">14sp</item>
        <item name="fontFamily">@font/inter_medium</item>
    </style>

    <style name="PopupMenuTextAppearanceSmall" parent="@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small">
        <item name="android:textColor">@color/grey800</item>
        <item name="android:textSize">14sp</item>
        <item name="fontFamily">@font/inter_medium</item>
    </style>

    <style name="PopupMenuTextAppearanceLarge" parent="@style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large">
        <item name="android:textColor">@color/grey800</item>
        <item name="android:textSize">14sp</item>
        <item name="fontFamily">@font/inter_medium</item>
    </style>

    <style name="PopupAnimation">
        <item name="android:windowEnterAnimation">@anim/nav_default_enter_anim</item>
        <item name="android:windowExitAnimation">@anim/nav_default_exit_anim</item>
    </style>

</resources>
