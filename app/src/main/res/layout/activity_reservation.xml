<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">
<androidx.constraintlayout.widget.ConstraintLayout
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/mainBcg"
    tools:context=".ui.reservation.ReservationActivity">

    <com.google.android.material.appbar.AppBarLayout
        android:id="@+id/appBarLayout2"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.appcompat.widget.Toolbar
            android:id="@+id/toolbar"
            android:layout_width="match_parent"
            android:layout_height="?attr/actionBarSize"
            android:background="@color/grey800"
            android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
            app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:orientation="horizontal"
                android:paddingStart="-8dp">

                <ImageButton
                    android:id="@+id/backBtnR"
                    android:layout_width="@dimen/toolbar_icon_size"
                    android:layout_height="@dimen/toolbar_icon_size"
                    android:layout_marginTop="2dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:tint="@color/white"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_icon_back" />

                <ImageButton
                    android:id="@+id/printBtn"
                    android:layout_width="@dimen/toolbar_icon_size"
                    android:layout_height="@dimen/toolbar_icon_size"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="@dimen/global_margin_16"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.428"
                    app:srcCompat="@drawable/ic_icon_print"
                    app:tint="@color/white" />

                <ImageButton
                    android:id="@+id/infoButton"
                    android:layout_width="@dimen/toolbar_icon_size"
                    android:layout_height="@dimen/toolbar_icon_size"
                    android:layout_marginTop="2dp"
                    android:layout_marginEnd="@dimen/margin_8"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/printBtn"
                    app:layout_constraintTop_toTopOf="parent"
                    app:layout_constraintVertical_bias="0.428"
                    app:srcCompat="@drawable/ic_icon_info"
                    app:tint="@color/white" />

                <TextView
                    android:id="@+id/activityTitleR"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="8dp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/label_res_activity"
                    android:textColor="@color/white"
                    android:textSize="17sp"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toStartOf="@+id/infoButton"
                    app:layout_constraintStart_toEndOf="@+id/backBtnR"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <androidx.fragment.app.FragmentContainerView
        android:id="@+id/nav_host_fragment"
        android:name="androidx.navigation.fragment.NavHostFragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:defaultNavHost="true"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/appBarLayout2"
        app:navGraph="@navigation/reservation_nav_graph" />

</androidx.constraintlayout.widget.ConstraintLayout>
</layout>