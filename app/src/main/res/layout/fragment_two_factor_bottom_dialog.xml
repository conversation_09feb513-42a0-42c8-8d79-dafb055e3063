<?xml version="1.0" encoding="utf-8"?>

<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <LinearLayout
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="vertical">

            <ImageView
                android:layout_width="28dp"
                android:layout_height="28dp"
                android:src="@drawable/ic_icon_lock"
                app:tint="@color/red500"
                tools:ignore="ContentDescription" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_12"
                android:fontFamily="@font/inter_medium"
                android:text="@string/otp_title"
                android:textColor="@color/grey900"
                android:textSize="@dimen/text_size_18" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="50dp"
                android:layout_marginTop="@dimen/margin_8"
                android:layout_marginEnd="50dp"
                android:fontFamily="@font/inter_regular"
                android:gravity="center_horizontal"
                android:text="@string/otp_subtitle"
                android:textColor="@color/grey700"
                android:textSize="@dimen/text_size_13" />

            <EditText
                android:id="@+id/et_otp"
                android:layout_width="wrap_content"
                android:minWidth="300dp"
                android:layout_marginStart="50dp"
                android:layout_marginEnd="50dp"
                android:layout_height="44dp"
                android:layout_marginTop="@dimen/margin_16"
                android:background="@drawable/shape_rounded_btn_bcg_grey_outline"
                android:fontFamily="@font/inter_semibold"
                android:hint="@string/otp_hint"
                android:gravity="center"
                android:letterSpacing="0.5"
                android:maxLength="6"
                android:paddingStart="@dimen/margin_16"
                android:paddingEnd="@dimen/margin_16"
                android:textColor="@color/grey700"
                android:textColorHint="@color/grey300"
                android:textSize="@dimen/text_size_15" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_12"
                android:fontFamily="@font/inter_regular"
                android:text="@string/otp_description"
                android:textColor="@color/grey500"
                android:textSize="@dimen/text_size_11" />

        </LinearLayout>

        <com.eatapp.clementine.views.LoadingButton
            android:id="@+id/btn_continue"
            android:layout_width="match_parent"
            android:layout_height="@dimen/button_height"
            android:layout_marginStart="16dp"
            android:layout_marginEnd="16dp"
            android:layout_marginBottom="16dp"
            android:background="@drawable/shape_rounded_btn_bcg_green"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:progressBarColor="@color/white"
            app:title="@string/continue_setup" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>