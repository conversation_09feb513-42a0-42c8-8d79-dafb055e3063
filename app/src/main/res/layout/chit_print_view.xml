<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/main_cont"
    android:layout_width="300dp"
    android:layout_height="wrap_content"
    android:animateLayoutChanges="true"
    android:background="@color/white"
    android:orientation="vertical"
    android:paddingStart="4dp"
    android:paddingBottom="90dp">

    <TextView
        android:id="@+id/guest"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:fontFamily="@font/inter_semibold"
        android:text="John Doe"
        android:textColor="@color/colorDark50"
        android:textSize="20sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintHorizontal_bias="0.0"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <ImageView
        android:id="@+id/imageView10"
        android:layout_width="@dimen/global_icon_size_24"
        android:layout_height="@dimen/global_icon_size_24"
        android:layout_marginTop="12dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/guest"
        app:srcCompat="@drawable/ic_icon_people" />

    <TextView
        android:id="@+id/covers"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:fontFamily="@font/inter_semibold"
        android:text="12 covers"
        android:textColor="@color/colorDark50"
        android:textSize="15sp"
        app:layout_constraintBottom_toBottomOf="@+id/imageView10"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/imageView10"
        app:layout_constraintTop_toTopOf="@+id/imageView10" />

    <View
        android:id="@+id/view18"
        android:layout_width="wrap_content"
        android:layout_height="1dp"
        android:layout_marginTop="@dimen/global_margin_16"
        android:background="@color/colorDark50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/covers" />

    <LinearLayout
        android:id="@+id/linearLayout10"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/global_margin_16"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view18">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imageView22asd"
                android:layout_width="@dimen/global_icon_size_24"
                android:layout_height="@dimen/global_icon_size_24"
                app:srcCompat="@drawable/ic_icon_time" />

            <TextView
                android:id="@+id/time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/inter_semibold"
                android:text="12 covers"
                android:textColor="@color/colorDark50"
                android:textSize="15sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/tables_cont"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/global_margin_16"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imageView2asd2"
                android:layout_width="@dimen/global_icon_size_24"
                android:layout_height="@dimen/global_icon_size_24"
                app:srcCompat="@drawable/ic_icon_table" />

            <TextView
                android:id="@+id/tables"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/inter_semibold"
                android:text="6a, 7a"
                android:textColor="@color/colorDark50"
                android:textSize="15sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/server_cont"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="@dimen/global_margin_16"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/imageView22"
                android:layout_width="@dimen/global_icon_size_24"
                android:layout_height="@dimen/global_icon_size_24"
                app:srcCompat="@drawable/ic_icon_server" />

            <TextView
                android:id="@+id/server"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:fontFamily="@font/inter_semibold"
                android:text="Server Name"
                android:textColor="@color/colorDark50"
                android:textSize="15sp" />
        </LinearLayout>
    </LinearLayout>

    <LinearLayout
        android:id="@+id/notes_tags_cont"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="@dimen/global_margin_16"
        android:orientation="vertical"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/linearLayout10">

        <View
            android:id="@+id/view19"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorDark50"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView21" />

        <LinearLayout
            android:id="@+id/guest_notes_cont"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/global_margin_16"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewdsda35"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_light"
                android:text="@string/guest_notes"
                android:textAllCaps="true"
                android:textColor="@color/colorDark50"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/guest_notes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_weight="1"
                android:fontFamily="@font/inter_semibold"
                android:text="Has a shellfish allergy"
                android:textColor="@color/colorDark50"
                android:textSize="15sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/guest_tags_cont"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/global_margin_16"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textViewasdasf35"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_light"
                android:text="@string/guest_tags"
                android:textAllCaps="true"
                android:textColor="@color/colorDark50"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/guest_tags"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_weight="1"
                android:fontFamily="@font/inter_semibold"
                android:text="Vip, High Spender"
                android:textColor="@color/colorDark50"
                android:textSize="15sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/reservation_notes_cont"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/global_margin_16"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textView35asdasd"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_light"
                android:text="@string/reservation_notes"
                android:textAllCaps="true"
                android:textColor="@color/colorDark50"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/reservation_notes"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_weight="1"
                android:fontFamily="@font/inter_semibold"
                android:text="Requested a table by the window. Non smoking if possible."
                android:textColor="@color/colorDark50"
                android:textSize="15sp" />
        </LinearLayout>

        <LinearLayout
            android:id="@+id/reservation_tags_cont"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginTop="@dimen/global_margin_16"
            android:gravity="center_vertical"
            android:orientation="vertical">

            <TextView
                android:id="@+id/textView35"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_light"
                android:text="@string/reservation_tags"
                android:textAllCaps="true"
                android:textColor="@color/colorDark50"
                android:textSize="15sp" />

            <TextView
                android:id="@+id/reservation_tags"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_weight="1"
                android:fontFamily="@font/inter_semibold"
                android:text="Birthday, Celebration, Non smoking"
                android:textColor="@color/colorDark50"
                android:textSize="15sp" />
        </LinearLayout>

    </LinearLayout>

    <View
        android:id="@+id/view20"
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:layout_marginTop="16dp"
        android:background="@color/colorDark50"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/notes_tags_cont" />

    <ImageView
        android:id="@+id/imageView23"
        android:layout_width="60dp"
        android:layout_height="30dp"
        android:layout_marginTop="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/view20"
        app:srcCompat="@drawable/ic_logo" />

    <TextView
        android:id="@+id/time_created"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:fontFamily="@font/inter_regular"
        android:text="12.08.2022"
        android:textColor="@color/colorDark50"
        android:textSize="15sp"
        app:layout_constraintBottom_toBottomOf="@+id/imageView23"
        app:layout_constraintEnd_toEndOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>