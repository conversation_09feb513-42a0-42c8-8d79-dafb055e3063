<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="guest"
            type="com.eatapp.clementine.data.network.response.guest.Guest" />

        <import type="android.view.View" />

        <import type="android.text.TextUtils" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white">

        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_16"
            android:foreground="?attr/selectableItemBackground"
            android:orientation="vertical"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_goneMarginStart="@dimen/margin_16">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_12"
                android:layout_marginEnd="@dimen/margin_16"
                android:orientation="horizontal"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tv_guest_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:ellipsize="end"
                    android:fontFamily="@font/inter_regular"
                    android:textColor="@color/grey700"
                    android:textSize="@dimen/text_size_14"
                    tools:text="Bruce Lee Bruce Lee " />

                <TextView
                    android:visibility="gone"
                    android:id="@+id/tv_example"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center_vertical"
                    android:layout_marginStart="@dimen/margin_8"
                    android:layout_weight="0"
                    android:background="@drawable/shape_rounded_btn_bcg_orange_100"
                    android:fontFamily="@font/inter_semibold"
                    android:paddingStart="@dimen/margin_4"
                    android:paddingEnd="@dimen/margin_4"
                    android:text="@string/example_label"
                    android:textColor="@color/grey900"
                    android:textSize="@dimen/text_size_12" />
            </LinearLayout>

            <TextView
                android:id="@+id/tv_phone"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_4"
                android:layout_marginEnd="@dimen/margin_16"
                android:fontFamily="@font/inter_medium"
                android:textColor="@color/grey600"
                android:textSize="@dimen/text_size_12"
                android:visibility="@{TextUtils.isEmpty(guest.phone) ? View.GONE : View.VISIBLE}"
                tools:text="+987123123123" />

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_4"
                android:layout_marginEnd="@dimen/margin_16"
                android:layout_marginBottom="@dimen/margin_12"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/tv_total_visit_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:fontFamily="@font/inter_semibold"
                    android:text='@{String.format("%s", guest.visitCount)}'
                    android:textColor="@color/grey500"
                    android:textSize="@dimen/text_size_12" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_4"
                    android:fontFamily="@font/inter_regular"
                    android:text="@string/total_visits"
                    android:textColor="@color/grey500"
                    android:textSize="@dimen/text_size_12" />

                <TextView
                    android:id="@+id/tv_no_shows_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_12"
                    android:fontFamily="@font/inter_semibold"
                    android:text='@{String.format("%s", guest.noShowCount)}'
                    android:textColor="@color/grey500"
                    android:textSize="@dimen/text_size_12" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_4"
                    android:fontFamily="@font/inter_regular"
                    android:text="@string/no_shows"
                    android:textColor="@color/grey500"
                    android:textSize="@dimen/text_size_12" />

                <TextView
                    android:id="@+id/tv_denied_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_12"
                    android:fontFamily="@font/inter_semibold"
                    android:text='@{String.format("%s", guest.deniedCount)}'
                    android:textColor="@color/grey500"
                    android:textSize="@dimen/text_size_12" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_4"
                    android:fontFamily="@font/inter_regular"
                    android:text="@string/denied"
                    android:textColor="@color/grey500"
                    android:textSize="@dimen/text_size_12" />

            </LinearLayout>

        </LinearLayout>

        <View
            android:id="@+id/bottom_separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>


</layout>