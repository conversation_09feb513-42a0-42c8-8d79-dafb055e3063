<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto">

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        android:layout_width="match_parent"
        android:layout_height="28dp"
        android:paddingStart="@dimen/margin_20"
        android:paddingEnd="@dimen/margin_20">

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:fontFamily="@font/inter_medium"
            android:textColor="@color/grey400"
            android:textSize="@dimen/text_size_14"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            app:layout_constraintStart_toEndOf="@id/tv_title"
            android:layout_marginStart="@dimen/margin_8"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            android:background="@color/grey500"
            android:layout_width="0dp"
            android:layout_height="1dp" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>