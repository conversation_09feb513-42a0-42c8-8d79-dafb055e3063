<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:paddingStart="@dimen/margin_16"
        android:paddingEnd="@dimen/margin_16"
        tools:background="@color/grey800">

        <TextView
            android:id="@+id/tv_restaurant_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_4"
            android:fontFamily="@font/inter_regular"
            android:textColor="@color/white"
            android:textSize="@dimen/text_size_14"
            android:maxLines="1"
            android:ellipsize="end"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toStartOf="@id/iv_checked"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Eat demo restaurant" />

        <ImageView
            android:id="@+id/iv_checked"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:src="@drawable/ic_icon_check"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:tint="@color/white" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>