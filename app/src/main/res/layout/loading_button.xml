<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:animateLayoutChanges="true"
    android:paddingStart="@dimen/margin_16"
    android:paddingEnd="@dimen/margin_16">

    <LinearLayout
        android:id="@+id/main_container"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:minHeight="30dp"
        android:orientation="horizontal"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <ImageView
            android:id="@+id/left_icon"
            android:layout_width="@dimen/global_icon_size_24"
            android:layout_height="@dimen/global_icon_size_24"
            android:layout_marginEnd="@dimen/margin_6"
            android:visibility="gone"
            app:srcCompat="@drawable/ic_icon_google"
            tools:visibility="visible" />

        <androidx.appcompat.widget.AppCompatTextView
            android:id="@+id/title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:ellipsize="middle"
            android:fontFamily="@font/inter_semibold"
            android:gravity="center"
            android:minHeight="30dp"
            android:singleLine="true"
            android:textAlignment="center"
            android:textAllCaps="false"
            android:textColor="@android:color/black"
            android:textSize="@dimen/text_size_15"
            tools:text="Create reservation" />

        <ImageView
            android:id="@+id/right_icon"
            android:layout_width="@dimen/global_icon_size_24"
            android:layout_height="@dimen/global_icon_size_24"
            android:layout_marginStart="@dimen/margin_6"
            android:visibility="gone"
            app:srcCompat="@drawable/ic_icon_google"
            tools:visibility="visible" />

    </LinearLayout>

    <ProgressBar
        android:id="@+id/progress"
        style="?android:attr/progressBarStyle"
        android:layout_width="30dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        android:layout_marginBottom="8dp"
        android:alpha="0"
        android:indeterminateTint="@color/colorRed"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>