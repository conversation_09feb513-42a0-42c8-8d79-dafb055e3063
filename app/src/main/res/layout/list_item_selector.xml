<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="clickListener"
            type="android.view.View.OnClickListener" />

        <variable
            name="radio"
            type="boolean" />

        <variable
            name="item"
            type="com.eatapp.clementine.internal.SelectorItem" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <LinearLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="@dimen/selector_item_height"
        android:background="@color/white"
        android:onClick="@{clickListener}"
        android:orientation="vertical">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/inner_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:gravity="center_vertical"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/icon"
                android:layout_width="@dimen/global_icon_size_20"
                android:layout_height="@dimen/global_icon_size_20"
                android:layout_marginStart="@dimen/margin_16"
                android:alpha="@{item.isDisabled ? 0.3f : 1.0f}"
                android:tagIcon="@{item}"
                android:visibility="@{(item.icon == null &amp;&amp; item.imageUrl == null) ? View.GONE : View.VISIBLE}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:srcCompat="@drawable/gluten_icon"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/title"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="@dimen/margin_16"
                android:fontFamily="@font/inter_regular"
                android:text='@{item.name}'
                android:textColor="@{item.isDisabled ? @color/colorGrey200 : @color/colorDark50}"
                android:textSize="15sp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toStartOf="@id/imageView5"
                app:layout_constraintStart_toEndOf="@id/icon"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_goneMarginStart="@dimen/margin_16"
                tools:text="Bruce Lee" />

            <ImageView
                android:id="@+id/imageView5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="16dp"
                android:alpha="@{item.isDisabled ? 0.3f : 1.0f}"
                android:src="@{item.isSelected ? (radio ? @drawable/ic_icon_radio_on : @drawable/ic_icon_checkbox_on) : (radio ? @drawable/ic_icon_radio_off : @drawable/ic_icon_checkbox_off)}"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tintColor="@{item.isSelected ? R.color.colorPrimary : R.color.colorGrey200}"
                tools:layout_editor_absoluteX="-46dp"
                tools:layout_editor_absoluteY="-42dp"
                tools:src="@drawable/ic_icon_radio_on"
                tools:tint="@color/colorPrimary" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <View
            android:id="@+id/bottom_separator"
            android:layout_width="match_parent"
            android:layout_height="0.5dp"
            android:background="@color/colorSeparator" />

    </LinearLayout>

</layout>