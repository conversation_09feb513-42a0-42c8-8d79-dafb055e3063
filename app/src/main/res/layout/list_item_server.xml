<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:app="http://schemas.android.com/apk/res-auto" xmlns:tools="http://schemas.android.com/tools" xmlns:android="http://schemas.android.com/apk/res/android">

    <data>

        <variable
                name="clickListener"
                type="android.view.View.OnClickListener"/>

        <variable name="server"
                  type="com.eatapp.clementine.data.network.response.server.Server"/>

        <import type="android.view.View" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@color/white"
        android:foreground="?attr/selectableItemBackground"
        android:onClick="@{clickListener}">

        <View
            android:id="@+id/bottom_separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <TextView
            android:id="@+id/server_name"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:fontFamily="@font/inter_medium"
            android:text='@{server.name}'
            android:textColor="@color/colorDark200"
            android:textSize="15sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="Bruce Lee " />

        <TextView
            android:id="@+id/textView1"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="13dp"
            android:fontFamily="@font/inter_regular"
            android:text="@string/now"
            android:textColor="@color/colorDark200"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/server_name" />

        <TextView
            android:id="@+id/now_served"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="13dp"
            android:fontFamily="@font/inter_medium"
            android:textColor="@color/colorDark200"
            android:textSize="13sp"
            android:text="@{server.nowServed}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/textView1"
            app:layout_constraintTop_toBottomOf="@+id/server_name"
            app:layout_constraintVertical_bias="0.0"
            tools:text="0" />

        <TextView
            android:id="@+id/textView43"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="13dp"
            android:fontFamily="@font/inter_regular"
            android:text="@string/total_"
            android:textColor="@color/colorDark200"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/now_served"
            app:layout_constraintTop_toBottomOf="@+id/server_name"
            app:layout_constraintVertical_bias="0.0" />

        <TextView
            android:id="@+id/total_served"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="13dp"
            android:fontFamily="@font/inter_medium"
            android:textColor="@color/colorDark200"
            android:textSize="13sp"
            android:text="@{server.totalServed}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/textView43"
            app:layout_constraintTop_toBottomOf="@+id/server_name"
            app:layout_constraintVertical_bias="0.0"
            tools:text="0" />

        <TextView
            android:id="@+id/textView98"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="13dp"
            android:fontFamily="@font/inter_regular"
            android:text="@string/last_seated"
            android:textColor="@color/colorDark200"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/total_served"
            app:layout_constraintTop_toBottomOf="@+id/server_name"
            app:layout_constraintVertical_bias="0.0" />

        <TextView
            android:id="@+id/last_seated"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="13dp"
            android:fontFamily="@font/inter_medium"
            android:text="@{server.lastSeated}"
            android:textColor="@color/colorDark200"
            android:textSize="13sp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toEndOf="@+id/textView98"
            app:layout_constraintTop_toBottomOf="@+id/server_name"
            app:layout_constraintVertical_bias="0.0" />

        <FrameLayout
            android:id="@+id/color"
            android:layout_width="4dp"
            android:layout_height="0dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

        </FrameLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>