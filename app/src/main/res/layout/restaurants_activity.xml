<?xml version="1.0" encoding="utf-8"?>
<layout>
<androidx.constraintlayout.widget.ConstraintLayout
        xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        tools:context=".ui.common.selector.RestaurantsActivity"
        android:background="@color/mainBcg">

    <com.google.android.material.appbar.AppBarLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintTop_toTopOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent" android:id="@+id/appBarLayout2">

        <androidx.appcompat.widget.Toolbar
                android:layout_width="match_parent"
                android:layout_height="?attr/actionBarSize"
                android:id="@+id/toolbar"
                android:theme="@style/ThemeOverlay.AppCompat.Dark.ActionBar"
                app:popupTheme="@style/ThemeOverlay.AppCompat.Light"
                android:background="@color/grey800"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toEndOf="parent">

            <androidx.constraintlayout.widget.ConstraintLayout
                    android:orientation="horizontal"
                    android:layout_width="match_parent"
                    android:layout_height="match_parent" android:paddingLeft="-8dp">

                <ImageButton
                        android:layout_width="@dimen/toolbar_icon_size"
                        android:layout_height="@dimen/toolbar_icon_size" app:srcCompat="@drawable/ic_icon_back"
                        android:id="@+id/backBtnRS" app:layout_constraintTop_toTopOf="parent"
                        app:layout_constraintBottom_toBottomOf="parent" app:layout_constraintStart_toStartOf="parent"
                        app:tint="@color/white" android:layout_marginTop="2dp"
                        android:background="?attr/selectableItemBackgroundBorderless"
                />

                <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:text="@string/label_restaurants_activity"
                        android:textColor="@color/white"
                        android:textSize="17sp" android:fontFamily="@font/inter_medium"
                        app:layout_constraintTop_toTopOf="parent" app:layout_constraintBottom_toBottomOf="parent"
                        android:id="@+id/activityTitle" app:layout_constraintStart_toEndOf="@+id/backBtnRS"
                        android:layout_marginStart="8dp" app:layout_constraintEnd_toEndOf="parent"
                        android:layout_marginEnd="16dp"/>

            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.appcompat.widget.Toolbar>

    </com.google.android.material.appbar.AppBarLayout>

    <FrameLayout
            xmlns:android="http://schemas.android.com/apk/res/android"
            xmlns:tools="http://schemas.android.com/tools"
            android:id="@+id/container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            tools:context=".ui.common.selector.RestaurantsActivity"
            app:layout_constraintTop_toBottomOf="@+id/appBarLayout2" app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"/>

</androidx.constraintlayout.widget.ConstraintLayout>
</layout>