<?xml version="1.0" encoding="utf-8"?>
<FrameLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        app:cardCornerRadius="@dimen/margin_8"
        app:cardElevation="3dp"
        android:layout_marginTop="@dimen/margin_8"
        android:layout_marginStart="@dimen/margin_16"
        android:layout_marginEnd="@dimen/margin_8"
        android:layout_marginBottom="@dimen/margin_16"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginEnd="@dimen/margin_16"
            android:background="@drawable/shape_rounded_bcg_white"
            android:orientation="vertical"
            android:padding="10dp">

            <LinearLayout
                android:id="@+id/sms_option"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="?attr/selectableItemBackground"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="11dp">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_icon_sms" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/sms"
                    android:textColor="@color/grey800"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/email_option"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="11dp">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_icon_email" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/email"
                    android:textColor="@color/grey800"
                    android:textSize="14sp" />
            </LinearLayout>

            <LinearLayout
                android:id="@+id/whatsapp_option"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="?attr/selectableItemBackground"
                android:gravity="center_vertical"
                android:orientation="horizontal"
                android:padding="11dp">

                <ImageView
                    android:layout_width="18dp"
                    android:layout_height="24dp"
                    android:src="@drawable/ic_icon_whatsapp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="12dp"
                    android:fontFamily="@font/inter_medium"
                    android:text="@string/whatsapp"
                    android:textColor="@color/grey800"
                    android:textSize="14sp" />
            </LinearLayout>

        </LinearLayout>

    </androidx.cardview.widget.CardView>


</FrameLayout>

