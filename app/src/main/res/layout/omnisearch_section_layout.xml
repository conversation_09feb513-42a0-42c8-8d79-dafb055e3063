<?xml version="1.0" encoding="utf-8"?>
<layout>

    <androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="wrap_content">

        <LinearLayout
            android:layout_marginStart="@dimen/margin_16"
            android:id="@+id/title_container"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content">

            <TextView
                android:id="@+id/title"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:fontFamily="@font/inter_medium"
                android:gravity="center_vertical"
                android:paddingTop="@dimen/margin_8"
                android:paddingBottom="@dimen/margin_8"
                android:textColor="@color/black"
                android:textSize="@dimen/text_size_16"
                tools:text="Guests" />

            <ImageView
                android:id="@+id/image_chevron"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_8"
                android:src="@drawable/ic_icon_arrow_down" />
        </LinearLayout>

        <!--Margin 11 is used to take into account list item background_shadow padding of 5dp-->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_items"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/margin_11"
            android:layout_marginTop="@dimen/margin_8"
            android:layout_marginEnd="@dimen/margin_11"
            android:layout_marginBottom="@dimen/margin_16"
            android:nestedScrollingEnabled="false"
            app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title_container" />


    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>