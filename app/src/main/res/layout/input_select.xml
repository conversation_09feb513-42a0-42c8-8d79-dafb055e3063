<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <variable
            name="key"
            type="String" />

        <variable
            name="hint"
            type="String" />

        <variable
            name="icon"
            type="int" />

        <variable
            name="value"
            type="String" />

        <variable
            name="disabled"
            type="Boolean" />

        <variable
            name="hideSeparator"
            type="Boolean" />

        <variable
            name="keyLabelCaps"
            type="Boolean" />

        <variable
            name="showIcon"
            type="Boolean" />

        <import type="android.view.View" />

        <import type="com.eatapp.clementine.R" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cont"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:animateLayoutChanges="true"
        android:background="@color/white"
        android:foreground="?attr/selectableItemBackground"
        android:gravity="bottom"
        android:orientation="vertical">

        <TextView
            android:id="@+id/textView19"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="@dimen/global_margin_16"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="@dimen/global_margin_16"
            android:fontFamily="@font/inter_regular"
            android:lines="1"
            android:text="@{key}"
            android:textAllCaps="@{keyLabelCaps}"
            android:textColor="@color/colorGrey200"
            android:textSize="@dimen/input_key_size"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="KEY" />

        <LinearLayout
            android:id="@+id/linearLayout3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:paddingStart="@dimen/input_padding_side"
            android:paddingEnd="@dimen/input_padding_side"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/textView19">

            <ImageView
                android:id="@+id/iconImg"
                android:layout_width="@dimen/global_icon_size_24"
                android:layout_height="@dimen/global_icon_size_24"
                android:layout_gravity="center_vertical"
                android:layout_marginEnd="6dp"
                android:visibility="@{showIcon ? View.VISIBLE : View.GONE }"
                app:icon="@{icon}"
                tools:layout_editor_absoluteX="-46dp"
                tools:layout_editor_absoluteY="-42dp"
                tools:srcCompat="@drawable/ic_icon_status_main"
                tools:visibility="visible" />

            <TextView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/input_spacing"
                android:layout_marginBottom="@dimen/input_padding_bottom"
                android:background="@android:color/transparent"
                android:ellipsize="end"
                android:ems="10"
                android:fontFamily="@font/inter_medium"
                android:hint="@{hint}"
                android:lines="1"
                android:text="@{value}"
                android:textColor="@{disabled ? @color/grey500 : @color/grey800}"
                android:textColorHint="@color/grey400"
                android:textSize="@dimen/input_value_size"
                tools:text="Wed 06, April" />

        </LinearLayout>

        <View
            android:id="@+id/disable"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:clickable="@{disabled}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <View
            android:id="@+id/separator"
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            android:visibility="@{hideSeparator ? View.INVISIBLE : View.VISIBLE}"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>

