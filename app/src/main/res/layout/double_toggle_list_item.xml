<layout xmlns:tools="http://schemas.android.com/tools"
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto">

    <data>

        <variable
            name="key1"
            type="String" />

        <variable
            name="key2"
            type="String" />

        <variable
            name="value1"
            type="Boolean" />

        <variable
            name="value2"
            type="Boolean" />

        <variable
            name="enabled"
            type="Boolean" />

    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:layout_width="match_parent"
        android:layout_height="50dp"
        android:background="@color/white"
        android:orientation="vertical">

        <LinearLayout
            android:id="@+id/container1"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/input_padding_side"
            android:paddingEnd="@dimen/input_padding_side"
            app:layout_constraintEnd_toStartOf="@+id/vertical_separator"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/title1"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/inter_medium"
                android:text="@{key1}"
                android:textColor="@color/grey800"
                android:textSize="15sp"
                tools:text="@string/walk_in" />

            <CheckBox
                android:id="@+id/cb_toggle1"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:checked="@{value1}"
                android:enabled="@{enabled}"
                tools:src="@drawable/ic_icon_radio_on"
                tools:tint="@color/colorPrimary" />

        </LinearLayout>

        <LinearLayout
            android:id="@+id/container2"
            android:layout_width="0dp"
            android:layout_height="match_parent"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            android:paddingStart="@dimen/input_padding_side"
            android:paddingEnd="@dimen/input_padding_side"
            app:layout_constraintStart_toEndOf="@+id/vertical_separator"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <TextView
                android:id="@+id/title2"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:fontFamily="@font/inter_medium"
                android:text="@{key2}"
                android:textColor="@color/grey800"
                android:textSize="15sp"
                tools:text="@string/waitlist" />

            <CheckBox
                android:id="@+id/cb_toggle2"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="16dp"
                android:checked="@{value2}"
                android:enabled="@{enabled}"
                tools:src="@drawable/ic_icon_radio_on"
                tools:tint="@color/colorPrimary" />

        </LinearLayout>

        <View
            android:id="@+id/separator"
            android:layout_width="0dp"
            android:layout_height="1dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent" />

        <View
            android:id="@+id/vertical_separator"
            android:layout_width="1dp"
            android:layout_height="0dp"
            android:background="@color/colorSeparator"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    </androidx.constraintlayout.widget.ConstraintLayout>
</layout>
