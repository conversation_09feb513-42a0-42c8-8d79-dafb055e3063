<?xml version="1.0" encoding="utf-8"?>
<layout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools">

    <data>

        <import type="com.eatapp.clementine.R" />
    </data>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/mainBcg"
        tools:context=".ui.home.HomeActivity">

        <fragment
            android:id="@+id/nav_host_fragment"
            android:name="androidx.navigation.fragment.NavHostFragment"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginStart="72dp"
            app:defaultNavHost="true"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:navGraph="@navigation/home_nav_graph" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/container_navigation"
            android:layout_width="72dp"
            android:layout_height="0dp"
            android:background="@color/grey800"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ImageView
                android:id="@+id/btn_menu"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/margin_24"
                android:layout_marginTop="@dimen/margin_16"
                android:layout_marginEnd="@dimen/margin_24"
                android:importantForAccessibility="no"
                android:src="@drawable/ic_icon_menu"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:tint="@color/white" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/btn_reserve"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="@dimen/margin_12"
                android:layout_marginTop="@dimen/margin_20"
                android:background="@drawable/shape_reserve_btn_bcg_orange"
                android:orientation="vertical"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btn_menu">

                <ImageView
                    android:id="@+id/image_plus"
                    android:layout_width="24dp"
                    android:layout_height="24dp"
                    android:layout_marginStart="@dimen/margin_12"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:srcCompat="@drawable/ic_icon_plus_green"
                    app:tint="@color/black" />

                <TextView
                    android:id="@+id/tv_reserve"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="@dimen/margin_4"
                    android:layout_marginEnd="@dimen/margin_16"
                    android:fontFamily="@font/inter_semibold"
                    android:text="@string/nav_item_reserve"
                    android:maxLines="1"
                    android:textColor="@color/black"
                    android:textSize="@dimen/text_size_14"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toEndOf="@id/image_plus"
                    app:layout_constraintTop_toTopOf="parent" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_navigation_categories"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:layout_marginStart="@dimen/margin_12"
                android:layout_marginTop="@dimen/margin_16"
                android:layout_marginEnd="@dimen/margin_12"
                android:layout_marginBottom="108dp"
                app:layoutManager="androidx.recyclerview.widget.LinearLayoutManager"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@id/btn_reserve" />

            <com.eatapp.clementine.views.TabBarVenueView
                android:id="@+id/container_venue_view"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:layout_constraintBottom_toTopOf="@id/container_logo" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/container_logo"
                android:layout_width="match_parent"
                android:layout_height="20dp"
                android:layout_marginStart="@dimen/margin_16"
                android:layout_marginEnd="@dimen/margin_16"
                android:layout_marginBottom="@dimen/margin_16"
                android:background="@color/grey800"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent">

                <ImageView
                    android:layout_width="40dp"
                    android:layout_height="0dp"
                    android:src="@drawable/ic_logo"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    app:tint="@color/white" />

                <LinearLayout
                    android:id="@+id/container_logout"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:visibility="gone"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintTop_toTopOf="parent">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:fontFamily="@font/inter_semibold"
                        android:text="@string/log_out"
                        android:textColor="@color/white"
                        android:textSize="@dimen/text_size_14" />

                    <ImageView
                        android:id="@+id/iv_logout"
                        android:layout_width="24dp"
                        android:layout_height="24dp"
                        android:src="@drawable/ic_navigation_logout"
                        app:layout_constraintBottom_toBottomOf="parent"
                        app:layout_constraintEnd_toEndOf="parent"
                        app:layout_constraintTop_toTopOf="parent" />

                </LinearLayout>
            </androidx.constraintlayout.widget.ConstraintLayout>

        </androidx.constraintlayout.widget.ConstraintLayout>

        <LinearLayout
            android:id="@+id/container_progress"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:background="@color/transparent"
            android:clickable="true"
            android:focusable="true"
            android:gravity="center"
            android:orientation="vertical"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

            <ProgressBar
                android:id="@+id/progressBar"
                style="?android:attr/progressBarStyle"
                android:layout_width="@dimen/progress_size"
                android:layout_height="@dimen/progress_size"
                android:indeterminateTint="@color/white" />

            <TextView
                android:id="@+id/progressBarText"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/margin_16"
                android:fontFamily="@font/inter_medium"
                android:text="@string/switching_restaurant_label"
                android:textColor="@color/white" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</layout>