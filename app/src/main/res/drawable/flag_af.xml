<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:strokeWidth="1"
      android:fillColor="#000001"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M426.7,0H640v480H426.7z"
      android:strokeWidth="1"
      android:fillColor="#090"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M213.3,0h213.4v480H213.3z"
      android:strokeWidth="1"
      android:fillColor="#bf0000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M340.78,267.43l8.83,0c0,3.4 2.13,7.02 4.79,9.04l-17.02,0c2.66,-2.34 3.4,-5.32 3.4,-9.04z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="m284.62,217.13 l4.89,5.32 60.62,0.21 4.89,-5.32 -15.53,-0.32 -7.44,-5.32l-24.46,0l-7.02,5.42z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M309.4,210.96l20.95,0c2.76,-1.49 3.72,-6.27 3.72,-8.93 0,-7.87 -5.64,-11.7 -11.17,-11.91q-1.49,-0.11 -2.02,-1.38c-0.53,-1.7 -0.43,-2.87 -1.06,-2.76 -0.43,0 -0.32,1.06 -0.74,2.55q-0.64,1.38 -2.13,1.7c-6.81,0.32 -11.27,5.32 -11.17,11.8 0.11,4.25 0.64,6.81 3.62,8.93z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M275.05,285.51L364.7,285.51l-7.98,-6.49l-73.8,0z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m316.21,260.94 l1.6,4.89l3.72,0l-2.98,-4.89zM314.08,265.83 L315.15,270.73l4.25,0l-1.6,-4.89zM321.53,265.83 L324.5,270.73l6.27,0l-4.89,-4.89zM284.84,276.89c3.3,-3.08 5.42,-5.64 5.42,-9.36l8.08,0q0,3.19 1.91,3.19l8.19,0l0,-4.79l-5.96,0l0,-26.27c-0.21,-9.36 11.27,-14.68 15.95,-14.68l-27.97,0l0,-0.85l58.81,0l0,0.85L321.1,225c8.4,0 16.48,7.98 16.59,14.68l0,7.44l-1.06,0l-0.11,-7.34c0,-7.34 -9.25,-14.14 -16.7,-13.93 -6.38,0.11 -16.38,6.27 -16.27,13.82l0,2.34l15.21,0.11 -0.11,2.66 2.34,1.49 4.79,1.49l0,4.04l3.4,0.96l0,3.93l4.04,1.81l0,4.04l2.66,1.6 -0.11,4.15 3.51,2.45l-8.29,0l5.21,5.85l-7.76,0l-3.83,-5.85l-5,0l2.23,5.74l-5.32,0l-1.38,-5.74l-6.59,0l0,6.17L284.94,276.89zM308.45,260.94l0,4.89l5.64,0l-1.06,-4.89L308.34,260.94z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M308.77,252.43l3.51,0l0,8.08l-3.51,0z"
      android:strokeWidth="0.53"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M303.77,261.05l3.4,0l0,-5.96c0,-2.55 2.34,-5.21 3.4,-5.32 1.28,0 3.08,2.45 3.19,5.1l0,6.17l3.62,0l0,-15.31l-13.61,0zM330.99,264.56l4.25,0l0,3.4l-4.25,0zM328.44,258.92l4.25,0l0,3.3l-4.25,0zM324.29,253.18l4.25,0l0,3.3l-4.25,0zM320.78,248.39l4.25,0l0,3.3l-4.25,0z"
      android:strokeWidth="0.53"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="m317.91,261.05 l4.47,0.21 7.76,6.81l0,-4.04l-2.66,-1.91l0,-3.19l-3.83,-2.13l0,-3.51l-3.72,-1.28L319.93,247.44l-1.81,-1.6z"
      android:strokeWidth="0.53"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M336.42,250.95l1.06,0l0,7.55l-1.06,0z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M274.63,225.64c-1.28,-2.13 -2.87,2.98 -8.29,6.7 -2.45,1.7 -4.25,6.27 -4.25,9.25q0.11,3.19 0,6.17c-0.11,1.17 -1.49,4.04 -0.53,4.79 2.34,1.7 5.42,5.74 6.81,7.13 1.28,1.06 2.34,-5.64 3.19,-8.51 1.06,-3.19 0.64,-7.13 3.4,-10 1.91,-2.13 6.81,-4.04 6.38,-4.89z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M274.31,242.01a10.63,10.63 0,0 0,-1.7 -2.76,6.38 6.38,73.75 0,0 -2.55,-1.91 5.32,5.32 0,0 1,-2.55 -1.6,4.25 4.25,64.67 0,1 -0.85,-1.6l0,-2.13l-0.32,0.32c-2.45,1.7 -4.25,6.27 -4.25,9.25l0,2.45q0.21,0.85 0.64,1.38l1.17,0.85 2.87,0.74a7.44,7.44 100.53,0 1,2.76 2.13,11.7 11.7,0 0,1 1.91,2.76l0.21,-0.85c0.85,-2.87 0.74,-6.27 2.76,-9.04z"
      android:strokeWidth="0.53"
      android:fillColor="#bf0000"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M266.65,231.91c-0.53,3.51 1.49,4.79 3.4,5.42 1.91,0.74 3.51,2.76 4.25,4.68m-12.44,1.6c0.85,3.19 2.98,2.76 4.89,3.4s3.93,3.19 4.79,5.1"
      android:strokeWidth="0.53"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="m272.82,223.51 l1.06,-0.64 18.82,31.8 -1.06,0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M274.84,222.23a2.13,2.13 0,1 1,-4.25 0,2.13 2.13,0 1,1 4.25,0zM291.01,196.71l7.66,0l0,1.7l-7.66,0zM291.01,200.01l7.66,0l0,14.68l-7.66,0zM290.58,194.69l8.51,0c0.21,-2.87 -2.66,-5.96 -4.25,-5.96 -1.7,0.11 -4.36,3.19 -4.25,5.96z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M312.17,192.99c-1.6,0.64 -2.87,2.45 -3.62,4.57s-1.06,4.57 -0.64,6.49q0.11,1.06 0.53,1.6 0.32,0.53 0.64,0.53 0.53,0 0.74,-0.32l0.21,-0.85q-0.21,-3.19 0.32,-5.74a8.51,8.51 0,0 1,3.19 -4.68q0.43,-0.32 0.53,-0.74l-0.32,-0.74q-0.74,-0.53 -1.6,-0.11m0.21,0.43q0.64,-0.21 1.06,0.11l0.11,0.21q0,0.11 -0.32,0.43a8.51,8.51 0,0 0,-3.3 4.89,18.08 18.08,0 0,0 -0.32,5.96l-0.21,0.64s0,0.11 -0.21,0q0,0 -0.43,-0.32l-0.43,-1.28q-0.43,-3.08 0.74,-6.38 1.17,-3.08 3.19,-4.25z"
      android:strokeWidth="0.53"
      android:fillColor="#bd6b00"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M314.93,195.01q-2.45,1.28 -3.19,4.47a14.89,14.89 0,0 0,-0.32 6.27q0.53,2.13 1.7,2.13 0.53,0.21 0.85,-0.32t0.21,-1.06q-0.64,-2.66 -0.32,-5.42 0.43,-2.76 2.34,-4.36 0.53,-0.43 0.53,-0.85l-0.21,-0.64q-0.74,-0.53 -1.6,-0.21m0.21,0.53q0.64,-0.21 1.06,0l0.11,0.32 -0.32,0.43a7.44,7.44 0,0 0,-2.55 4.68q-0.43,2.98 0.21,5.53 0,0.64 0,0.85l-0.53,0.11c-0.32,0 -1.06,-0.53 -1.28,-1.81 -0.32,-1.81 -0.21,-4.15 0.32,-6.06q0.85,-3.08 2.98,-4.04"
      android:strokeWidth="0.53"
      android:fillColor="#bd6b00"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M290.58,226.59l8.51,0l0,11.7l-8.51,0zM291.11,245.1l8.19,0l0,2.55l-8.19,0zM290.9,249.46l8.51,0l0,9.25l-8.51,0zM290.26,260.62l9.25,0l0,5.21L290.26,265.83zM291.43,242.97l7.44,0l1.49,-2.55l-10.21,0zM301.43,233.82 L301.53,227.44l5.1,0a18.08,18.08 0,0 0,-5.21 6.38z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M291.96,236.48c0,1.38 1.6,0.85 1.6,0.11l0,-5.96c0,-1.06 2.55,-0.85 2.55,-0.11l0,6.38c0,1.06 1.81,0.96 1.7,0l0,-7.44c0,-2.34 -5.85,-2.23 -5.85,-0.11zM291.96,250.63l6.06,0l0,7.44l-6.06,0z"
      android:strokeWidth="0.53"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M295.79,253.82l2.13,0l0,1.06l-2.13,0zM292.07,253.82l2.13,0l0,1.06l-2.13,0zM294.2,250.63l1.6,0l0,3.19l-1.6,0zM294.2,254.88l1.6,0l0,3.3l-1.6,0zM260.48,175.12c0.43,5.85 -1.49,9.15 -4.57,8.61 -0.85,-3.19 1.06,-5.42 4.57,-8.61zM253.57,188.2c-2.76,-1.38 -0.74,-12.23 0.32,-16.8 0.74,5.85 2.13,14.14 -0.32,16.8z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M254.53,188.73c4.68,1.6 8.51,-3.4 9.68,-9.25 -3.83,5.32 -10.1,5.32 -9.57,9.25zM251.02,194.16c-3.62,-0.96 -1.49,-12.44 -0.74,-17.02 0.74,4.79 3.3,15.42 0.74,17.02zM252.3,193.84c0.21,-3.93 4.15,-2.87 6.91,-5 -0.53,2.13 -2.13,5.53 -6.91,5zM247.83,199.16c-3.62,-1.06 -1.49,-13.4 -1.7,-18.5 1.06,4.47 4.47,17.33 1.7,18.5zM249.53,198.62c2.98,0.96 6.91,-1.06 7.23,-4.57 -2.66,1.81 -6.7,0.43 -7.23,4.57z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M245.06,204.58c-3.4,0.32 -1.91,-10.21 -1.91,-19.99 1.28,9.15 4.79,17.55 1.91,19.99z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M246.34,204.15c2.34,1.06 6.49,-0.74 7.66,-4.68 -4.25,1.81 -7.02,0 -7.66,4.68zM273.56,180.54c-0.64,5.21 -2.76,8.19 -5.85,7.66 -0.85,-3.19 1.7,-5.32 5.85,-7.66zM265.27,193.73c5.21,0.74 7.02,-3.19 10.63,-8.4 -5,3.62 -10.85,4.25 -10.63,8.51z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M263.67,193.2c-2.76,-3.4 0,-7.76 2.13,-11.38 -0.43,5.42 1.38,8.51 -2.13,11.38zM262.61,198.84c-0.43,-3.4 5.32,-4.15 7.87,-5.96 -0.96,1.91 -2.13,7.13 -7.98,5.96z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M261.34,198.84c-3.93,-0.43 -2.34,-7.13 0.53,-10.74 -1.17,5.1 2.13,8.61 -0.53,10.74z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M258.36,204.47c-4.47,-2.13 -1.6,-7.66 0,-10.95 -0.64,4.36 2.98,7.66 0,10.85z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M259.21,203.83c2.34,3.19 6.91,-0.85 7.87,-5.53 -3.93,3.3 -6.91,2.76 -7.87,5.64zM249,225.42c-0.43,-4.57 2.98,-12.76 0.53,-17.23 -0.32,-0.64 0.74,-2.23 1.49,-1.28 1.06,1.6 2.13,6.06 2.66,4.36s0.53,-4.89 2.13,-5.53c1.06,-0.32 2.45,-0.64 2.02,1.06 -0.43,1.49 -1.28,3.62 -0.32,3.72 0.53,0 2.13,-2.13 3.51,-3.19 1.06,-0.85 2.76,0.64 1.06,1.91 -5.1,4.25 -10.1,6.27 -13.08,16.16zM239.75,294.02c-0.64,0 -1.38,-0.32 -0.64,0.64 6.06,7.44 7.76,9.57 16.59,8.51 8.83,-1.17 10.95,-3.62 17.23,-7.13a15.95,15.95 0,0 1,11.91 -1.06c1.7,0.53 2.76,0.53 1.49,-0.74s-2.66,-2.87 -4.25,-4.04a19.14,19.14 74.73,0 0,-13.51 -2.87c-6.38,1.06 -11.8,5.21 -18.29,6.81a26.59,26.59 0,0 1,-10.53 0zM290.58,307.31c1.06,0.21 1.81,2.34 2.45,0.96 0.85,-2.45 0.21,-4.25 -0.85,-4.15 -1.28,0.32 -3.3,3.19 -1.6,3.19z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M235.6,221.91q-1.91,-2.13 1.06,-2.02c1.49,0 4.47,1.06 5.64,0.11 1.06,-0.74 0.53,-3.93 1.06,-5.32 0.21,-0.96 0.74,-2.13 2.13,-0.21 3.83,6.17 8.51,13.61 10.63,20.84 1.06,4.04 0,10.42 -3.62,14.68 0,-3.62 -1.28,-6.06 -2.87,-9.15 -2.13,-3.93 -9.68,-14.89 -14.04,-19.04z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M251.44,254.24c4.25,0 5,-5.64 5,-7.23 -2.13,0.43 -5.74,3.93 -5,7.23zM288.13,309.44c2.98,0.64 2.87,-6.59 -0.21,-9.68 1.38,4.68 -2.13,8.93 0.11,9.57zM286.86,309.33c0.21,3.4 -8.51,-0.43 -10.63,-3.19 5.1,2.23 10.42,0.43 10.63,3.19zM283.14,304.44c0.32,3.3 -7.44,0.32 -9.89,-2.23 5.21,1.7 9.57,-0.53 9.89,2.13zM284.52,304.86c3.08,0.74 2.55,-6.81 -0.43,-9.36 1.49,5 -1.91,8.61 0.43,9.36zM281.33,300.29c3.08,0.74 1.28,-5.74 -0.96,-8.29 0.43,4.68 -1.06,7.98 1.06,8.29zM279.73,300.29c0.32,3.4 -5.74,0.85 -8.08,-2.45 5.1,1.6 7.76,-0.32 8.08,2.45zM278.14,297.63c1.91,-1.38 -0.11,-5.1 -3.93,-4.89 0.43,2.23 1.7,6.27 3.93,4.89zM293.03,313.26c0.11,3.4 -8.51,1.7 -11.27,-1.91 5.53,1.06 10.95,-0.85 11.17,1.91zM258.57,307.1c0.32,3.4 -9.15,-0.43 -11.49,-3.62 5,1.7 11.17,0.85 11.49,3.62zM264.31,308.48c2.02,-1.38 -2.02,-5 -5.32,-5.85 0.43,2.23 3.19,7.23 5.32,5.96zM264.95,310.92c0.21,3.08 -10.1,1.38 -12.76,-1.49 8.83,1.6 12.44,-1.17 12.76,1.49z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M269.84,312.95c1.06,2.87 -8.83,2.13 -12.34,0.53 5.64,0 11.49,-2.55 12.34,-0.53z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M274.42,315.07c1.06,2.55 -8.08,2.55 -12.55,1.06 5.96,0 11.49,-3.62 12.55,-1.06zM281.12,316.45c1.7,3.08 -8.08,3.3 -11.17,1.81 5.53,-0.74 9.78,-4.25 11.17,-1.81zM269.74,311.24c-3.08,1.91 -2.87,-3.83 -5.32,-7.76 3.83,3.51 7.44,5.96 5.32,7.76z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M275.27,313.37c-2.55,2.23 -4.68,-5.64 -7.02,-10.1 3.83,4.25 9.36,8.19 7.02,10zM282.5,315.5c-2.13,2.55 -8.51,-7.44 -10.85,-12.76 3.51,4.15 12.55,10.63 10.85,12.76zM276.33,323.15c-1.06,3.83 -17.23,-3.62 -19.14,-7.55 9.36,4.89 19.35,3.83 19.14,7.44zM224.54,244.67c-0.43,-0.53 -1.49,0 -1.28,1.17 0.32,1.6 2.66,9.78 6.7,12.55 2.87,2.13 18.08,5.42 24.88,6.91q5.64,1.06 9.46,5.64a99.97,99.97 0,0 0,-3.19 -10.42c-1.28,-3.19 -4.68,-6.59 -8.29,-6.7 -6.49,-0.32 -14.99,-0.85 -21.27,-3.51a17.02,17.02 0,0 1,-7.13 -5.64z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M262.08,277.11c2.13,1.49 4.36,-3.93 1.81,-9.15 -0.11,5 -4.04,6.7 -1.81,9.15z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M264.1,282.11c2.87,0.85 3.72,-4.25 1.91,-8.29 0.32,4.36 -4.57,7.02 -1.91,8.29z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M266.33,286.15c2.76,1.38 3.72,-3.83 1.81,-7.55 0.21,4.79 -3.93,6.27 -1.81,7.44z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M265.16,286.47c-1.06,3.19 -7.44,-2.87 -8.51,-6.17 3.93,3.93 9.25,3.4 8.51,6.06z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M262.29,281.47c-1.28,3.19 -9.25,-5.32 -11.06,-9.25 3.93,3.93 11.91,6.91 11.06,9.15z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M260.7,276.47c-1.28,3.72 -9.89,-6.17 -12.44,-9.68 4.25,3.83 13.4,7.02 12.44,9.68zM260.38,272.85c3.19,-0.64 -0.11,-3.19 -3.93,-7.34 -0.11,4.36 0.53,7.44 3.93,7.34z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M255.17,270.3c1.38,-1.38 -1.17,-2.02 -4.36,-5.64 -0.53,2.45 2.98,6.91 4.47,5.64zM270.06,286.47c1.7,1.06 2.76,-2.45 0.74,-5.53 -0.53,3.4 -2.23,4.25 -0.74,5.53zM233.69,264.88c-3.51,2.13 -9.15,-6.38 -10.63,-9.89 3.08,4.04 11.27,7.66 10.63,9.89z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M236.77,269.77c-2.02,2.13 -8.19,-3.72 -10.32,-6.7 3.19,2.87 11.17,3.19 10.32,6.7z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M240.07,274.24c-0.64,2.98 -9.57,-3.72 -11.7,-6.91 3.83,3.72 12.34,3.4 11.7,6.91z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M238.68,277.53c-1.38,2.66 -8.72,-4.04 -10.53,-7.44 4.57,3.83 11.7,4.79 10.63,7.44zM234.96,264.45c2.23,-2.45 1.28,-3.62 -0.43,-7.44 -0.85,3.93 -2.23,5.53 0.43,7.44zM238.05,269.02c4.25,0.21 0,-4.89 -1.06,-9.25 0.43,4.89 -1.06,8.83 1.06,9.25z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M240.7,273.07c2.87,-0.64 2.13,-4.79 -0.21,-9.78 0.53,5.42 -2.45,8.51 0.21,9.78zM239.64,281.25c-1.06,3.19 -9.36,-4.25 -10.63,-7.23 4.25,3.62 11.38,4.79 10.63,7.23z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M244.64,286.36c-1.17,3.19 -9.89,-3.4 -12.55,-7.02 5.21,4.25 13.19,3.83 12.55,7.02z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M249.74,291.57c-1.38,4.57 -10.53,-2.76 -13.19,-6.38 5.74,4.47 13.82,3.19 13.19,6.38zM241.24,279.87c2.45,1.17 3.4,-5.74 2.02,-10.74 0,5.32 -5,9.36 -2.13,10.63z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M245.38,285.4c2.98,0.85 2.13,-6.7 -0.53,-11.7 -0.32,5 -2.45,9.57 0.53,11.7zM250.7,290.61c3.19,0.11 1.06,-6.49 -1.7,-10.21 0.43,4.79 -1.06,9.57 1.7,10.21zM244.85,293.38c-1.06,1.7 -3.4,-1.38 -7.44,-3.72 3.62,1.06 7.87,2.13 7.44,3.72zM242.94,237.33c3.19,-2.34 0.74,-6.59 0,-10.63 -1.06,3.83 -3.62,8.93 0,10.63zM242.94,242.97c-4.79,-0.53 -4.04,-6.49 -4.25,-10.32 1.49,5.21 5.32,6.06 4.25,10.42zM243.58,242.23c3.93,-0.21 3.72,-4.68 3.93,-9.15 -2.02,4.15 -4.25,4.79 -3.93,9.15z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M243.47,247.76c-3.19,0.32 -4.68,-2.76 -5.32,-7.44 2.87,4.36 5.42,2.98 5.32,7.44zM244.53,247.44c3.93,0.53 3.19,-4.04 3.19,-7.44 -1.28,3.19 -4.47,4.25 -3.19,7.44z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M238.36,245.52c0.32,2.98 2.23,8.08 5.32,6.91 1.17,-3.62 -2.76,-4.36 -5.32,-6.91z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M244.53,252.75c-1.28,-2.55 3.19,-3.93 4.04,-7.34 0.53,4.89 0.11,8.08 -4.04,7.44zM231.88,221.7c2.45,-2.55 0.32,-6.81 -0.43,-10.85 -1.06,3.83 -2.66,8.93 0.43,10.85zM231.88,226.59c-4.25,0.53 -5.32,-8.19 -5.85,-12.02 1.49,5.21 6.38,7.44 5.85,12.12zM232.73,226.59c2.98,-1.6 2.34,-5 3.19,-7.44 -1.91,3.08 -3.83,3.51 -3.19,7.44z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M231.77,232.34c-4.36,0.32 -7.02,-9.36 -7.23,-13.19 1.38,5.21 7.87,7.98 7.34,13.19zM232.73,232.12c4.25,-0.96 3.72,-3.72 3.08,-8.08 -1.38,4.47 -3.72,3.51 -3.08,8.08z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M231.77,237.87c-4.89,0.85 -4.57,-7.02 -8.51,-12.66 3.4,4.25 9.57,9.57 8.51,12.66zM232.83,237.55c3.83,0.21 4.25,-5.42 4.04,-7.76 -0.96,2.34 -5.32,4.47 -3.93,7.87z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M224.11,231.8c1.81,6.06 4.47,12.12 7.66,11.7 1.6,-3.51 -3.08,-3.93 -7.66,-11.7z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M232.94,242.54c-1.28,-2.66 3.19,-3.93 4.04,-7.34 0.53,4.89 0.11,8.08 -4.04,7.34zM225.39,238.72c2.66,5.42 3.83,11.7 7.44,10.74 1.38,-4.25 -4.04,-5.1 -7.44,-10.74z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M233.58,248.5c-1.6,-2.98 2.87,-3.93 4.04,-7.87 0.53,5.1 0,8.83 -4.04,7.76zM241.24,211.81c2.55,0.64 5.32,-2.23 4.36,-6.59 -2.98,0.64 -4.25,3.4 -4.36,6.59zM232.83,209.58c0.21,1.28 1.81,1.38 1.28,-0.43a5.32,5.32 0,0 1,0 -3.62,8.51 8.51,0 0,0 0,-4.89c-0.43,-1.06 -1.91,-0.43 -1.28,0.43s0.74,2.98 0.21,3.93q-0.74,2.34 -0.21,4.57zM257.19,226.59c-1.06,1.38 -3.08,0.43 -1.49,-1.6 1.28,-1.6 3.19,-2.98 3.19,-4.68 0.21,-2.13 1.38,-5.32 2.55,-6.49s2.55,0.43 1.28,1.28c-1.38,0.85 -2.34,4.68 -2.23,6.17 -0.11,2.13 -2.13,3.72 -3.3,5.32zM254,224.15c-1.06,1.49 -2.55,0.53 -1.7,-1.81 0.74,-1.6 0.85,-3.72 1.7,-4.89 1.28,-1.81 3.19,-3.3 4.36,-4.47 1.28,-1.06 2.13,0 1.06,1.06a28.71,28.71 128.87,0 0,-3.51 4.25c-1.49,2.34 -0.85,4.25 -1.91,5.85zM237.3,216.49c-0.11,2.13 1.6,2.55 1.49,-0.43 0,-3.19 -2.34,-6.17 -1.06,-10.95 0.85,-2.34 0.85,-6.7 0.43,-8.93s-2.13,-0.85 -1.38,0.96c0.64,2.13 -0.11,5.96 -0.64,7.98 -1.6,5.74 1.28,8.51 1.06,11.38zM241.87,204.79c-0.21,2.02 -1.91,2.13 -1.38,-0.53q0.64,-3.08 0,-5.64c-0.64,-2.23 -0.43,-6.06 0,-7.66 0.53,-1.7 2.13,-0.74 1.49,0.53a10.63,10.63 0,0 0,-0.32 6.27c0.64,2.13 0.53,5.1 0.21,7.13zM225.28,244.25c0.85,0.96 2.13,0.32 1.06,-1.06 -1.06,-1.06 -0.74,-1.28 -1.38,-2.55 -0.64,-1.49 -0.53,-2.23 -1.28,-3.19 -0.74,-1.06 -1.7,0 -1.06,0.74 0.85,1.06 0.64,1.7 1.06,2.66 1.06,1.6 0.74,2.45 1.6,3.4zM246.98,270.41a9.57,9.57 0,0 1,4.68 7.13,17.02 17.02,78.48 0,0 2.13,7.55c-2.13,-0.53 -3.19,-3.93 -3.51,-7.23 -0.32,-3.4 -2.13,-4.79 -3.19,-7.44zM252.4,276.68c1.81,3.3 4.25,4.57 4.47,7.02 0.21,2.87 0.43,2.98 1.17,5.74 -2.13,-0.53 -2.66,-0.74 -3.19,-5 -0.32,-2.98 -2.76,-5 -2.45,-7.76z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M308.34,307.31c1.06,1.91 2.13,4.79 4.25,4.25 0,-1.38 -2.23,-2.45 -4.25,-4.25m3.19,0.64c3.93,1.7 7.44,1.28 7.98,3.83 -3.83,0.43 -5.32,-1.06 -8.08,-3.83zM294.41,294.44a14.89,14.89 0,0 1,5.32 8.19,30.84 30.84,122.87 0,0 3.83,8.29 13.82,13.82 0,0 1,-5.64 -7.87c-0.74,-3.19 -1.7,-5.64 -3.51,-8.51zM297.71,294.44c2.98,2.34 5.74,5.1 6.59,8.4 0.85,3.08 1.38,5.42 3.4,8.51 -3.19,-2.02 -4.36,-5 -5.32,-8.29 -0.74,-3.19 -2.66,-5.53 -4.68,-8.51zM307.49,302.2a1.06,1.06 0,0 1,0.74 -1.28l2.76,-0.85c1.06,-0.32 1.7,0.43 1.7,0.96l0,2.13q0,0.96 -0.74,0.96 -1.28,0 -2.55,0.74 -1.06,0.53 -1.6,-0.53zM318.76,302.2q0,-1.06 -0.64,-1.28a5.32,5.32 0,0 0,-2.55 -0.43q-1.38,0 -1.17,0.64l0,2.23c0,0.85 0,0.85 0.43,1.06q1.38,-0.11 2.66,0.64 0.96,0.43 1.17,-0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M365.45,225.64c1.28,-2.13 2.87,2.98 8.29,6.7 2.45,1.7 4.25,6.27 4.25,9.25q-0.11,3.19 -0,6.17c0.11,1.17 1.49,4.04 0.53,4.79 -2.34,1.7 -5.42,5.74 -6.81,7.13 -1.28,1.06 -2.34,-5.64 -3.19,-8.51 -1.06,-3.19 -0.64,-7.13 -3.4,-10 -1.91,-2.13 -6.81,-4.04 -6.38,-4.89z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M365.77,242.01a10.63,10.63 0,0 1,1.7 -2.76,6.38 6.38,0 0,1 2.55,-1.91 5.32,5.32 0,0 0,2.55 -1.6,4.25 4.25,0 0,0 0.85,-1.6l-0,-2.13l0.32,0.32c2.45,1.7 4.25,6.27 4.25,9.25l-0,2.45q-0.21,0.85 -0.64,1.38l-1.17,0.85 -2.87,0.74a7.44,7.44 53.79,0 0,-2.76 2.13,11.7 11.7,0 0,0 -1.91,2.76l-0.21,-0.85c-0.85,-2.87 -0.74,-6.27 -2.76,-9.04z"
      android:strokeWidth="0.53"
      android:fillColor="#bf0000"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M373.42,231.91c0.53,3.51 -1.49,4.79 -3.4,5.42 -1.91,0.74 -3.51,2.76 -4.25,4.68m12.44,1.6c-0.85,3.19 -2.98,2.76 -4.89,3.4s-3.93,3.19 -4.79,5.1"
      android:strokeWidth="0.53"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="m367.26,223.51l-1.06,-0.64 -18.82,31.8 1.06,0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M365.24,222.23a2.13,2.13 0,1 0,4.25 0,2.13 2.13,0 1,0 -4.25,0zM349.07,196.71l-7.66,0l-0,1.7l7.66,0zM349.07,200.01l-7.66,0l-0,14.68l7.66,0zM349.5,194.69l-8.51,0c-0.21,-2.87 2.66,-5.96 4.25,-5.96 1.7,0.11 4.36,3.19 4.25,5.96z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M327.91,192.99c1.6,0.64 2.87,2.45 3.62,4.57s1.06,4.57 0.64,6.49q-0.11,1.06 -0.53,1.6 -0.32,0.53 -0.64,0.53 -0.53,0 -0.74,-0.32l-0.21,-0.85q0.21,-3.19 -0.32,-5.74a8.51,8.51 0,0 0,-3.19 -4.68q-0.43,-0.32 -0.53,-0.74l0.32,-0.74q0.74,-0.53 1.6,-0.11m-0.21,0.43q-0.64,-0.21 -1.06,0.11l-0.11,0.21q-0,0.11 0.32,0.43a8.51,8.51 0,0 1,3.3 4.89,18.08 18.08,0 0,1 0.32,5.96l0.21,0.64s-0,0.11 0.21,0q-0,0 0.43,-0.32l0.43,-1.28q0.43,-3.08 -0.74,-6.38 -1.17,-3.08 -3.19,-4.25z"
      android:strokeWidth="0.53"
      android:fillColor="#bd6b00"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M325.14,195.01q2.45,1.28 3.19,4.47a14.89,14.89 0,0 1,0.32 6.27q-0.53,2.13 -1.7,2.13 -0.53,0.21 -0.85,-0.32t-0.21,-1.06q0.64,-2.66 0.32,-5.42 -0.43,-2.76 -2.34,-4.36 -0.53,-0.43 -0.53,-0.85l0.21,-0.64q0.74,-0.53 1.6,-0.21m-0.21,0.53q-0.64,-0.21 -1.06,0l-0.11,0.32 0.32,0.43a7.44,7.44 0,0 1,2.55 4.68q0.43,2.98 -0.21,5.53 -0,0.64 -0,0.85l0.53,0.11c0.32,0 1.06,-0.53 1.28,-1.81 0.32,-1.81 0.21,-4.15 -0.32,-6.06q-0.85,-3.08 -2.98,-4.04"
      android:strokeWidth="0.53"
      android:fillColor="#bd6b00"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M349.5,226.59l-8.51,0l-0,11.7l8.51,0zM348.96,245.1l-8.19,0l-0,2.55l8.19,0zM349.18,249.46l-8.51,0l-0,9.25l8.51,0zM349.81,260.62l-9.25,0l-0,5.21L349.81,265.83zM348.65,242.97l-7.44,0l-1.49,-2.55l10.21,0zM338.65,233.82L338.54,227.44l-5.1,0a18.08,18.08 0,0 1,5.21 6.38z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M348.11,236.48c-0,1.38 -1.6,0.85 -1.6,0.11l-0,-5.96c-0,-1.06 -2.55,-0.85 -2.55,-0.11l-0,6.38c-0,1.06 -1.81,0.96 -1.7,0l-0,-7.44c-0,-2.34 5.85,-2.23 5.85,-0.11zM348.11,250.63l-6.06,0l-0,7.44l6.06,0z"
      android:strokeWidth="0.53"
      android:fillColor="#00000000"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M344.28,253.82l-2.13,0l-0,1.06l2.13,0zM348.01,253.82l-2.13,0l-0,1.06l2.13,0zM345.88,250.63l-1.6,0l-0,3.19l1.6,0zM345.88,254.88l-1.6,0l-0,3.3l1.6,0zM379.59,175.12c-0.43,5.85 1.49,9.15 4.57,8.61 0.85,-3.19 -1.06,-5.42 -4.57,-8.61zM386.5,188.2c2.76,-1.38 0.74,-12.23 -0.32,-16.8 -0.74,5.85 -2.13,14.14 0.32,16.8z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M385.55,188.73c-4.68,1.6 -8.51,-3.4 -9.68,-9.25 3.83,5.32 10.1,5.32 9.57,9.25zM389.06,194.16c3.62,-0.96 1.49,-12.44 0.74,-17.02 -0.74,4.79 -3.3,15.42 -0.74,17.02zM387.78,193.84c-0.21,-3.93 -4.15,-2.87 -6.91,-5 0.53,2.13 2.13,5.53 6.91,5zM392.25,199.16c3.62,-1.06 1.49,-13.4 1.7,-18.5 -1.06,4.47 -4.47,17.33 -1.7,18.5zM390.55,198.62c-2.98,0.96 -6.91,-1.06 -7.23,-4.57 2.66,1.81 6.7,0.43 7.23,4.57z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M395.01,204.58c3.4,0.32 1.91,-10.21 1.91,-19.99 -1.28,9.15 -4.79,17.55 -1.91,19.99z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M393.74,204.15c-2.34,1.06 -6.49,-0.74 -7.66,-4.68 4.25,1.81 7.02,0 7.66,4.68zM366.51,180.54c0.64,5.21 2.76,8.19 5.85,7.66 0.85,-3.19 -1.7,-5.32 -5.85,-7.66zM374.81,193.73c-5.21,0.74 -7.02,-3.19 -10.63,-8.4 5,3.62 10.85,4.25 10.63,8.51z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M376.4,193.2c2.76,-3.4 -0,-7.76 -2.13,-11.38 0.43,5.42 -1.38,8.51 2.13,11.38zM377.46,198.84c0.43,-3.4 -5.32,-4.15 -7.87,-5.96 0.96,1.91 2.13,7.13 7.98,5.96z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M378.74,198.84c3.93,-0.43 2.34,-7.13 -0.53,-10.74 1.17,5.1 -2.13,8.61 0.53,10.74z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M381.72,204.47c4.47,-2.13 1.6,-7.66 -0,-10.95 0.64,4.36 -2.98,7.66 -0,10.85z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M380.87,203.83c-2.34,3.19 -6.91,-0.85 -7.87,-5.53 3.93,3.3 6.91,2.76 7.87,5.64zM391.08,225.42c0.43,-4.57 -2.98,-12.76 -0.53,-17.23 0.32,-0.64 -0.74,-2.23 -1.49,-1.28 -1.06,1.6 -2.13,6.06 -2.66,4.36s-0.53,-4.89 -2.13,-5.53c-1.06,-0.32 -2.45,-0.64 -2.02,1.06 0.43,1.49 1.28,3.62 0.32,3.72 -0.53,0 -2.13,-2.13 -3.51,-3.19 -1.06,-0.85 -2.76,0.64 -1.06,1.91 5.1,4.25 10.1,6.27 13.08,16.16zM400.33,294.02c0.64,0 1.38,-0.32 0.64,0.64 -6.06,7.44 -7.76,9.57 -16.59,8.51 -8.83,-1.17 -10.95,-3.62 -17.23,-7.13a15.95,15.95 0,0 0,-11.91 -1.06c-1.7,0.53 -2.76,0.53 -1.49,-0.74s2.66,-2.87 4.25,-4.04a19.14,19.14 0,0 1,13.51 -2.87c6.38,1.06 11.8,5.21 18.29,6.81a26.59,26.59 0,0 0,10.53 0zM349.5,307.31c-1.06,0.21 -1.81,2.34 -2.45,0.96 -0.85,-2.45 -0.21,-4.25 0.85,-4.15 1.28,0.32 3.3,3.19 1.6,3.19z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M404.48,221.91q1.91,-2.13 -1.06,-2.02c-1.49,0 -4.47,1.06 -5.64,0.11 -1.06,-0.74 -0.53,-3.93 -1.06,-5.32 -0.21,-0.96 -0.74,-2.13 -2.13,-0.21 -3.83,6.17 -8.51,13.61 -10.63,20.84 -1.06,4.04 -0,10.42 3.62,14.68 -0,-3.62 1.28,-6.06 2.87,-9.15 2.13,-3.93 9.68,-14.89 14.04,-19.04z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M388.63,254.24c-4.25,0 -5,-5.64 -5,-7.23 2.13,0.43 5.74,3.93 5,7.23zM351.94,309.44c-2.98,0.64 -2.87,-6.59 0.21,-9.68 -1.38,4.68 2.13,8.93 -0.11,9.57zM353.22,309.33c-0.21,3.4 8.51,-0.43 10.63,-3.19 -5.1,2.23 -10.42,0.43 -10.63,3.19zM356.94,304.44c-0.32,3.3 7.44,0.32 9.89,-2.23 -5.21,1.7 -9.57,-0.53 -9.89,2.13zM355.56,304.86c-3.08,0.74 -2.55,-6.81 0.43,-9.36 -1.49,5 1.91,8.61 -0.43,9.36zM358.75,300.29c-3.08,0.74 -1.28,-5.74 0.96,-8.29 -0.43,4.68 1.06,7.98 -1.06,8.29zM360.34,300.29c-0.32,3.4 5.74,0.85 8.08,-2.45 -5.1,1.6 -7.76,-0.32 -8.08,2.45zM361.94,297.63c-1.91,-1.38 0.11,-5.1 3.93,-4.89 -0.43,2.23 -1.7,6.27 -3.93,4.89zM347.05,313.26c-0.11,3.4 8.51,1.7 11.27,-1.91 -5.53,1.06 -10.95,-0.85 -11.17,1.91zM381.51,307.1c-0.32,3.4 9.15,-0.43 11.49,-3.62 -5,1.7 -11.17,0.85 -11.49,3.62zM375.76,308.48c-2.02,-1.38 2.02,-5 5.32,-5.85 -0.43,2.23 -3.19,7.23 -5.32,5.96zM375.13,310.92c-0.21,3.08 10.1,1.38 12.76,-1.49 -8.83,1.6 -12.44,-1.17 -12.76,1.49z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M370.23,312.95c-1.06,2.87 8.83,2.13 12.34,0.53 -5.64,0 -11.49,-2.55 -12.34,-0.53z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M365.66,315.07c-1.06,2.55 8.08,2.55 12.55,1.06 -5.96,0 -11.49,-3.62 -12.55,-1.06zM358.96,316.45c-1.7,3.08 8.08,3.3 11.17,1.81 -5.53,-0.74 -9.78,-4.25 -11.17,-1.81zM370.34,311.24c3.08,1.91 2.87,-3.83 5.32,-7.76 -3.83,3.51 -7.44,5.96 -5.32,7.76z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M364.81,313.37c2.55,2.23 4.68,-5.64 7.02,-10.1 -3.83,4.25 -9.36,8.19 -7.02,10zM357.58,315.5c2.13,2.55 8.51,-7.44 10.85,-12.76 -3.51,4.15 -12.55,10.63 -10.85,12.76zM363.75,323.15c1.06,3.83 17.23,-3.62 19.14,-7.55 -9.36,4.89 -19.35,3.83 -19.14,7.44zM415.54,244.67c0.43,-0.53 1.49,0 1.28,1.17 -0.32,1.6 -2.66,9.78 -6.7,12.55 -2.87,2.13 -18.08,5.42 -24.88,6.91q-5.64,1.06 -9.46,5.64a99.97,99.97 0,0 1,3.19 -10.42c1.28,-3.19 4.68,-6.59 8.29,-6.7 6.49,-0.32 14.99,-0.85 21.27,-3.51a17.02,17.02 0,0 0,7.13 -5.64z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M378,277.11c-2.13,1.49 -4.36,-3.93 -1.81,-9.15 0.11,5 4.04,6.7 1.81,9.15z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M375.98,282.11c-2.87,0.85 -3.72,-4.25 -1.91,-8.29 -0.32,4.36 4.57,7.02 1.91,8.29z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M373.74,286.15c-2.76,1.38 -3.72,-3.83 -1.81,-7.55 -0.21,4.79 3.93,6.27 1.81,7.44z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M374.91,286.47c1.06,3.19 7.44,-2.87 8.51,-6.17 -3.93,3.93 -9.25,3.4 -8.51,6.06z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M377.78,281.47c1.28,3.19 9.25,-5.32 11.06,-9.25 -3.93,3.93 -11.91,6.91 -11.06,9.15z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M379.38,276.47c1.28,3.72 9.89,-6.17 12.44,-9.68 -4.25,3.83 -13.4,7.02 -12.44,9.68zM379.7,272.85c-3.19,-0.64 0.11,-3.19 3.93,-7.34 0.11,4.36 -0.53,7.44 -3.93,7.34z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M384.91,270.3c-1.38,-1.38 1.17,-2.02 4.36,-5.64 0.53,2.45 -2.98,6.91 -4.47,5.64zM370.02,286.47c-1.7,1.06 -2.76,-2.45 -0.74,-5.53 0.53,3.4 2.23,4.25 0.74,5.53zM406.39,264.88c3.51,2.13 9.15,-6.38 10.63,-9.89 -3.08,4.04 -11.27,7.66 -10.63,9.89z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M403.31,269.77c2.02,2.13 8.19,-3.72 10.32,-6.7 -3.19,2.87 -11.17,3.19 -10.32,6.7z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M400.01,274.24c0.64,2.98 9.57,-3.72 11.7,-6.91 -3.83,3.72 -12.34,3.4 -11.7,6.91z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M401.39,277.53c1.38,2.66 8.72,-4.04 10.53,-7.44 -4.57,3.83 -11.7,4.79 -10.63,7.44zM405.11,264.45c-2.23,-2.45 -1.28,-3.62 0.43,-7.44 0.85,3.93 2.23,5.53 -0.43,7.44zM402.03,269.02c-4.25,0.21 -0,-4.89 1.06,-9.25 -0.43,4.89 1.06,8.83 -1.06,9.25z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M399.37,273.07c-2.87,-0.64 -2.13,-4.79 0.21,-9.78 -0.53,5.42 2.45,8.51 -0.21,9.78zM400.44,281.25c1.06,3.19 9.36,-4.25 10.63,-7.23 -4.25,3.62 -11.38,4.79 -10.63,7.23z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M395.44,286.36c1.17,3.19 9.89,-3.4 12.55,-7.02 -5.21,4.25 -13.19,3.83 -12.55,7.02z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M390.33,291.57c1.38,4.57 10.53,-2.76 13.19,-6.38 -5.74,4.47 -13.82,3.19 -13.19,6.38zM398.84,279.87c-2.45,1.17 -3.4,-5.74 -2.02,-10.74 -0,5.32 5,9.36 2.13,10.63z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M394.69,285.4c-2.98,0.85 -2.13,-6.7 0.53,-11.7 0.32,5 2.45,9.57 -0.53,11.7zM389.38,290.61c-3.19,0.11 -1.06,-6.49 1.7,-10.21 -0.43,4.79 1.06,9.57 -1.7,10.21zM395.22,293.38c1.06,1.7 3.4,-1.38 7.44,-3.72 -3.62,1.06 -7.87,2.13 -7.44,3.72zM397.14,237.33c-3.19,-2.34 -0.74,-6.59 -0,-10.63 1.06,3.83 3.62,8.93 -0,10.63zM397.14,242.97c4.79,-0.53 4.04,-6.49 4.25,-10.32 -1.49,5.21 -5.32,6.06 -4.25,10.42zM396.5,242.23c-3.93,-0.21 -3.72,-4.68 -3.93,-9.15 2.02,4.15 4.25,4.79 3.93,9.15z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M396.61,247.76c3.19,0.32 4.68,-2.76 5.32,-7.44 -2.87,4.36 -5.42,2.98 -5.32,7.44zM395.54,247.44c-3.93,0.53 -3.19,-4.04 -3.19,-7.44 1.28,3.19 4.47,4.25 3.19,7.44z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M401.71,245.52c-0.32,2.98 -2.23,8.08 -5.32,6.91 -1.17,-3.62 2.76,-4.36 5.32,-6.91z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M395.54,252.75c1.28,-2.55 -3.19,-3.93 -4.04,-7.34 -0.53,4.89 -0.11,8.08 4.04,7.44zM408.2,221.7c-2.45,-2.55 -0.32,-6.81 0.43,-10.85 1.06,3.83 2.66,8.93 -0.43,10.85zM408.2,226.59c4.25,0.53 5.32,-8.19 5.85,-12.02 -1.49,5.21 -6.38,7.44 -5.85,12.12zM407.35,226.59c-2.98,-1.6 -2.34,-5 -3.19,-7.44 1.91,3.08 3.83,3.51 3.19,7.44z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M408.31,232.34c4.36,0.32 7.02,-9.36 7.23,-13.19 -1.38,5.21 -7.87,7.98 -7.34,13.19zM407.35,232.12c-4.25,-0.96 -3.72,-3.72 -3.08,-8.08 1.38,4.47 3.72,3.51 3.08,8.08z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M408.31,237.87c4.89,0.85 4.57,-7.02 8.51,-12.66 -3.4,4.25 -9.57,9.57 -8.51,12.66zM407.24,237.55c-3.83,0.21 -4.25,-5.42 -4.04,-7.76 0.96,2.34 5.32,4.47 3.93,7.87z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M415.96,231.8c-1.81,6.06 -4.47,12.12 -7.66,11.7 -1.6,-3.51 3.08,-3.93 7.66,-11.7z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M407.14,242.54c1.28,-2.66 -3.19,-3.93 -4.04,-7.34 -0.53,4.89 -0.11,8.08 4.04,7.34zM414.69,238.72c-2.66,5.42 -3.83,11.7 -7.44,10.74 -1.38,-4.25 4.04,-5.1 7.44,-10.74z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M406.5,248.5c1.6,-2.98 -2.87,-3.93 -4.04,-7.87 -0.53,5.1 -0,8.83 4.04,7.76zM398.84,211.81c-2.55,0.64 -5.32,-2.23 -4.36,-6.59 2.98,0.64 4.25,3.4 4.36,6.59zM407.24,209.58c-0.21,1.28 -1.81,1.38 -1.28,-0.43a5.32,5.32 0,0 0,-0 -3.62,8.51 8.51,0 0,1 -0,-4.89c0.43,-1.06 1.91,-0.43 1.28,0.43s-0.74,2.98 -0.21,3.93q0.74,2.34 0.21,4.57zM382.89,226.59c1.06,1.38 3.08,0.43 1.49,-1.6 -1.28,-1.6 -3.19,-2.98 -3.19,-4.68 -0.21,-2.13 -1.38,-5.32 -2.55,-6.49s-2.55,0.43 -1.28,1.28c1.38,0.85 2.34,4.68 2.23,6.17 0.11,2.13 2.13,3.72 3.3,5.32zM386.08,224.15c1.06,1.49 2.55,0.53 1.7,-1.81 -0.74,-1.6 -0.85,-3.72 -1.7,-4.89 -1.28,-1.81 -3.19,-3.3 -4.36,-4.47 -1.28,-1.06 -2.13,0 -1.06,1.06a28.71,28.71 0,0 1,3.51 4.25c1.49,2.34 0.85,4.25 1.91,5.85zM402.78,216.49c0.11,2.13 -1.6,2.55 -1.49,-0.43 -0,-3.19 2.34,-6.17 1.06,-10.95 -0.85,-2.34 -0.85,-6.7 -0.43,-8.93s2.13,-0.85 1.38,0.96c-0.64,2.13 0.11,5.96 0.64,7.98 1.6,5.74 -1.28,8.51 -1.06,11.38zM398.2,204.79c0.21,2.02 1.91,2.13 1.38,-0.53q-0.64,-3.08 -0,-5.64c0.64,-2.23 0.43,-6.06 -0,-7.66 -0.53,-1.7 -2.13,-0.74 -1.49,0.53a10.63,10.63 0,0 1,0.32 6.27c-0.64,2.13 -0.53,5.1 -0.21,7.13zM414.79,244.25c-0.85,0.96 -2.13,0.32 -1.06,-1.06 1.06,-1.06 0.74,-1.28 1.38,-2.55 0.64,-1.49 0.53,-2.23 1.28,-3.19 0.74,-1.06 1.7,0 1.06,0.74 -0.85,1.06 -0.64,1.7 -1.06,2.66 -1.06,1.6 -0.74,2.45 -1.6,3.4zM393.1,270.41a9.57,9.57 0,0 0,-4.68 7.13,17.02 17.02,0 0,1 -2.13,7.55c2.13,-0.53 3.19,-3.93 3.51,-7.23 0.32,-3.4 2.13,-4.79 3.19,-7.44zM387.67,276.68c-1.81,3.3 -4.25,4.57 -4.47,7.02 -0.21,2.87 -0.43,2.98 -1.17,5.74 2.13,-0.53 2.66,-0.74 3.19,-5 0.32,-2.98 2.76,-5 2.45,-7.76z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:fillType="evenOdd"
      android:strokeColor="#bd6b00"/>
  <path
      android:pathData="M331.74,307.31c-1.06,1.91 -2.13,4.79 -4.25,4.25 -0,-1.38 2.23,-2.45 4.25,-4.25m-3.19,0.64c-3.93,1.7 -7.44,1.28 -7.98,3.83 3.83,0.43 5.32,-1.06 8.08,-3.83zM345.67,294.44a14.89,14.89 0,0 0,-5.32 8.19,30.84 30.84,57.13 0,1 -3.83,8.29 13.82,13.82 0,0 0,5.64 -7.87c0.74,-3.19 1.7,-5.64 3.51,-8.51zM342.37,294.44c-2.98,2.34 -5.74,5.1 -6.59,8.4 -0.85,3.08 -1.38,5.42 -3.4,8.51 3.19,-2.02 4.36,-5 5.32,-8.29 0.74,-3.19 2.66,-5.53 4.68,-8.51zM332.59,302.2a1.06,1.06 0,0 0,-0.74 -1.28l-2.76,-0.85c-1.06,-0.32 -1.7,0.43 -1.7,0.96l-0,2.13q-0,0.96 0.74,0.96 1.28,0 2.55,0.74 1.06,0.53 1.6,-0.53zM321.31,302.2q-0,-1.06 0.64,-1.28a5.32,5.32 0,0 1,2.55 -0.43q1.38,0 1.17,0.64l-0,2.23c-0,0.85 -0,0.85 -0.43,1.06q-1.38,-0.11 -2.66,0.64 -0.96,0.43 -1.17,-0.64z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M350.35,332.09q-0.11,1.91 1.06,3.3a20.21,20.21 0,0 0,-14.68 1.17c-1.91,0.85 -4.25,-1.06 -2.02,-2.87 3.19,-2.45 10.32,-1.06 15.63,-1.6m-61.15,0a7.44,7.44 0,0 1,-0.43 3.19c4.68,-1.81 9.68,-0.21 14.46,1.7 3.19,1.38 3.51,-1.06 2.98,-1.81a7.44,7.44 0,0 0,-5.32 -3.08zM293.24,309.01q-2.13,-0.74 -4.25,1.49c-4.57,4.47 -10,8.83 -14.36,12.34 -1.6,1.38 -3.19,3.93 3.62,6.38 0.32,0.21 5.32,2.13 8.51,2.13 1.38,0 1.38,1.91 1.06,2.45 -0.53,1.06 -0.11,1.49 -1.17,2.45 -1.17,1.06 0,2.23 1.06,1.38 3.83,-3.4 10.21,-1.17 16.27,0.74 1.49,0.43 4.04,0.32 4.04,-1.7s1.6,-3.62 2.55,-3.72c2.55,0.43 14.89,0.53 18.61,0.11 2.13,-0.32 2.34,3.08 3.51,4.25 0.85,0.96 3.93,1.17 6.17,0.21 4.25,-1.91 10.63,-1.91 13.29,0 1.06,0.74 2.02,0 1.38,-0.74 -0.85,-1.06 -0.74,-1.7 -1.17,-2.55 -1.06,-2.13 -0.21,-2.55 0.85,-2.66 11.7,-1.6 15.53,-5.53 11.91,-8.83 -4.68,-4.04 -9.78,-8.19 -14.25,-12.97 -1.28,-1.28 -2.13,-1.81 -4.57,-0.74a71.25,71.25 115.79,0 1,-26.91 6.27,80.82 80.82,0 0,1 -26.16,-6.17z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m348.33,309.65 l-1.7,0.43c-9.57,3.4 -18.29,5.74 -27.33,5.74 -8.83,0 -18.08,-2.55 -26.48,-5.96a2.13,2.13 0,0 0,-1.6 0q-0.85,0.21 -1.38,0.74a123.36,123.36 59.06,0 1,-12.55 10.95c-0.74,0.53 -0.64,1.91 0.53,2.34 8.83,3.19 17.44,9.04 42.11,8.83 24.99,-0.21 33.82,-5.96 41.69,-8.61q0.85,-0.32 1.38,-1.06l0.11,-0.85 -0.64,-0.85c-4.57,-3.72 -9.36,-6.7 -12.55,-11.06q-0.53,-0.74 -1.6,-0.53zM348.33,310.18q0.96,0 1.17,0.32c3.19,4.57 8.19,7.44 12.66,11.17l0.43,0.74l0,0.43q-0.32,0.53 -1.06,0.74c-8.08,2.76 -16.7,8.51 -41.47,8.72 -24.67,0.21 -33.18,-5.64 -42.01,-8.83 -0.85,-0.43 -0.74,-1.28 -0.43,-1.49q6.81,-5.21 12.55,-11.06l1.17,-0.64l1.28,0a72.32,72.32 0,0 0,26.59 5.96c9.25,0 18.08,-2.34 27.65,-5.64l1.6,-0.43z"
      android:strokeWidth="0.53"
      android:fillColor="#bd6b00"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M287.82,149.17c0,-1.49 2.13,-1.6 1.91,0.43 -0.32,2.45 4.79,8.83 5.21,12.76 0.32,2.66 -1.6,4.89 -3.4,6.38a7.44,7.44 0,0 1,-7.23 0.53c-0.96,-0.85 -1.81,-3.51 -1.06,-4.57 0.21,-0.32 1.38,3.93 3.93,3.93 3.51,0 6.38,-2.66 6.38,-5 0.21,-4.04 -5.64,-10.42 -5.74,-14.46m10.1,10c0.64,-0.43 1.49,1.38 0.85,1.81s-1.6,-1.38 -0.85,-1.91zM299.51,155.45c-0.32,0.21 -0.85,0 -0.74,-0.21a12.76,12.76 0,0 1,3.83 -3.51c0.43,-0.21 1.06,0.43 0.85,0.74a11.7,11.7 0,0 1,-3.93 2.98m13.4,-10.63c0.32,-0.64 2.23,-1.38 2.76,-1.81 0.43,-0.53 0.64,0.43 0.43,0.74 -0.32,0.74 -2.02,1.81 -2.76,1.91q-0.64,-0.11 -0.43,-0.74zM317.49,145.13a8.51,8.51 0,0 1,2.66 -3.62c0.53,-0.32 1.38,0 1.17,0.43a9.57,9.57 0,0 1,-3.08 3.51c-0.32,0.32 -0.85,0 -0.74,-0.32m-3.93,2.87q-0.32,0.53 0.11,0.85 1.06,0.32 2.13,0c0.64,-0.43 0.32,-3.08 -0.53,-1.7 -0.64,0.85 -1.06,0.64 -1.7,0.85m-7.76,5.96c-1.38,-1.06 0.43,-2.55 1.81,-1.49 2.87,2.13 -4.25,10.42 -8.08,14.25 -0.74,0.74 -1.38,-1.06 -0.43,-2.02a36.16,36.16 0,0 0,7.13 -8.08c0.43,-0.53 0.74,-1.7 -0.43,-2.66m16.27,-7.02c0.11,-1.06 -1.7,0 -1.7,-1.38 0,-0.74 2.02,-1.28 2.87,-0.43 1.38,1.49 0.32,3.93 -2.13,4.15 -1.91,0 -5.32,2.87 -4.79,3.4 0.53,0.74 5.74,1.17 8.83,0.74 1.91,-0.32 1.49,1.38 -0.43,1.6s-3.4,0 -5.1,0.64c-2.13,0.53 -2.98,3.19 -4.15,4.25 -0.21,0.21 -0.85,-0.85 -0.64,-1.28 0.85,-1.28 2.13,-3.19 3.62,-3.83 0.85,-0.32 -2.55,-0.43 -3.62,-0.74 -0.85,-0.21 -0.64,-1.38 -0.32,-2.02 0.43,-0.85 3.62,-4.15 5,-4.04 1.17,0 2.45,-0.32 2.55,-1.06m5.32,0.21q0.85,-0.96 1.6,-1.91c0.32,-0.32 0.96,0 0.85,0.85 -0.11,0.74 -1.06,1.28 -1.6,1.81 -0.53,0.32 -1.06,-0.43 -0.74,-0.74zM334.29,144.71c0.96,0 1.06,1.7 0.21,1.91 -0.64,0.21 -1.06,-1.81 -0.21,-1.91m-2.23,5.32c0,1.6 0.74,1.49 2.13,1.38s2.55,0 2.55,-1.28c0,-1.38 -0.74,-2.66 -1.06,-1.7 -0.11,0.85 -0.32,2.34 -0.85,1.7 -0.43,-0.53 -0.21,-0.64 -1.06,0.21 -0.53,0.53 -0.53,-0.21 -0.85,-0.64 -0.21,-0.32 -0.85,0.21 -0.85,0.43zM322.27,157.68c-0.32,2.02 0,4.79 0.96,4.79 1.28,0 3.83,-4.25 5.1,-6.59 0.74,-1.28 1.91,-1.49 1.38,-0.11 -0.74,2.02 -0.64,6.38 0,7.66 0.43,0.64 3.19,-0.64 3.62,-1.6 0.85,-1.81 0.11,-5.1 0.43,-7.13 0.11,-1.28 1.38,-1.6 1.28,-0.32l-0.11,7.98c0,1.06 3.08,2.55 3.51,-0.64 0.21,-1.91 1.28,-3.93 0,-6.06 -0.85,-1.38 1.17,-1.28 2.23,0.64 0.74,1.28 -0.64,3.4 -0.53,5 0,2.55 -1.91,4.04 -3.3,4.04 -1.28,0 -2.13,-1.6 -3.19,-1.6s-2.34,1.81 -3.19,1.7c-3.83,-0.21 -1.81,-5.64 -2.98,-5.74 -1.28,0 -2.66,5.32 -4.25,5.21 -1.49,-0.21 -3.19,-4.47 -2.45,-6.17 0.53,-1.7 1.6,-2.13 1.49,-1.06m17.97,-8.51c-1.81,-1.06 0,-3.93 0.96,-2.98 1.7,2.13 3.4,6.91 4.68,7.34 0.74,0.21 0.64,-3.62 1.17,-5.32 0.43,-1.38 1.91,-0.96 1.7,0.74 -0.11,0.53 -2.13,6.81 -1.91,7.02a49.98,49.98 0,0 1,3.51 8.29c0.32,1.28 -1.17,0.43 -1.38,0.21 -0.96,-1.49 -2.55,-6.91 -2.55,-6.59l-1.81,8.19c-0.21,1.06 -1.81,0.85 -1.38,-1.06 0.32,-1.49 2.45,-8.83 2.34,-9.15a18.08,18.08 0,0 0,-5.32 -6.7"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M343.43,166.83c-0.43,0 -1.28,1.06 1.28,1.6 3.3,0.64 7.02,-0.53 8.08,-3.83 1.38,-3.93 2.13,-7.66 2.87,-9.04 0.85,-1.6 1.91,-1.49 1.06,-3.83 -0.53,-1.81 -1.6,-1.28 -1.81,-0.32 -0.53,2.45 -2.76,10.63 -3.51,12.02q-1.91,4.04 -7.98,3.4"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M350.24,153.85c-0.43,-0.74 -1.28,0 -1.06,0.74a1.06,1.06 0,0 0,1.28 1.06c0.74,0 2.34,0.11 2.34,-1.06 0,-0.85 -0.74,-1.6 -1.17,-0.64q-0.85,1.17 -1.38,0zM350.98,150.66c-0.21,0.21 0,1.17 0.32,1.06a7.44,7.44 0,0 0,3.51 -0.85c0.21,-0.21 0.11,-0.74 -0.21,-0.74 -1.06,0 -2.76,0 -3.62,0.53m9.36,2.45c0.85,-1.28 2.98,-1.38 2.13,0.43l-6.7,13.08c-0.85,1.49 -1.49,0.74 -0.85,-0.43 0.74,-1.49 5.21,-12.76 5.42,-13.08"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M352.15,168.74c-0.21,-0.85 -1.6,-2.13 -1.38,0.21 0.21,4.04 5.85,2.76 7.44,1.38s0.32,4.57 2.34,5.21c1.06,0.32 3.19,-1.17 4.25,-2.55 2.87,-3.72 4.79,-9.15 7.44,-12.76 1.06,-1.49 -0.53,-2.55 -1.06,-1.38 -2.55,4.04 -5.53,12.34 -8.83,14.46 -2.66,1.7 -1.81,-2.13 -1.91,-3.4 -0.11,-0.85 -1.17,-2.13 -2.55,-0.96a6.38,6.38 0,0 1,-3.93 1.28c-0.74,0 -1.49,0 -1.81,-1.49"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M362.15,161.3c0,-0.32 -1.17,-0.43 -1.06,0.74 0,0.85 1.06,1.06 1.17,1.06 1.6,-1.28 -0.32,-0.64 -0.11,-1.91zM359.71,165.98c-0.32,0 -0.64,1.06 0.21,1.17l4.15,-0.21c0.43,0 0.64,-0.96 -0.43,-0.85 -1.28,0 -2.87,-0.32 -3.93,0zM293.77,148.32c0.53,0 1.7,1.49 1.6,2.02 0,0.21 -1.28,0 -1.6,-0.32s-0.21,-1.7 0,-1.7m-5.64,11.06c-1.06,0.64 0.21,1.81 1.06,1.28 2.98,-2.02 7.44,-4.04 8.51,-7.98 0.32,-1.28 1.49,-3.3 2.66,-3.72 1.06,-0.53 2.76,2.02 3.83,0 0.64,-1.06 2.87,0.74 3.4,-0.43 0.64,-1.38 0.32,-2.13 0.32,-3.62 0,-0.85 -0.74,-1.06 -1.28,0.32q-0.11,1.06 -0.11,1.7 -0.43,0.43 -1.06,0.21c-0.21,-0.21 0,-0.74 -0.64,-1.06q-0.43,-0.11 -0.85,0.21c-0.74,1.38 -1.06,2.66 -2.23,1.06 -0.96,-1.06 -1.49,-3.3 -2.13,-0.32 -0.21,1.06 -1.81,2.55 -2.76,2.55 -1.17,0 -0.85,-3.19 -3.4,-2.66 -1.38,0.32 -1.28,2.87 -1.06,3.72 0.32,1.38 4.25,0.43 3.93,1.28 -0.64,2.87 -4.68,5.74 -8.19,7.44m-24.14,14.04c-0.11,0.53 0.53,1.81 1.17,1.91 0.64,0 1.06,-1.38 0.85,-1.91 -0.21,-0.32 -1.91,-0.32 -2.02,0m3.51,5.21c-0.43,-0.43 -1.7,0.74 -0.64,1.6 0.53,0.53 2.66,1.17 3.19,0.21 0.85,-1.28 -0.74,-5.85 0,-6.38 0.53,-0.53 2.98,2.98 4.25,3.19 2.87,0.43 2.13,-4.89 5.32,-4.47 2.02,0.21 2.23,-2.34 1.91,-4.04 -0.21,-1.6 -2.76,-3.83 -3.93,-4.89 -1.49,-1.28 -2.23,1.06 -1.28,1.7 1.28,1.06 3.51,3.08 3.83,4.36 0.11,0.64 -1.49,1.91 -2.13,1.6 -1.49,-0.85 -2.76,-4.25 -4.04,-5 -0.43,-0.21 -1.49,0.32 -1.06,1.38 0.64,1.17 3.19,2.87 3.3,4.15 0.11,1.06 -1.06,3.4 -1.91,3.4s-3.19,-2.87 -3.93,-4.25c-0.43,-0.53 -1.6,-0.53 -1.81,0.43a23.4,23.4 0,0 0,0.53 5.85c0.21,1.7 -0.96,1.81 -1.6,1.17m-4.25,-9.15c-0.43,0.43 0.85,1.28 1.06,1.06 0.43,-0.43 2.23,-2.45 1.91,-3.19 -0.32,-0.64 -2.76,-2.13 -3.19,-1.38 -0.74,1.17 2.34,1.81 1.81,2.13zM267.61,160.55s0.85,2.66 1.49,1.49c0.43,-0.74 -1.49,-1.49 -1.49,-1.49m1.28,4.25c-0.21,0 -1.06,0.74 -0.53,1.06 0.85,0.43 3.08,0.85 2.55,-0.74 -0.32,-0.96 3.4,0 2.45,-2.55a4.25,4.25 0,0 0,-1.81 -1.81c-0.43,0 -1.6,0.53 -0.85,0.96 0.53,0.21 2.13,1.17 1.6,1.81 -0.74,0.64 -1.17,-0.32 -2.02,-0.11 -0.43,0 -0.11,1.28 -0.43,1.6 0,0.21 -0.74,-0.43 -0.96,-0.32zM274.73,154.7a4.25,4.25 0,0 0,-1.28 2.13q0.11,0.53 0.53,0.53a3.19,3.19 0,0 0,1.28 -2.02c0,-0.32 -0.21,-0.85 -0.53,-0.64m2.98,-0.32c-0.85,-1.06 1.06,-2.76 1.81,-0.53 0.53,1.38 5.85,8.4 6.91,10.74 0.85,1.6 0,2.23 -0.96,1.06 -2.66,-3.4 -4.89,-7.66 -7.76,-11.27m5.53,0.11c0.96,-1.06 2.87,-3.19 2.34,-4.25s-1.6,-1.06 -1.81,-0.74c-1.06,1.38 0.85,1.06 0.53,1.49q-0.85,1.38 -1.38,2.76c-0.11,0.32 0.11,0.96 0.32,0.74m82.74,3.4c-0.74,-0.53 0.64,-3.19 1.6,-2.13 2.45,2.87 3.62,12.34 4.36,19.46 0,0 -1.06,0.96 -1.06,0.74 0,-3.72 -1.6,-15.31 -4.89,-18.08m-56.47,-9.15c-0.85,-1.91 1.17,-2.55 1.49,-1.28 1.38,6.17 4.79,10.85 7.44,14.99 0.74,1.28 0,2.13 -1.81,0.85 -1.28,-0.85 -2.66,-4.15 -3.19,-4.25 -1.28,-0.21 -4.04,5.32 -9.68,3.72 -1.49,-0.43 -1.38,-4.79 -1.49,-6.7 0,-0.96 1.06,-1.06 1.06,0 0,1.81 0,5.53 2.23,5.74 1.91,0 5.96,-2.55 6.81,-4.68s-2.02,-6.27 -2.87,-8.51z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M367.47,174.48c0.43,-1.28 6.49,-11.49 7.34,-13.72 0.43,-1.06 2.13,1.91 0.43,3.51 -1.49,1.28 -5.85,8.51 -6.7,11.06 -0.43,1.06 -1.49,0.53 -1.06,-0.85"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M377.78,164.81c1.06,-4.25 3.83,0.64 1.38,2.98 -3.62,3.62 -4.79,10.53 -10.63,11.59 -1.49,0.32 -4.25,-0.74 -5.1,-1.38 -0.32,-0.21 0.21,-1.7 1.17,-0.96 1.38,1.06 4.36,1.38 5.96,0.11a26.59,26.59 0,0 0,7.23 -12.34m-60.62,13.51c-0.32,0.32 -1.06,0.32 -1.17,0.74 -0.32,1.49 0,2.34 -0.32,3.83s-1.38,1.49 -1.28,0.32c0,-1.49 1.38,-3.72 0.43,-3.83 -0.64,-0.11 -1.06,-0.96 -0.43,-1.38q1.6,-0.96 2.55,-0.43 0.53,0.32 0.21,0.74"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="M316.32,176.18c-1.49,1.49 -2.98,2.02 -4.36,3.72 -0.64,0.64 -0.53,1.6 -0.96,2.55 -0.32,0.96 -1.49,1.06 -1.81,0.96 -0.53,-0.43 -0.43,-2.13 -1.06,-1.28s-0.96,2.13 -1.81,2.13 -2.13,-1.6 -1.38,-1.6c2.45,-0.32 2.34,-2.13 3.19,-2.34 1.06,-0.11 1.06,1.6 1.81,1.28 0.43,-0.21 0.74,-2.23 1.28,-2.76 1.6,-1.7 2.87,-2.55 4.57,-3.83 0.74,-0.64 1.38,0.53 0.53,1.28zM321.95,181.5c-1.28,0.21 -1.06,1.81 -0.64,1.91 0.53,0.32 1.49,0.43 1.81,-1.38 0.21,-0.74 0.32,3.72 1.91,2.02 1.06,-1.06 3.3,0.21 4.25,-1.06 0.74,-0.96 1.06,-1.6 0.43,-2.87 -0.21,-0.32 -1.06,-0.21 -1.06,0.74s-0.53,1.81 -1.38,1.7c-0.43,-0.11 0.21,-2.02 -0.21,-2.55a1.06,1.06 0,0 0,-0.74 0c-0.32,0.43 0.32,2.34 -0.64,2.55 -1.28,0.21 -0.64,-1.28 -1.06,-1.49 -1.81,-0.85 -1.91,0.21 -2.66,0.32zM331.52,178.31c0.96,-0.21 0.64,-0.21 2.13,-1.38 0.53,-0.43 0.64,0.85 0.53,1.38 0,0.74 -1.06,0.21 -1.38,0.96 -0.43,0.96 -0.21,3.19 -0.43,4.04 0,0.43 -0.85,0.43 -0.85,0 -0.21,-1.06 0.11,-2.13 0,-3.51 0,-0.43 -0.53,-1.17 0,-1.38zM326.21,175.65q-0.21,1.38 -0.21,2.45c0,0.53 1.06,0.21 1.06,0.11 0,-0.85 0.21,-2.13 0,-2.45q-0.53,-0.32 -0.85,-0.11"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m319.51,165.76 l-1.49,5.96 -2.13,-4.04l0,4.15l-4.68,-5.53 1.6,5.96 -4.25,-3.62 2.34,4.04 -7.44,-4.79 4.68,5.53 -5.96,-2.98 4.25,3.62 -9.57,-3.62 9.25,4.57a30.84,30.84 0,0 1,13.4 -2.76q7.98,0.11 13.29,2.76l9.36,-4.57 -9.57,3.62 4.25,-3.62 -5.85,2.98 4.57,-5.53 -7.44,4.79 2.34,-4.04 -4.25,3.51 1.6,-5.85 -4.57,5.53L323.23,167.68l-2.13,4.04z"
      android:strokeWidth="0.53"
      android:fillColor="#fff"
      android:strokeColor="#00000000"
      android:fillType="evenOdd"/>
  <path
      android:pathData="m311.3,295 l-0.3,2.6h-0.4l-0.1,-1.8 -0.5,-1.6 -0.5,-1.3 -1,-1.4 0.8,-2.2a7,7 0,0 1,1.5 2.4,9 9,0 0,1 0.5,3.2m7,-4.2q0,1 -0.5,1.5 -0.3,0.5 -1.3,0.7l0.4,1.5v2l-0.1,1.3h-0.4l-0.1,-1.3 -0.2,-1 -0.4,-1 -0.7,-1.4 -1,-1.7 0.6,-2 1,1q0.4,0.3 1,0.3 1.2,0 1.2,-1.3h0.4v1.4m6.4,4.8 l-0.5,2.1q-0.6,0 -0.8,-0.7l-0.4,-1.3 -0.1,-1.7 -1,0.2a2,2 0,0 1,-1.3 -0.4,1 1,0 0,1 -0.5,-1q0,-1.4 0.7,-2.3 0.8,-1 1.5,-1.1 0.7,0 1,0.4l0.3,0.9v2q0,1.3 0.3,1.9 0,0.4 0.8,1m-2,-3.5q0,-0.9 -0.8,-0.8l-0.6,0.1q-0.2,0.1 -0.2,0.3 0,0.5 1,0.5zM331.3,295 L331,297.6q-0.8,-0.5 -1.4,-2l-1.3,-4.1 -1.8,5.5 -0.8,0.7v-2.5q0.9,-1 1,-1.5l0.8,-1.7 0.5,-2.7h0.4l0.9,2.7q0.3,1 0.9,1.6l1,1.4"
      android:fillColor="#fff"/>
  <path
      android:pathData="M350.8,319.4q0.6,0.6 0.7,1.2l0.4,1.6 -0.8,0.1 -1,-1.5 -1.1,-1.2 -1.7,-1.5 -2,-1.7q-0.6,-0.3 -0.6,-0.5l-0.3,-0.8 -0.2,-1.6 2.7,2.2 2.5,2.2zM341.3,313.6 L341.1,315.6L338,315.6l0.3,-2zM349.7,322.5 L342.1,324.8 340.8,322.8 347.3,320.8 346.6,320 345.7,319.4a1,1 0,0 1,-0.4 1l-1,0.6a3,3 0,0 1,-1.8 0,2 2,0 0,1 -1.3,-0.7 4,4 0,0 1,-0.7 -2.2q0,-1.5 0.9,-1.8 1.1,-0.3 3,0.7a8,8 0,0 1,3 2.4zM343.9,318.5 L343.1,318.2h-0.6l-0.5,0.3v0.6l0.5,0.2h0.6l0.4,-0.3zM335.9,316.9 L335.4,318.9 332.2,318.6 332.7,316.6zM343.4,324.6 L341.7,325L340,325l-1.5,-0.4q-0.5,0.8 -1.5,1.2l-1.6,0.6 -1.2,0.3 -1,-2 1.1,-0.3 1.3,-0.4 0.9,-0.5 -1,-0.5h-0.9l-0.2,0.3h-0.5q-0.8,-1.2 -0.3,-2c0.5,-0.8 0.9,-0.8 2,-1a7,7 0,0 1,2.6 -0.2q1.2,0.1 1.5,0.9 0.2,0.3 0.2,0.7l-0.4,1.2h1.1l1.7,-0.3zM335.4,326.4 L333.8,326.7a3,3 0,0 1,-2.2 -0.4,6 6,0 0,1 -1.7,-2.6l-0.8,-2.2a2,2 0,0 0,-0.8 -1l-0.9,-0.5 0.6,-2.1q0.9,0.4 1.4,1l1,1.7 0.5,1.5 1.1,2.2q0.5,0.4 1,0.3l1.7,-0.2zM328.4,318.9 L327.4,320.8 324.4,320.1 325.4,318.2zM330.2,327.3 L322.7,328 322.3,326 328.5,325.3 327.9,324.5 326.9,323.9 327.4,321.9q1,0.6 1.6,1.3 0.5,0.8 0.8,2.1zM324.2,328.3 L322,328.1 320.3,327.6 319,328h-3.7l-1.2,-0.3q-0.4,-0.3 -0.8,-1a4,4 0,0 1,-1.5 1l-1.7,0.1h-1.7l0.2,-2.1h1.7q1.2,0.1 2.1,-0.4a2,2 0,0 0,1.3 -1.8l0.7,0.1 -0.1,1.3q0,0.4 0.3,0.7 0.4,0.3 1,0.3h1.5q1.5,0 2,-0.2 0.9,-0.2 1,-1.1l0.1,-0.4s0.3,0 0.5,-0.2l0.5,-0.2v0.7l-0.3,1.1 2,0.5q0.1,-0.3 -0.1,-0.7l-0.3,-0.6 0.1,-0.3 0.3,-0.2 1,-0.9 0.5,1v1zM312.9,319.6 L310.9,320.9 309.6,320 308.2,321 306.3,320 308.1,318.7 309.6,319.5 311.1,318.5 312.9,319.5m-3,8.2 l-7.3,-1.2 0.8,-2 6.2,1q0,-0.6 -0.2,-1l-0.5,-0.8 1.6,-1.7q0.6,0.8 0.7,1.6t-0.5,2.1zM303.8,326.7 L302.2,326.4q-1.3,-0.3 -1.5,-1.2 -0.3,-0.9 0.8,-2.8l1.2,-2q0.4,-0.7 0.3,-1.2l-0.3,-0.7 2.2,-1.6q0.4,0.8 0.3,1.4 0,0.8 -0.7,1.8l-0.8,1.4a6,6 0,0 0,-0.9 2.2q0,0.6 0.5,0.7l1.6,0.4zM300,318.7 L297.5,319.8 295.7,318.1 298.3,317.1zM299,325.3 L297.4,326.7 295.7,327.3 293.3,327.2 290.5,326.5a8,8 0,0 1,-3.4 -2q-0.9,-1.2 0,-2.2a7,7 0,0 1,2 -1.6q1.1,-0.7 3.8,-1.6l0.4,0.5 -2.8,1.2q-0.8,0.4 -1.3,1t0.2,1.6a11,11 0,0 0,6.3 2.2q1.8,0 2.3,-0.7 0.4,-0.4 0.5,-1l0.2,-1.6 2.5,-1.5 -0.1,1.5a4,4 0,0 1,-1 1.6z"
      android:fillColor="#bf0000"/>
</vector>
