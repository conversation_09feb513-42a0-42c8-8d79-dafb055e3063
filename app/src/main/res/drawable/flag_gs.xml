<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:fillColor="#006"/>
  <path
      android:strokeWidth="1"
      android:pathData="M471,345s-2.6,6.8 -4,6.8c-1.6,0 -7,-3 -7,-3s-3.9,6.4 -6,6.7c-2,0.4 -7.4,-0.9 -7.4,-0.9s-5.2,0 -5.4,-0.7c-0.2,-0.8 0.2,-2.3 0.2,-2.3s-7.5,6 -9.1,5.6c-1.7,-0.3 -7.3,-7.4 -7.3,-7.4l-1,3.7 -10.6,-0.4 -9.3,-6s-5.2,8.6 -5.4,8.4 -9.1,2.1 -9.1,2.1l-0.6,-1.7 -6,-3.5s4.7,-6.5 4.7,-6.7 -2.2,-1 -2.2,-1l-3.4,2.8 -6.9,4.5 -6.9,-3.2 3,-5.6 0.4,-4 5.4,-8.3 66.2,-64.3 32.6,60.2z"
      android:fillColor="#6a4c2d"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m518,348.8 l17.3,-0.6 -7.4,-3.6 65.7,-2.5 -9.3,-3.5 -8.2,-11.2 -34,-2.5s-2.6,-2 -6.7,-1c-0.2,-2.6 -3.4,-6 -3.4,-6l-21,-1.7 -13,9 8.8,22.6z"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m451,156 l1.8,-6s3,-5 3,-7.4c0,-2.2 2.3,-4.9 2.3,-4.9s6.9,-2 8.4,2.3c7.4,-11.3 16.2,-0.6 16.2,-0.6l2.4,-2.8 5,-6s7,6.5 7,7.6c0.2,1.2 1.3,0.4 1.3,0.4l7.6,-0.6a9,9 0,0 0,2.9 8.2c2.7,1.6 5.1,11 5.1,11z"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="516.76"
          android:startY="149.41"
          android:endX="518.64"
          android:endY="126.47"
          android:type="linear">
        <item android:offset="0" android:color="#FF474747"/>
        <item android:offset="1" android:color="#FFFF5500"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M518,226.1c1.4,-0.8 5.5,-2.1 5,-8.2s-5.7,-6.8 -8.7,-6.6 -5.5,2.8 -5.5,2.8l-9.8,-6.2s4.9,-30.7 10,-32.6c5,-3.5 5.8,-5 5.8,-5.8 0,-0.9 -1.8,-2.9 -1.8,-2.9l-31.7,-3.7 -30,3.5s-2.2,3.6 -2,5c0.4,1.4 0.5,2.9 5.8,7 6,4.6 10,30.8 10,30.8s-8.3,4.2 -8.9,3.6c-0.5,-0.5 -3,-1 -4.3,-0.8s-5.7,2.5 -5.7,8.2c0,5.8 4.4,9.1 4.4,9.1s28.6,-3.3 32.9,4.6c4.1,-9.5 31.3,-6.2 34.6,-7.8z"
      android:fillColor="#656263"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:strokeWidth="1"
      android:pathData="M548.9,194.2s0.3,-3.3 2.7,-4.9 18.2,-2.4 22,0 5.2,14.1 5.2,14.1 2.4,4.1 2.6,7.2c0.1,3 0.5,5 0.5,5s12.6,16.5 12.8,31.4c1.4,10.1 1.1,37.2 -2.9,47.5a43,43 0,0 1,-5.5 21.3s1.3,2.2 1.1,4.6 -1.4,4.6 -1.4,4.6l18.5,9.1 -6.8,-2.5 6.8,5.7 -7.7,-3.6 4.5,4.6c-0.1,-0.2 -11.2,-5.1 -11.2,-5.1l5,4.7 -8.5,-4s5,5.5 5,5.2l-7.4,-3.2 0.4,2.7s-5.8,-0.3 -5.8,-4.6a19,19 0,0 1,-4.7 -3.8l-13.2,-2.2 -14.9,-44.6 3.5,-75.2 1,-3.8z"
      android:strokeLineJoin="round"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M539,434.8s-7.7,-16 -11.5,-16.3c-3.4,-6.6 12,-60 41.7,-63.1 16.3,1.3 1.3,18.8 -9.4,13.4a45,45 0,0 0,6.7 11.5s-21.3,9.2 -27.6,54.5zM436.7,437.1s6.3,-18.3 10.1,-18.5c3.9,-0.2 -10.1,-60.9 -40.6,-61.3 -16.4,1.3 -1.3,18.9 9.5,13.5a45,45 0,0 1,-6.8 11.5s21.5,9.2 27.8,54.8z"
      android:fillColor="#fb0"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M464.6,323c0,-0.2 -7.6,2.3 -2.7,10.5 0.3,-3.2 3.7,-5 3.7,-5s-5.1,6 0.3,11.1c0.7,-4.8 3.8,-6.3 3.8,-6.3s-3.9,11.2 0,13.5c0.5,-4.5 3.3,-6.5 3.3,-6.5s-3.4,10 -0.3,12.3c0.3,-3.8 2.9,-5.5 2.9,-5.5s-1.5,10.5 2.5,11.2c0,-3.7 3,-7.3 3,-7.3s-1.3,8.5 5,8.9c0,-3.2 1.2,-6.9 1.2,-6.9s2.9,9 7,7.6v-7.7s2.7,8.6 7.7,7c-0.7,-2.4 0.3,-5.3 0.3,-5.3s2.6,5.4 7.4,3.6c0.8,-1.6 -0.2,-4.8 -0.2,-4.8s7,7.2 9,2.9c2.2,-4.3 -5.5,-5.8 -5.5,-5.8h5.2s-1.7,-4.4 -8.7,-5.4c2.4,-1 4.9,-0.2 4.9,-0.2s-1.5,-5.5 -8.7,-6.1c2.8,-1 6,-0.3 6,-0.3s-1,-5.2 -9,-6.7c1.3,-1.4 4.7,-1 4.7,-1s-3.2,-4.9 -6.8,-4.6 -36,-3.3 -36,-3.2z"
      android:fillColor="#00713d"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M496.6,349.5s3,1.4 2.8,2.6m-1.1,-4.9s3.7,2.6 3.6,4.5m-0.3,-5.8s2.9,1.8 3,4.7m1.9,-3.6s1.4,3.2 1,3.8m1.9,-2.1c0.2,2.5 -0.2,2.8 -0.2,2.8m-36,-18.5s3.5,2.2 3.2,5.3m-3.4,-2.7s1.6,1.6 1.3,2.6m3,-5.2s2.3,3.2 1.5,5.4m2.4,-3.3s1,2 0,3m1.8,-2.1s1.2,2 0.1,2.7m-1.5,3.9s3.2,0.7 3.7,3.1m-1.8,-5.3s3.4,0.2 3.8,3.8m0.7,-5.1s2.3,4 1.9,5.2m2.4,-4.8s0.9,3.7 0.2,5m2.6,-3.5v4.3m-4.2,-10.2s2,0.8 2,2.9m-0.1,-4.8s2.7,1.5 2.5,4.5m0.7,-5.7s2.3,2.7 1.5,5.8m2.5,-5.2s-0.8,3.5 -0.4,4.9m3,-2.5s-1.5,1.1 -0.7,2.6m-12.7,10.8s0.5,2.6 0,3m-3.3,-4.6s1.6,3 1,4.4m-4.6,-4.5s1.9,2.2 1.8,4m-4.8,-3.6s1.8,1.7 1.6,2.7m-3.1,-0.1s1.7,1.8 1.6,2.1"
      android:fillColor="#00000000"
      android:strokeColor="#3ec26d"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="m441.3,229.3 l24.3,-0.5s16,0 17.7,5.2c3,-6.9 17,-6.5 17,-6.5l25,-0.8 0.5,60.6c-5,23.6 -22,40.2 -40.7,47.3a62,62 0,0 1,-41.2 -47.7z"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m450,229 l35.3,94.2 32,-96.2c-10,0.6 -31.2,-1.6 -33.9,7.5 -4,-8.1 -26.4,-4.9 -33.5,-5.5z"
      android:fillColor="#006b00"
      android:strokeColor="#000"/>
  <path
      android:pathData="M474,280c1.4,0.7 1.3,-25.5 3.1,-26.3l1.4,-5.5c-1.5,-2.7 -8.4,-2.6 -11,-0.2l1.7,4.8c3.5,5.6 2.8,27.8 4.8,27.2z"
      android:strokeWidth=".5"
      android:fillColor="#ffc900"
      android:strokeColor="#006b00"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M464.5,77s2.5,-2.9 2.6,-3.5 11.4,-1.2 18,-13.7c3.8,-6.5 0,-2.7 0,-2.7L485,54s4.6,-4.4 3,-6.8c-1.8,-2.4 -1.2,3 -4,3 -2.7,-0.1 -1.2,-6 -1.2,-6s-0.2,-0.6 -1,-1c-1.1,0.2 -0.8,2.2 -1.8,2.4 -1,0.3 -2,-4.7 -2,-4.7s-1.7,-2.3 -3.1,4.8c0.8,7.7 5.6,6.2 5.6,11.2s-4.3,8.8 -5.5,9c-1.3,0 -0.8,-4.3 -0.8,-4.3s-0.7,-2 -1.1,-2 2.5,-0.5 2,-6.1c-1,-6.8 -1.8,1.6 -3.6,1.2 -1.9,-0.3 -0.5,-6.2 0.2,-7 0.7,-0.6 -0.8,-3.4 -4.8,3.8 -0.3,3.6 -0.8,-0.9 -1.6,-0.6 -1.3,2.8 -1.1,4.8 0.8,7.4 2.9,2.7 4.7,5.3 4.6,6.6 -0.1,1.4 -1.6,4.5 -3.7,4.5 -2,0 0.1,-3.8 0,-5 0,-1.3 -3.5,-5.8 -3.5,-5.8s-2.4,-3.9 -2.1,-4 -0.3,-0.7 -1.4,3.3 -2.5,-2.6 -2.5,-2.6 -1.6,4.9 1.8,7.8c-2.6,-0.3 -2.8,0.7 -2.8,0.7 0,1.4 3.5,2 4,4.3 0.4,2.4 -3.7,3.8 -3.7,3.8s1.7,2.3 6.5,-2.3c0.1,3 -1.8,5 -1.8,5 1.6,0.7 2.8,0.6 3,2.5z"
      android:strokeLineJoin="round"
      android:fillColor="#cdad56"
      android:strokeColor="#000"/>
  <path
      android:pathData="M449,77s-2.2,-3.2 -2.3,-3.8 -10.4,0.8 -15.3,-11.8c-3.2,-6 0,-2.5 0,-2.5l0.2,-2.9s-3.4,-4.1 -2,-6.3c1.5,-2.2 1,2.8 2.8,2.8 2.3,-0.1 1,-5.4 1,-5.4s0.2,-0.6 1,-0.9c0.9,0.1 1.3,3.2 2.1,3.4s2.7,-3.6 3.3,-4.4c0.8,-0.1 0.7,-3.2 2,3.3 -0.7,6.9 -6.4,5.5 -6.4,10s3.6,8.1 4.7,8.2c1,0.2 0.7,-3.8 0.7,-3.8s0.6,-1.8 1,-1.8c0.3,0 -3.4,0.7 -3,-4.5 0.8,-4.8 2.7,0.3 4.3,0s-0.2,-5 0.2,-5.8c0.1,-0.8 2.4,-3.9 3.3,3 0.3,3.3 2,-2.7 2.7,-2.5 1.2,2.6 -0.5,6 -2.1,8.4 -2.4,2.4 -3.5,5 -3.4,6.2 0,1.3 0.6,2.8 2.4,2.8s0.5,-2.2 0.6,-3.3 3,-4.8 3,-4.8c0.6,-1 0.1,-2.6 0.7,-3.4 1,0 1.4,-1.3 2.3,2.3 1,3.6 2.1,-2.4 2.1,-2.4s1.4,4.5 -1.5,7.2c2.2,-0.3 2.4,0.6 2.4,0.6 0,1.2 -1,1.7 -1.4,4a4,4 0,0 0,1.1 3.3s-1.4,2.2 -5.5,-2a7,7 0,0 0,1.6 4.5c-1.4,0.7 -2.4,0.6 -2.6,2.3z"
      android:strokeLineJoin="round"
      android:strokeWidth=".9"
      android:fillColor="#cdad56"
      android:strokeColor="#000"/>
  <path
      android:pathData="M511.3,243c-2,-1.8 -2.3,-0.2 -3.4,-0.6q-0.7,-0.4 -1.3,-1.1 -0.6,-0.6 -1.4,-0.8l-0.6,1.7c-0.2,0.6 0.6,1.5 0.6,2.4q-0.1,2 -2.3,2.7c0.5,-0.9 0.7,-0.9 0.6,-2 0,-0.5 -1.6,-1.5 -1.4,-2 0.2,-0.9 0.7,-1.8 0.4,-2.7 -0.7,0.5 -1.5,0.2 -2.2,0.5 -0.5,0.3 -0.6,1.3 -1.3,1.7s-2.5,0.2 -4,-0.6c0.9,-0.7 1.6,-0.2 2.5,-1 0.4,-0.2 0.4,-1.7 0.9,-2l1.9,-1q-0.4,-0.9 -0.8,-1.6c-0.4,-0.5 -2.1,-0.4 -2.5,-0.8 -0.8,-1 -0.7,-2.1 -1.4,-3 2,0.7 1.7,1.7 2.2,1.6q1.6,-0.7 2.5,-0.3c0.9,0.4 1.4,1.5 2,1.7l0.6,-1.9c0.2,-0.6 -0.8,-1.5 -0.7,-2.1 0.4,-1.1 1.4,-2.1 1.8,-3.3l0.4,3.1c0.1,0.6 1.2,0.9 1.2,1.4 0.1,0.6 -0.5,1.8 -0.5,2.4q1,0 2,-0.2c0.7,-0.1 1,-1.5 1.6,-1.6 1.2,-0.2 2,-0.1 3.3,0 -1,0.9 -1.6,0.8 -2.1,1.6 -0.5,0.3 0.1,1.4 -1.1,2.2 -0.5,0.3 -1.8,0 -2.2,0.3l1.2,1.3c0.4,0.5 2.1,0.7 2.5,1.2q1.3,1.4 1,2.9zM457,244c2,-1.9 2.3,-0.2 3.4,-0.7q0.7,-0.3 1.3,-1t1.4,-0.9l0.6,1.8c0.2,0.5 -0.6,1.5 -0.6,2.3 0.2,1.3 0.7,2.3 2.3,2.8 -0.5,-1 -0.7,-1 -0.6,-2 0,-0.6 1.6,-1.6 1.4,-2.1 -0.2,-0.8 -0.7,-1.8 -0.4,-2.6 0.7,0.4 1.5,0.2 2.2,0.4 0.5,0.3 0.6,1.3 1.3,1.7s2.5,0.2 4,-0.6c-0.9,-0.7 -1.6,-0.2 -2.5,-0.9 -0.4,-0.3 -0.4,-1.8 -0.9,-2 -0.4,-0.4 -1.5,-0.8 -1.9,-1.1q0.4,-0.9 0.8,-1.6c0.4,-0.5 2.1,-0.4 2.5,-0.8 0.8,-1 0.7,-2.1 1.4,-3 -2,0.7 -1.7,1.7 -2.2,1.6q-1.6,-0.6 -2.5,-0.3c-0.9,0.3 -1.4,1.5 -2,1.7l-0.6,-1.9c-0.2,-0.5 0.9,-1.5 0.7,-2 -0.4,-1.2 -1.4,-2.2 -1.8,-3.4l-0.4,3.2c-0.1,0.5 -1.2,0.8 -1.2,1.3 -0.1,0.6 0.5,1.9 0.5,2.4q-1,0 -2,-0.2c-0.7,0 -1,-1.4 -1.6,-1.6 -1.2,-0.2 -2,-0.1 -3.3,0.1 1,0.8 1.6,0.8 2.2,1.5 0.4,0.4 -0.2,1.5 1,2.2 0.5,0.3 1.8,0 2.2,0.3l-1.2,1.4c-0.4,0.4 -2.1,0.6 -2.5,1q-1.2,1.5 -1,3"
      android:fillColor="#ffc900"/>
  <path
      android:pathData="M509.4,249.2c-1.9,1.5 -14.3,4.6 -14.4,15.3s2.2,13.2 -0.1,13.5c-4.5,0 -5.1,-12 -5,-17.3s0.3,-4.5 0.3,-4.5 3,0.8 2.8,3.4 3,-6.4 1.9,-8.8c2,2 4.7,1.2 4.7,1 0,0 -1.5,-1.7 -2.2,-3 -0.6,-1.1 2.3,0.8 2.3,0.8s0.2,-2 -2.4,-2c-3.3,0 0.5,-1 0.5,-1s2,1.7 3.2,-0.2a11,11 0,0 0,-3.4 -2.3s-1.8,-3.4 -4.2,-4c-2.8,-0.7 -2.5,1.2 -5.7,0.9 -0.6,1.2 -0.6,1.4 0.7,1.8 -2.2,1.5 -1,4.4 -1,4.4s3.4,-1.4 3.3,1c-0.1,2.5 -2,2 -3.3,0.5 -1.2,-0.6 -1.6,0.7 -1.6,0.7l1.8,2s-3.4,-0.2 -4.3,2.1c1.9,-0.1 3,0.4 3,0.4s-4,1.8 -4.4,2.7 -0.5,-1 -0.7,-1l-3.8,-1.4 -1.3,5.9s2.6,2.5 4,1.7 4,-3.3 5.5,-2.6c-4.5,3.4 -9,8.3 -11.4,9 -0.6,-0.6 -2.8,-2.7 -3.7,-1.6 -0.9,1 -0.2,2.4 1,2.3 1,-0.2 -3.8,1 -2.8,3.1 1,2 1,1.9 1.8,1.4 1,-0.6 -0.7,-0.7 2.6,-1.7s3.2,-1.9 3.2,-1.9 -0.7,1.5 -2.5,2c-1.7,0.3 -3.1,0.3 -2.8,0.9 0.4,0.5 1.2,1.5 1,2 -0.3,0.6 3.6,-2.8 4.7,-0.1 2.6,-0.2 4.4,-3.4 3.1,-5.3 0.2,2 1.4,2.7 0.7,3.7 -0.8,0.9 6,-3.2 2.7,-5.6 0.8,2 1,3.6 1,3.6s1.5,0 1.9,-0.6 -0.8,1.5 -0.3,1.9 3,2.7 2,4.3c-0.7,-1 -0.8,-2.4 -1.6,-2.3s-4,2.5 -5.9,2.6 2.3,7 2.3,7 -2.9,-0.3 -3.3,0 -2.3,-2.5 -2.6,-0.9c-0.7,2 0.6,1.2 0.6,1.2s-1.7,-0.8 -2.6,0.2 -1.7,1.9 -1.1,2.3 3.4,0.4 3.8,0.3 -3.2,0.2 -3.4,0.6c-0.3,0.4 -0.8,2 0,2.5 0.7,0.5 2.7,-0.3 2.8,-0.7s0.2,1.5 0.2,1.5 3.3,0.3 3.3,-3.1 0.3,2.4 0.3,2.4 3.3,0.6 3.4,-2.8 0.4,2.3 0.4,2.3 2.2,-0.7 2.2,-1.2 -0.1,6.7 -1.7,8.8c-2.4,-1.6 -4,1 -4,1s0.2,4 0,5 1.6,-0.6 1.7,-1c0.2,-0.4 2.5,-1.5 2.6,-1.7l0.8,-1.7s-0.6,2 -1.4,2.2c-1,0.3 -1.8,1.2 -1.4,2 0.3,0.9 1.7,1.4 2.2,2.2s2.3,-4.7 2.3,-4.7l0.2,1.2s2.3,-0.6 2.5,-1.7c0.3,-1 -2.4,-2 -0.2,-3.8s0,1.7 0,1.7 0.7,2.6 1.2,2.6 1.8,-5 0.6,-6.2 1.9,1.5 1.9,1.5 1.8,-5 -0.2,-5.7l-2.8,-1s1,-1.3 0.5,-1.4c-0.5,-0.2 2.7,3 3.2,2.1s1.3,-3.2 -2.4,-4.6 -0.1,-5.1 -0.1,-5.1 2.3,2.7 4,1.2c1.6,-1.5 -0.2,-1.5 -0.2,-1.5s4.7,-3 4.9,-4.5c-1,-0.1 -2.5,0.1 -2.5,0.1s2.7,-1.7 2,-4.6c-1,1.5 -2.4,1.5 -2.4,1.5s2.3,-2.3 2,-4.5c-1.4,1.1 -1.3,2 -2.2,1.7s-2.4,-8.9 1.1,-9.4c3.6,-0.6 1.7,4.2 1.8,4.2s5.4,-2.2 0,-5.7c1.3,-0.4 4,2 4,2s-1.2,-6 -7,-2.3q2,-2.4 3.5,-2.2c1.2,0.3 5.3,-0.1 5.3,-1.3 -1,-0.8 -3.2,0.4 -4.4,0 -1.1,-0.4 8.2,-1.1 7.4,-5.7"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#ffc900"
      android:strokeColor="#006b00"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M469,252.7s5.1,-2.9 7.6,0.7m2.1,5.4c-0.1,0 -1.8,1.8 -2.2,1.8m5.6,3.9s4.8,0.4 7.8,-4.6"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#006b00"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M487.2,263s0.4,2.7 1.2,2.7 -1.3,0.7 -2,0.2c0.8,1.2 1.3,3.4 0,4.2"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#006b00"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M484.8,297.3s-1.6,1.9 -3.7,2"
      android:strokeWidth=".5"
      android:fillColor="#e80000"
      android:strokeColor="#006b00"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M486.7,288.6s-1,-5.5 -0.4,-6.8c0.4,-1.8 2,-2.5 3.4,-4.8"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#006b00"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M486.2,284.6s-1.3,3.3 -6.9,2.2m8.4,-10.3s0.4,4.3 -4.7,2.7m-1.5,-22.2s-2,1.8 -1.3,4.4"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#006b00"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M489.1,252.1s2.7,-1 3.2,-1.6a8,8 0,0 0,1.7 -2.4c0.2,-0.8 -1.8,-2 -0.8,-3.8q1,-1.4 3,0c1.6,1 -1.3,-3 -2.8,-3.2 -1.6,-0.1 -2.6,1.1 -3,0.8 -0.4,-0.2 0.1,1.1 -0.5,1.1s1.3,1.1 1.2,1.8 2,3 1.9,3.5 -3.2,3.7 -3.9,3.8"
      android:fillColor="#006b00"/>
  <path
      android:pathData="M491,243.2q1.6,-0.5 1.2,1z"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#ffc900"
      android:strokeColor="#ffc900"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M483,303.7s-0.5,2.9 0.5,3.1q0.1,-1.6 0.5,-2.1zM480.5,300.2c-1,-0.1 -2.5,0.8 -2,1.8 0.7,-0.7 1.4,0 2,-0.7v-1zM480.5,298.2s-1.5,-0.2 -1.7,0.9q1.4,-0.4 1.7,0z"
      android:strokeLineJoin="round"
      android:strokeWidth=".5"
      android:fillColor="#e80000"
      android:strokeColor="#006b00"/>
  <path
      android:pathData="M474.1,282.2s1,0.3 1.2,1.3c0.7,-0.4 1,-2.6 -1.2,-1.3zM471.8,284.6s2,-1.2 2.1,0q-0.5,0.5 -0.8,0.7c-0.3,0 -0.8,-0.8 -1.3,-0.7zM471.7,288.5s1.2,-1 1.6,-0.4c0.5,0.8 -0.4,0.7 -0.4,0.7zM470,266.6s-1.5,-0.6 -2.1,0.5c1,0.2 1.3,0.5 1.8,0.9 -0.2,-0.5 -0.4,-1.2 0.3,-1.4zM467.1,274.9s0,-1.9 1,-2.6q0.5,0.4 0.8,1.2c-0.6,0.2 -1.4,0.2 -1.8,1.4zM471,273.6s-1.6,0.8 -1,1.7c0.5,-0.7 1.1,-0.5 1.1,-0.5v-1.2z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#e80000"
      android:strokeColor="#006b00"/>
  <path
      android:pathData="M468.7,255.8c-0.6,0.7 0.7,2.2 2.1,2 0.4,-1.6 -1.7,-2.5 -2,-2z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ffc900"
      android:strokeColor="#006b00"/>
  <path
      android:pathData="M470.2,254.6s0.3,1.3 1.5,1a4,4 0,0 0,-1 -1.9c0,0.3 0.1,0.8 -0.5,0.9zM471,258.3s0.7,0.8 2,-0.7c-0.6,0.1 -1.6,-0.4 -1.6,-0.4s0,1 -0.4,1.1z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#e80000"
      android:strokeColor="#006b00"/>
  <path
      android:pathData="M468.6,252.8c-0.5,0.7 0.3,1.7 1.7,1.5 0.3,-1.7 -1.3,-2 -1.7,-1.5z"
      android:strokeLineJoin="round"
      android:strokeWidth=".6"
      android:fillColor="#ffc900"
      android:strokeColor="#006b00"/>
  <path
      android:pathData="M469.2,246.3s5.5,-1.6 8.4,0.8c3,2.3 3,0.2 3,0.2s4.2,1.6 5.4,1.2c1.2,-0.3 -0.9,0.2 0.8,-0.9 1.7,-1 -3.4,0.3 -3.8,-1.9 -0.8,-1.3 0,-3 -1.7,-2.4 -1.2,-1.6 0.8,-2.7 0.3,-4.4 -1.2,1 -1.9,-0.3 -2.9,2 -2.3,-0.5 -0.3,-3.8 -2.9,-4.1 0,2.3 -1.9,2.4 -2,3.8 -1.1,0.8 -6,3.7 -4.6,5.7">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="508.95"
          android:startY="235.58"
          android:endX="509.04"
          android:endY="248.54"
          android:type="linear">
        <item android:offset="0" android:color="#FFB50000"/>
        <item android:offset="1" android:color="#FFFFC500"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M406,425c3.6,-1 22.9,4.2 30.3,12.2 -1.6,-14.3 -5.5,-25.1 -5.5,-25.1s-11.5,-3.3 -13,-1.7a81,81 0,0 0,-11.8 14.5zM400.1,359.3q-2,0.1 -4,3.4c-1.6,3.8 -2.7,13.5 -5,15.8 -2.2,2.2 -4.3,2.4 -4.3,4.5 0,2 0.3,6.7 6.3,8.5 6.1,0.3 15.8,-9.7 15.8,-9.7s5,-5.4 7,-11.2c-11.7,4 -20,-6.8 -15.8,-11.3zM569.7,422.7a53,53 0,0 0,-30.4 12.1c1.6,-14.1 5.5,-25 5.5,-25 1.3,-0.3 11.3,-3.2 12.8,-1.6 2.3,2.4 9.7,9.7 12.1,14.5zM575.2,357.4c1.3,0.2 1.8,2 3,4.2 1.5,3.9 3,9.4 5.3,11.6 2.3,2.3 4.3,5.6 4.3,7.6s-0.5,4.8 -6.5,6.6c-6,0.2 -14.8,-7.6 -14.8,-7.6s-5,-5.3 -7,-11.2c11.7,4 19.2,-6.3 15.7,-11.2z"
      android:fillColor="#c01500"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M393.4,412.5s12.1,8.4 12.4,12.2c32.7,-48.5 116.1,-63.6 163.7,-3.4 6.3,-8.3 12.8,-11 12.8,-11 -50,-65.4 -149.7,-57.3 -189,2.2z"
      android:fillColor="#fb0"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m517.4,227 l4.8,-0.1 -8.5,10.8 10.2,11.9 -20.1,25.3 19,22.8a58,58 0,0 1,-7.8 14l-10.9,-12L524,275l-16.2,-18.4zM449.3,229.2h-5l9.5,10.4 -10.1,12.6 21,22.8 -17.4,23.3c2,5 5,10.8 8.3,15l10.3,-13.1 -21.2,-23.6 15.5,-19.7z"
      android:fillColor="#1e5aa6"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m473,290.4 l-7.6,10 26.8,31c4.8,-2.4 8.5,-5.1 12.5,-8.4l-14,-16.1 5.7,-16.7 8.2,9.4 -25.5,32.6q-6.6,-2.7 -13.3,-8.2l13.5,-17.1zM464.7,274.9 L469.5,281 466.4,272.7zM499.6,280.3 L503.6,275.1 502.1,273.1z"
      android:fillColor="#1e5aa6"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M556,195s4.8,-4 8.1,-4"
      android:fillColor="#00000000"
      android:strokeColor="#fff700"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M556.7,194.5c0.1,-0.1 28.1,-4 29,-5m-28.9,5.2 l31.4,-2.7m-31.5,2.8s33.3,-1.5 35.3,0.2"
      android:fillColor="#00000000"
      android:strokeColor="#fff700"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M556.6,194.7s32.6,0.3 33.2,1m-33.1,-1 l31.4,2.6m-31.7,-2.3s32,2.8 35,6.8"
      android:fillColor="#00000000"
      android:strokeColor="#fff700"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M593.1,207.5c0,-0.1 -10.8,-12.9 -36.8,-12.7"
      android:fillColor="#00000000"
      android:strokeColor="#fff700"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M556.4,195s18,1.3 23.7,7.6"
      android:fillColor="#00000000"
      android:strokeColor="#fff700"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M556.7,194.8s11.7,-2.2 23.9,12.5"
      android:fillColor="#00000000"
      android:strokeColor="#fff700"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M549.4,208.2s16.1,0.6 16.1,2.2 -12,4.6 -12.2,11.2c-0.1,6.7 9.2,7.2 9.9,15.6 0.6,8.4 -7.3,9.6 -9,11.9 -1.5,1 -5.3,13 -4.8,20s2.5,30.5 6.2,35.3c2.8,2.2 7,9.3 11.7,7 4.6,-2.1 1.4,-10.2 1,-12.4 -0.5,-2.2 1.8,-5.9 1.8,-9.2 0,-3.4 -1.7,-6 -1.5,-6.8s12.8,3 12,15.5 -5.9,8.7 -5.9,8.7 1.6,15.4 -2.3,17.4c-7.2,3.8 -12.4,-0.8 -12.4,-0.8l0.7,3.2 -5.5,-2.8s-7,-10 -8.5,-14.5a126,126 0,0 1,-2.9 -28.5c0.7,-4.3 1.1,-29.3 0.8,-30.6 -0.3,-1.2 -1.6,-22.1 -0.8,-25.3s5.7,-17.1 5.6,-17.1z"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="565.89"
          android:startY="243.5"
          android:endX="587.04"
          android:endY="240.75"
          android:type="linear">
        <item android:offset="0" android:color="#FFD5DFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M532,204s11.2,-10.9 17.7,-9.6c3.3,0 0.2,2.4 0.2,2.4s5.7,0.4 6.5,3c0.1,1 -2.7,1.4 -2.7,1.4s2.3,0.5 2.5,2.5c0.2,2.1 -24,0.5 -24.3,0.4z"
      android:fillColor="#ff7000"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M532.6,203.6s12,-1.6 17.1,-6.7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M544.8,200.3s8.9,-0.4 8.9,1"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M576.3,329.9s7.6,-1.8 9.7,-4.5c1.2,-1 7.9,10 -9.7,4.5z"
      android:strokeLineJoin="round"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M574.3,267s1,5.8 -1.8,9.8c-1.4,1.6 -5.7,4.3 -5.7,6.2s1.6,4.4 1.3,6.6 -2.7,4.5 -2.6,6.4c0,1.9 2.7,12 2.4,12.2m-7.6,-60.2s-6,2 -7.3,8"
      android:fillColor="#00000000"
      android:strokeColor="#fff"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M557.8,200.3s2,5.7 8.7,0.3c-4.3,-5.9 -8.7,-0.2 -8.7,-0.3z"
      android:fillColor="#c75b00"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M562.7,200q0,0.5 -0.7,0.6t-0.7,-0.6 0.7,-0.5q0.6,0 0.7,0.5"/>
  <path
      android:strokeWidth="1"
      android:pathData="M503.4,187s12.6,13.7 20.7,13.5c1.5,4 -3.7,7.8 -5.7,9.5 -4.2,-1.3 -8,0.3 -17.6,-11.6 0.6,-7.6 2.7,-11 2.6,-11.4zM520.4,155.2c1.7,-5 5,-9 7.8,-9.5 -0.8,-3.8 5.9,-20.9 25.3,-27.4 1.2,8.7 -8.3,17.4 -8.3,17.4s28.7,-5 34.5,-12.2c-0.6,3.2 -6.4,23.6 -37,23.4 11.6,11 -3.7,19.8 -10.2,17.1 12,-9.2 -3.5,-14.7 -12,-8.8z"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M530.2,148.1c5.8,-3.7 7.9,-3.7 12.7,-3l-3.5,0.6s-0.3,0.5 1.8,2.4c-2.4,-0.6 -4.4,-2 -11,0z"
      android:fillColor="#cccccd"
      android:strokeColor="#ccc"/>
  <path
      android:strokeWidth="1"
      android:pathData="M528,145.7s11.4,-6 17.8,-10.2"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M518.2,210s15.7,4.3 17.5,-18.2c-3.4,-9.8 -8.1,-31 -0.9,-37 -6.6,-4.6 -14,0.2 -14,0.2 -0.4,1 -6.3,9.5 1.7,24.3 -19,-5 -11.3,13 -11.3,13 0.8,-3 10.9,-5.6 13.4,8.8 1,3.6 -6.8,9 -6.4,9z"
      android:fillColor="#00f"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M535.9,192s16.6,-9 16.2,-30c-14.2,0.4 -19.2,18.6 -19.2,18.6z"
      android:fillColor="#00f"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M461.3,190.9s-12.3,10.7 -19.3,7.3c-5.1,3 -11.2,-2.4 -11.2,-2.4s7,26.3 32.6,7.3a61,61 0,0 0,-2 -12.2z"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M441.9,197.7c1,-5.5 5.3,-8.2 8.8,-3 4.6,1 9.2,-18 -7.2,-14.8 4.6,-24.7 -9.2,-33.9 -9.2,-33.9s-4.9,27.4 -2.6,32.4c2.2,5 -3.3,-9.3 -21,-13.4 -0.3,20.7 19.8,30.3 19.8,30.3s5.6,5.2 11.4,2.4z"
      android:fillColor="#00f"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M443.5,179.7s-7.7,6.6 -6,15.5m-5.7,-16.3s-1.8,6.3 2,15.4"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M430.6,196.4s3.6,-5.8 11.3,1.6"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M432.1,156.3c0,-0.2 -11.7,0 -4.8,11.9 -5.1,1.6 -16.7,-6 -8.7,-16 -26.3,-0.7 -37,-13.3 -37,-24.9 7.7,8 26.2,4.8 32.7,9.7 -8,-7.4 -6.4,-17.7 -6.4,-17.7s22.4,6.7 26.4,26.6c-1.3,3.9 -1.9,10.7 -2.2,10.4z"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M414,136.5c4.4,4.4 15.5,5.7 21.1,10.6"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M419.2,149.5s9.2,-1.2 11.9,2c-3.6,0 -4.8,-0.9 -10,1 1.4,-0.7 1,-2 1.8,-2s-3.3,-1 -3.7,-1z"
      android:strokeWidth=".7"
      android:fillColor="#cccccd"
      android:strokeColor="#ccc"/>
  <path
      android:strokeWidth="1"
      android:pathData="M465,77.5s5.1,-4.5 10.6,-0.5c-2.5,7.4 -11.4,4.6 -11.4,4.6s0.3,3.7 -0.6,5.5c1.8,1.4 3.2,5.8 3.2,5.8s9.2,-2.2 11.2,1.8c3.5,-0.5 6,0 6,0s7.2,-1.8 9.7,-1.8 10.5,2 11.3,3.6c0.7,1.4 3.4,11.4 5.2,11.2 1.8,-0.1 -4.3,2.4 -6,-0.1 -1.6,-2.5 -1.1,3.3 -1.1,3.3s5,5.3 5.6,6.5 -3.1,10.8 -0.3,17.5c-2.5,0.2 -2.7,2.8 -2.7,2.8 -0.1,3 -3.8,3.7 -3.8,3.7l-1,-4 -2.4,1.5 1,-3.1s3.4,-8.3 3.7,-11 -3.1,-7.4 -5.8,-7.4 -4.6,8.5 -4.6,8.5 -1.3,6.3 -0.9,7l-1.8,-2.1s-1.1,3.8 -2,5c-1,1.2 -2.9,1.6 -2.9,1.6s-1.3,-3.8 -0.8,-5.3 7.2,-7.3 6.6,-11.4 0,-3.2 -0.1,-3.3c-0.2,-0.2 -3.6,-3.1 -3.7,-4.8s-4.5,2.1 -10.1,1c-1.7,3 -2,10.5 -2,10.5s-0.5,9 0.6,10c1.2,1.1 -3,3.3 -3,3.3l-2.9,4 -1,-2.3 -2.1,1.5 1.2,-3c-0.1,-2.3 2.9,-8.4 2.9,-13.6s0.3,-10.8 0.3,-10.8 -5.4,-0.3 -5.3,5.4c0.1,5.6 -1.3,6 -1,7.5 0.3,1.7 1.7,6.3 1.3,7.6 -0.4,1.4 -2.2,1.8 -2.2,1.8l-0.5,0.7s-5.5,2.6 -5.3,3.6l-0.2,-3 -0.3,-4.3s3.3,-2 3.3,-7.2 -0.7,-6 -0.6,-7.2 0.9,-5.5 0.7,-6c-0.1,-0.4 -3,1.3 -4,1.3s1.7,-3.1 2,-5.4c0.3,-2.2 -3,2.1 -5.7,-0.4 1.3,-2.7 3.1,-3.6 3.4,-5.6 0.3,-2.1 -2,1.7 -4.1,0.2 0.2,-2 2.2,-3.7 2.2,-3.7s-1.7,-0.2 -2.4,0c-1.3,-0.4 1.5,-2.5 1.7,-5.5s-1.7,-4.1 -1.7,-4.3c0,-0.1 -3,-2.6 -3.4,-3.7 -0.4,-1 -0.4,-2.2 -0.4,-2.2s-5,3.6 -10.7,-4c5.3,-4.5 11,-1.2 11,-1.2s1.5,-4.2 8.3,-3.9 8.2,4.2 7.9,3.8z"
      android:fillColor="#923f00"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M502.4,154.2c0.1,0 12.6,0.4 12.7,7.1 0.2,6.7 -3.8,5 -4,5l-9,-1.2z"
      android:fillColor="#00f"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M490.3,152.4s15.6,-0.1 14.7,6.7 -4.8,5.4 -4.8,5.4l-7.6,-0.6z"
      android:fillColor="#fffeff"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m481.5,151.7 l9,0.7s5.5,0.7 5.2,6c-0.2,5.1 -5.5,5.2 -5.5,5.2l-8.8,-0.5z"
      android:fillColor="#00f"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M460.4,154.2c-0.2,0 -12.6,0.4 -12.8,7.1 0,6.7 3.9,5 4,5l9.1,-1.2z"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M472.5,152.4s-15.6,-0.1 -14.7,6.7c0.8,6.8 4.8,5.4 4.8,5.4l7.5,-0.6z"
      android:fillColor="#00f"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m481.3,151.7 l-9,0.7s-5.5,0.7 -5.3,6c0.3,5.1 5.6,5.2 5.6,5.2l8.8,-0.5z"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M505.4,226.7s6.5,-9.5 9.3,-8.5c2.4,0.8 0.5,8.2 -0.6,8.9zM461,228.4c-2.1,-2.8 -5,-10.7 -7.8,-8.5 -2.4,0.8 -0.6,8.2 0.6,8.9z"
      android:fillColor="#5e0043"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M465.2,209.5s12.7,7.8 16.8,8 17.3,-10.1 17.3,-10.1"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m466.5,179 l2.4,-3 12.1,6 12.4,-5.5 2.4,2.6 -13.8,9.7z"
      android:fillColor="#5e0043"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M456.8,178.8a51,51 0,0 1,10.3 18.7c1.4,7.9 -0.8,-10.9 -0.8,-10.9s9.7,4.8 10,7.8 4.6,-0.2 4.8,-0.7l-28,-18.2zM504.8,178.3s-10.2,13.4 -9,28c-2.1,-7 -0.4,-18.5 -0.4,-18.5l-2.2,1.4s-2.4,9.9 -5.3,11.4q-0.6,-1.7 -0.4,-1.6c0.2,0.1 -3.1,4 -3.7,4.4s0.2,13.7 0.2,13.7 1,9.6 2.2,9.5l-3,1.7 -1.3,-25.6 2.9,-2.9s3.8,-4.7 4.1,-9.2c-1.6,1.4 -3.5,1.8 -3.5,1.8s-0.5,6.5 -2,7.4c-1.6,1 -1.6,2.6 -1.6,2.6l-0.4,-8.5z"
      android:strokeLineJoin="round"
      android:fillColor="#474747"
      android:strokeColor="#474747"/>
  <path
      android:strokeWidth="1"
      android:pathData="m481.4,193.8 l2,40.5"
      android:fillColor="#00000000"
      android:strokeColor="#000"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M456.5,213.2s4.7,2.7 6.4,11c15.2,-1.1 20,4.2 20,4.2s12.4,-6 18.2,-4.8c2,-4.3 7.8,-9.7 7.8,-9.7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m450.2,173.5 l31.2,20.4 28.2,-18.9s5.8,-3.2 5.3,-5.5c-0.5,-2.4 -2.5,-1.5 -3.5,-1 -1,0.3 -29.5,20.5 -29.5,20.5l-30.2,-19.2s-2.2,-0.7 -2.6,0.8c-0.4,1.4 0.8,2.2 1.1,2.9z"
      android:fillColor="#b4b6b9"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M506,168.2s-8,-3.4 -8,-0.5c0,3 0.3,3.3 1.6,5.2 1.3,1.8 -1,3 -1,3l-0.8,-2c-0.4,-1.3 -4.7,-2.2 -5,-3.8 -0.5,-1.5 0.8,-3.9 -1.7,-4.2 -2.6,-0.3 -5.1,1 -5.6,4a61,61 0,0 1,-3.9 9.8l0.5,-15.6c5.6,0.4 16,1.6 24.1,2.6 -2.4,-0.4 1,0.1 0.8,1 -0.2,0.6 -1,0.7 -1,0.5zM477.8,164.3a37,37 0,0 0,-10.4 1.9c-1.8,1 3,2.7 2.3,4 -0.6,1.5 -0.7,4.4 -3,3.8s-9.8,-4.4 -10,-5.7c-0.3,-1.2 -1.8,-1.3 -1.8,-1.3s21.8,-3 22.9,-2.7z"
      android:strokeLineJoin="round"
      android:fillColor="#474747"
      android:strokeColor="#474747"/>
  <path
      android:strokeWidth="1"
      android:pathData="M481.2,162.5v19.1m-14.7,-41.7 l-9,14.6"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M482.6,139.5s3.8,5.2 3.4,6.7c1.5,1.2 3.3,6 3.3,6"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="512.72"
          android:startY="145.05"
          android:endX="520.35"
          android:endY="145.05"
          android:type="linear">
        <item android:offset="0" android:color="#FF474747"/>
        <item android:offset="1" android:color="#FFFF5500"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M497.3,138.7s-8.2,11.6 -8,12.5"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M455.1,83.4q-0.5,1 -2.3,0.6 -1.8,-0.7 -1.7,-1.8c0.2,-0.7 1.3,-1 2.4,-0.6 1,0.3 1.8,1.1 1.6,1.8m3.4,-0.2q0.5,1 2.3,0.5t1.7,-1.8c-0.2,-0.6 -1.3,-0.9 -2.4,-0.5 -1,0.3 -1.8,1.1 -1.6,1.8"/>
  <path
      android:strokeWidth="1"
      android:pathData="M555,324s-3.2,0.5 -3,0.8c0,0.3 -9,0.6 -9.1,0.3 -0.2,-0.3 -1.4,1.5 -1.4,1.5l1.5,-0.9s2.2,2.3 2.8,2.1c0.7,-0.1 -0.3,1 0,1.1 0.1,0.2 0.8,-0.4 0.8,-0.4l14.8,-0.2zM560.4,328.5 L548.4,328.9s-3.1,2.8 -3.3,3.4 2,0.9 2,0.9l0.6,2.3 1.6,-0.6s10.3,1.5 19.7,-0.6c4.8,-1.6 5.7,-3.6 2.3,-4.9 -3.3,-1.2 -10.8,-0.8 -10.9,-0.9z"
      android:strokeLineJoin="round"
      android:fillColor="#ff7000"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m519,353.4 l21.7,-1 -4,-3.4 59,-2.4 -2,-4.7 -66.1,2.7 8,3.7 -18,0.5 0.6,1.9 -4.7,-0.2s5.3,2 5.4,3z"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="623.09"
          android:startY="350.02"
          android:endX="544.33"
          android:endY="339.03"
          android:type="linear">
        <item android:offset="0" android:color="#FFD5DFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="M543.4,232.9c-1.7,-0.9 -9.4,-2 -17.9,3.6l0.4,19.6s12.5,-6.7 18.5,-5.2c-0.3,-6 -0.3,-13.8 -1,-18z"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="571.02"
          android:startY="251.4"
          android:endX="564.31"
          android:endY="234.43"
          android:type="linear">
        <item android:offset="0" android:color="#FFD5DFFF"/>
        <item android:offset="1" android:color="#FFFFFFFF"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:strokeWidth="1"
      android:pathData="m422.7,292.4 l-51.4,51m74.4,-50.7 l-41.8,55m39.9,-69.3 l-55.7,67.5m-1.7,-0.9 l5.4,-6.4m62.6,-26.3 l-21.6,29.8m-0.9,3.9 l0.5,11.4m31.9,-34.7 l-23,28.8m18.9,-2.3 l9.3,-15.3m-5.7,-2.9 l-11.2,12.6m7.8,-24.6 l-7.6,9.3m-2.3,-22.9s-23.3,34.1 -22.9,37m20.5,-41.4c-0.6,0.3 -20.1,26.2 -20.1,26.2m-1.1,8.2 l-3.6,4.3m-4.5,6.5 l-5.4,7.1"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M420.2,213.8s-1.6,5 0,7.5 11.5,22.5 11.5,22.5 6.8,-8.3 9.3,-8.5c2.4,-0.3 1.3,21.6 1.3,21.6s-4,3.8 -6.5,3.6 6,8.6 5.8,16 -11.2,44.5 -15.3,45.1c-4,0.7 1.8,-6.7 1.6,-8.8 -0.3,-2 -1.4,-0.6 -2.3,-2.9s1.4,-5.6 1,-7.9c-0.5,-2.2 -2.6,-1.8 -2.8,-3.4s1.4,-2 1.1,-3.8 -2.7,-1.4 -2.4,-3c0.2,-1.5 0.4,-0.8 0.2,-3.8 -0.2,-2.9 -0.7,2 -3.2,2.3s-4.5,5.9 -4.5,5.9 -5,7 -9.9,3.8c3,6.3 0.7,9 -0.5,9.2 -1,0.3 1,5 -1.8,5.2s2,10.6 -1,11.5c3.3,1.6 0.6,3.6 0.6,3.6s-7.8,0.7 -6.1,10.9c-23,-8.2 -34.3,-22.1 -34,-36.4s4.7,-27 15.7,-31.7c3.2,-11.8 8.6,-24.4 8.6,-24.4s-0.9,-5.2 -0.2,-8.6a17,17 0,0 1,3.8 -6.7s-0.4,-8.2 -0.2,-12.2 1.8,-5.9 2,-8.1c0.2,-2.3 -0.7,-13.8 1.6,-15.8s6.5,-1.8 8.8,-3.2c2.2,-1.3 5.2,-3.8 8.1,-3.6 3,0.2 5.4,2.3 5.4,2.3s11,0 11.7,4.2 -2.2,6 -2.2,6 1.6,6 -5.2,11.6z"
      android:fillColor="#8a9396"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M410.4,198.2c0.3,0.7 -0.5,1.8 -1.8,2.5s-2.7,0.8 -3,0.2q-0.4,-1.2 1.7,-2.5c1.4,-0.8 2.7,-0.8 3,-0.2z"
      android:fillColor="#cecfcf"
      android:strokeColor="#2b2b2b"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M408.5,199.4q-0.1,0.7 -0.8,0.7t-0.9,-0.7q0,-0.8 0.9,-0.8 0.7,0 0.8,0.8"/>
  <path
      android:pathData="M425,206.8s2.7,12.2 -0.5,20.7m1.4,-26s5.3,7 4.6,15.8m-4.1,-17.3c0.1,0 4.5,3.7 4.3,6.3m-3.3,-7.9s3,2 3.6,4.1"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M426,197.2s-10.5,17 -9.1,27.7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M426.7,196.6s-14,8.3 -16.5,35.4"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="m425.2,201.6 l-5.6,4.2"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M405,233.5s7.8,-32.2 21.5,-36.9"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M431.9,244.4s7.4,-9.3 9.1,-9 1.2,21.6 1.2,21.6 -5.5,4.1 -6.7,4c-1.2,-0.2 6.2,10 6,13.7 -0.1,3.8 -0.3,3.8 -0.3,3.8s0,-2.2 -1.7,-5.8c-1.8,-3.7 -0.7,-7.8 -12,-17 -2.7,-5.6 5.6,3.5 7.3,2.1s-3,-13.2 -3,-13.4z"
      android:fillColor="#2b2b2b"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M418.7,192s-3.1,0.8 -3.5,2c-0.5,1.4 -2.3,2.6 -3.3,2.2s-2.8,-2 -2.8,-2"
      android:fillColor="#00000000"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M401,202.9s-4.7,4.6 -2.4,5.2 4.7,-3.7 4.7,-3.7 0,7.3 2,6.1c2,-1.1 7.7,-5.2 7.7,-5.2s2,-0.2 2.5,0c0.4,0.1 5.3,4.4 8.6,2.8 -1.9,4.8 -4,5.4 -4,5.4s-3.4,4.4 -7.8,3.4 -5.5,-2.8 -5.5,-2.8 -3.7,0.2 -4.8,-1.5l-1.6,-2.6s-2,2 -2.7,1c-0.5,-1 0,-6.7 3.3,-8.1z"
      android:strokeLineJoin="round"
      android:fillColor="#2b2b2b"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M427.5,196.4s-8.6,-2.6 -11.6,1.7 -2.3,6.6 -0.7,7"
      android:fillColor="#00000000"
      android:strokeColor="#2b2b2b"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M428.8,196.1c0,1 -0.7,1.9 -1.6,1.9s-1.6,-0.9 -1.6,-1.9q0.1,-1.7 1.6,-1.8 1.5,0.2 1.6,1.8"/>
  <path
      android:strokeWidth="1"
      android:pathData="M403.7,218.2s3.5,5.4 7.7,6.9 -3,2.7 -7.1,0c-3,-4.2 -2.2,-7.2 -2.2,-7.2s0.8,-0.8 1.6,0.3zM433.8,250.1s-9.9,-13.7 -12.7,-14.5c-2.9,-0.7 2.2,-1.3 5.2,1.6s-0.8,-4.7 -0.8,-4.7z"
      android:fillColor="#2b2b2b"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M402.7,329.6c3.8,-1 20.4,9.5 24,12.1 3.8,2.7 11.6,1 11.6,1s-3.6,2.2 -5.8,2.7c-2.3,0.5 6.6,0.5 6.6,0.5s-21.1,5.9 -42.6,-5.4c-2,-8.8 4.6,-10.8 6.2,-10.9z"
      android:strokeLineJoin="round"
      android:fillColor="#2b2b2b"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M427,253.5s-2.9,-0.5 -4.3,-2.1 -3.4,-5.7 -5.5,-7.4c-2.1,-1.6 -12.6,-7.4 -16.4,-7s-5,-0.4 -5.5,-0.9 -2,0.2 -1.7,2.2 -3,6.3 -1.8,8.3a62,62 0,0 0,7.7 10.6c1,0.3 0.4,4.8 0.4,4.8s4.8,4.9 6,5.1c1.2,0.3 2.5,1.2 2.3,2.4 -0.1,1.1 -5.3,7.4 -5.3,7.4s-5.5,3 -5.6,4.5c0,1.7 1.4,5 5.8,6.1a41,41 0,0 0,16.8 -0.8c0.6,-1 1.6,-7 1.2,-7.7 -0.4,-0.6 -3.3,-2.4 -4.8,-2.2 -1.5,0.3 -2.9,1.5 -2.7,1.8s-2.1,1.4 -2.1,0.3 4.4,-6 4.8,-5.5 6.6,1 7.8,4.1c1.1,3.1 1.1,5.4 4.4,5 3.2,-0.3 7.8,-3.3 8.2,-9.5a13,13 0,0 0,-4.7 -11c-1,-0.7 -4.4,-2.7 -4.7,-3.7s-1,-4 -0.4,-4.8z"
      android:fillColor="#2b2b2b"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M368.3,281s10.5,-2.7 13.2,-2.6c2.7,0 13,4.8 16,7.7s9,9.8 13,9.2 5.2,-1.4 5.2,-1.4l-1.6,3s-3.2,0.9 -4.9,0.5c-1.6,-0.4 -4.8,-1.5 -8,-4.6 -3.1,-3 -13,-11.5 -21.3,-10.9s-13.1,8.8 -13.1,8.8 0,-4 0.4,-4.9c0.3,-0.8 -1.8,2 -1.8,2z"
      android:fillColor="#2b2b2b"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M407,289.2s2.6,0.7 7,0.7c4.3,0 6.6,-1.9 6.6,-1.9l-0.3,-1.7 0.7,-2.3s3.3,3.3 3.3,4 -1.4,1 -1.4,1l-0.5,-1.7 -1.1,1.5s-6.3,5 -9.8,4.2c-3.4,-0.7 -6.6,-3 -5.7,-3.5s1.3,-0.3 1.2,-0.3zM391.7,298s-3.8,0 -5.5,0.8 -2.3,1.8 -3.5,1.7c-1.1,-0.2 -2,-1.6 -1.7,-2.2 0.5,-1 2.8,-2.2 7,-1.9s3.7,1.6 3.7,1.6zM405,308.3c0,-0.2 -0.3,-5.5 -2.4,-7.7a6,6 0,0 0,-5.8 -1.9c-1.2,0.3 4.2,2.7 4.8,4.2 0.5,1.6 2.3,6 1.8,7s-1.3,-3 -4.7,-4.2c-3.3,-1.2 -7.9,-0.5 -7,1 1,1.4 4.7,0 6.6,3.3a60,60 0,0 1,3.3 6.5l0.7,-2.3 1.6,-0.4 0.3,-4.4z"
      android:fillColor="#2b2b2b"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="m402.3,325.7 l-3.5,-7s-1,-4.3 -4,-5.5 -6.8,-1 -6.8,0.4c-0.1,1.5 6.2,3.4 6.6,4.2s0.2,2.2 -0.3,2.3 -3.4,-1 -4.8,-0.7 -2.2,2.2 -4.3,1.6c-2.1,-0.5 -3.9,-7.2 -3,-8 0.7,-0.6 -1.6,1.3 -2.1,-0.5 -0.5,-1.7 0.7,-7.2 -0.1,-7.9s-4.8,-3 -4.8,-3.5c-0.1,-0.5 0.2,-27 22.3,-5 -9.5,-11.3 -13.2,-10 -15,-10.1 -1.2,0 -9.9,0.7 -12,11.7 -2.2,11 -4.8,4.1 -4.8,4.1s-0.4,4.8 1.9,6.1c2.3,1.4 -1.1,5.3 -1.1,5.3s-4.2,-10 -3.5,-14.5c-0.9,3.5 -0.8,12.3 4.4,21.8a71,71 0,0 0,27.2 19.1c8,-12.3 7.7,-13.8 7.7,-14z"
      android:strokeLineJoin="round"
      android:fillColor="#2b2b2b"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M387.8,325.8s4.4,0.5 5.6,2.8c1.3,2.3 1.8,5.8 1.8,5.8 0.8,-1.5 1,-2.7 2.6,-3.8 1.5,-1.1 2.6,-1.3 2.5,-2 0,-0.9 -4.8,-5.8 -7.1,-6.1 -2.4,-0.3 -6.5,2.6 -6.5,2.6s-0.8,1 1,0.7z"
      android:strokeLineJoin="round"
      android:fillColor="#8a9396"
      android:strokeColor="#2b2b2b"/>
  <path
      android:strokeWidth="1"
      android:pathData="M398,337.4s11.6,5.3 30.6,5.6"
      android:fillColor="#00000000"
      android:strokeColor="#8a9396"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M436.7,283.6s-1,9.7 -8.5,29"
      android:fillColor="#00000000"
      android:strokeColor="#2b2b2b"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="M435.2,289.6s-2.3,7 -8.5,12.2"
      android:fillColor="#00000000"
      android:strokeColor="#2b2b2b"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="m401.3,410.3 l2,-2.2 0.6,0.5 -0.1,0.2 -0.2,0.3v0.2l0.2,0.2 2.6,2.3 0.2,0.2h0.2l0.1,-0.2q0.4,-0.4 0.5,-0.8 0.2,-0.4 0.1,-0.8 0,-0.5 -0.4,-1l0.8,-0.4 1.2,2 -3.2,3.6 -0.6,-0.5 0.2,-0.3 0.1,-0.2v-0.2l-0.2,-0.2 -2.6,-2.3 -0.3,-0.2h-0.1l-0.3,0.2 -0.2,0.2zM405.7,405.4 L409.4,401.8 411.2,402.7 410.9,403.4 409.9,403.2 409.3,403.4q-0.3,0.1 -0.6,0.5l-0.7,0.6 1,1.1 0.3,-0.2q0.3,-0.2 0.3,-0.4v-0.2l-0.2,-0.3 -0.2,-0.2 0.6,-0.6 1.8,1.9 -0.6,0.5 -0.2,-0.1 -0.3,-0.3h-0.2l-0.4,0.3 -0.2,0.2 1,1 0.2,0.2h0.1l0.2,-0.2 0.4,-0.3 0.6,-0.8q0.2,-0.4 0.1,-1 0,-0.4 -0.3,-0.9l0.8,-0.4 1,2.3 -3.8,3.7 -0.6,-0.6h0.2l0.2,-0.4v-0.2l-0.2,-0.3 -2.4,-2.4 -0.3,-0.2h-0.2l-0.2,0.1 -0.2,0.2zM413.4,398q1.2,-1 2.4,-0.9 1.3,0.1 2.2,1.2 0.6,0.7 0.8,1.5t-0.1,1.6 -1.1,1.5q-1,0.9 -2.2,0.9 -1.4,0 -2.4,-1.2 -1,-1 -0.8,-2.3t1.2,-2.3m0.7,0.7q-0.5,0.5 -0.5,1.2 0,0.8 0.7,1.6t1.5,1q0.6,0 1.1,-0.4t0.6,-1q0,-0.9 -0.8,-1.7 -0.4,-0.5 -1,-0.8 -0.4,-0.2 -0.9,-0.2t-0.7,0.3m6.1,-6.2 l0.5,-0.3 0.1,0.1 2.9,-2v-0.2l0.4,-0.3 1.9,1.3 -0.5,0.7 -0.7,-0.3q-0.4,-0.2 -0.8,-0.1l-0.4,0.1 -0.1,0.1v0.2l2.1,3 0.2,0.3h0.3l0.5,-0.3 0.4,0.7 -2.4,1.8 -0.5,-0.7 0.1,-0.1 0.3,-0.3v-0.2l-0.1,-0.3 -2.1,-3 -0.2,-0.2 -0.1,0.1q-0.2,0.1 -0.3,0.4 -0.2,0.3 -0.2,0.7l0.1,0.8 -0.8,0.2zM425.8,389 L430.2,386.2 431.9,387.5 431.3,388.2 430.5,387.8h-0.7q-0.3,0 -0.7,0.3l-0.8,0.5 0.8,1.3 0.2,-0.2 0.4,-0.2v-0.3l-0.1,-0.4 -0.1,-0.2 0.7,-0.4 1.3,2.3 -0.7,0.4 -0.1,-0.3 -0.3,-0.3h-0.2l-0.4,0.2 -0.3,0.1 0.8,1.3 0.1,0.2h0.1l0.3,-0.1 0.4,-0.3 0.8,-0.6 0.3,-0.9v-1l0.8,-0.2 0.5,2.4 -4.6,2.8 -0.4,-0.7 0.1,-0.1 0.3,-0.3q0.1,0 0,-0.2v-0.3l-1.8,-2.9 -0.3,-0.3h-0.1l-0.3,0.1 -0.2,0.1zM432,385.2 L435.5,383.4 436.2,383.1q0.4,-0.2 0.9,-0.1 0.4,0 0.7,0.2l0.5,0.6q0.4,0.7 0.1,1.3 -0.2,0.6 -0.8,1l1.8,0.8h0.1l0.3,0.1h0.3l0.2,-0.1 0.3,-0.1 0.3,0.7 -1.7,1 -3,-1.5 -0.5,0.2 0.6,1.1 0.2,0.4h0.5l0.2,-0.2 0.4,0.8 -2.7,1.4 -0.4,-0.8 0.3,-0.1 0.2,-0.2 0.1,-0.2 -0.1,-0.3 -1.6,-3.1 -0.2,-0.2q0,-0.1 -0.2,0h-0.3l-0.2,0.1 -0.4,-0.7zM434.6,385 L435.3,386.4 436.1,386q0.5,-0.2 0.7,-0.5l0.2,-0.4v-0.5l-0.4,-0.3h-0.4l-0.7,0.2zM439,381.6 L442.6,380 443.3,379.8h0.9q0.4,0 0.7,0.2l0.5,0.7q0.3,0.6 0,1.2t-0.9,1l1.8,0.9 0.4,0.1h0.5l0.2,-0.2 0.4,0.8 -1.8,0.8 -3,-1.6 -0.4,0.2 0.5,1.1 0.2,0.4h0.4l0.3,-0.1 0.3,0.7 -2.7,1.3 -0.4,-0.8 0.3,-0.1 0.2,-0.2 0.1,-0.2 -0.1,-0.3 -1.4,-3.2q0,-0.2 -0.2,-0.3h-0.5l-0.2,0.2 -0.3,-0.8zM441.6,381.6 L442.2,383.1 443.1,382.7 443.8,382.3q0.2,-0.2 0.2,-0.5v-0.4l-0.3,-0.4h-0.5l-0.7,0.2 -1,0.4zM448.4,377.7 L449.7,377.2 453.1,381.1 453.5,381.4h0.5l0.2,0.8 -2.7,1 -0.3,-0.8 0.2,-0.1q0.2,0 0.3,-0.2v-0.2l-0.2,-0.2 -0.4,-0.5 -1.7,0.6v1l0.3,0.1 0.4,-0.1 0.3,0.8 -2.3,0.8 -0.3,-0.8q0.2,0 0.3,-0.2l0.1,-0.1v-0.4zM449.4,380.9 L450.5,380.5 449.4,379.1zM453.2,376 L455.9,375.2 458,378.5v-4l2.8,-0.7 0.2,0.8 -0.3,0.1q-0.2,0 -0.3,0.2v0.4l1,3.4 0.1,0.2 0.2,0.1h0.6l0.2,0.7 -2.9,0.9 -0.2,-0.9h0.2q0.3,0 0.3,-0.2l0.1,-0.1v-0.3l-1,-3.7 -0.1,5.4 -0.8,0.2 -2.8,-4.4 1,3.7 0.1,0.1 0.2,0.1h0.3l0.3,-0.1 0.2,0.8 -2.4,0.7 -0.3,-0.8h0.3l0.3,-0.2v-0.5l-1,-3.3v-0.3l-0.2,-0.1h-0.6zM465.6,372.7 L468.8,372.1h1q0.5,0 0.8,0.2t0.6,0.5l0.3,0.7q0.1,0.8 -0.4,1.4 -0.5,0.7 -1.5,0.9l-1,0.2 0.2,1.1v0.3h0.8l0.1,0.8 -3,0.6v-0.8h0.1l0.4,-0.2v-0.5l-0.6,-3.3v-0.4l-0.2,-0.1h-0.6zM468,373.3 L468.4,375h0.5l0.7,-0.3 0.3,-0.4v-0.5q0,-0.4 -0.4,-0.6 -0.3,-0.1 -0.8,0zM472.2,371.5 L476,371h0.8l0.8,0.2q0.4,0.2 0.6,0.5 0.3,0.3 0.3,0.7 0.1,0.7 -0.3,1.2t-1.1,0.8l1.4,1.3 0.3,0.2 0.2,0.1h0.6v0.8l-1.9,0.3 -2.3,-2.4h-0.5l0.2,1.2v0.4l0.2,0.2h0.6v0.8l-3,0.4v-0.9h0.2l0.3,-0.1 0.2,-0.2v-0.3l-0.5,-3.5v-0.3h-0.8zM474.6,372.2 L474.8,373.8h1l0.7,-0.3 0.4,-0.3v-0.5l-0.1,-0.4 -0.4,-0.2h-0.8zM483.4,370.3q1.4,-0.1 2.4,0.7 1,0.9 1,2.3 0,1 -0.3,1.7 -0.4,0.7 -1,1.2 -0.8,0.4 -1.8,0.5 -1.4,0 -2.3,-0.6 -1.1,-0.9 -1.2,-2.4 0,-1.4 0.8,-2.4t2.4,-1m0,1q-0.7,0 -1,0.6 -0.6,0.6 -0.5,1.7 0,1 0.6,1.6 0.4,0.5 1.1,0.5t1.1,-0.6q0.5,-0.6 0.4,-1.7 0,-0.7 -0.3,-1.2t-0.6,-0.7q-0.4,-0.3 -0.8,-0.2m4.6,-1.1h3.4l1,0.1 0.7,0.4 0.4,0.6 0.2,0.8q0,0.8 -0.6,1.3t-1.7,0.5h-1v1.5l0.4,0.1h0.3v0.9h-3v-0.9h0.5l0.1,-0.1v-0.4l0.1,-3.4v-0.4l-0.1,-0.1h-0.6zM490.3,371.2v1.8h0.6l0.7,-0.1 0.3,-0.3q0.2,-0.2 0.2,-0.5 0,-0.5 -0.3,-0.7t-0.8,-0.2zM494.8,370.2 L498.6,370.5 499.4,370.6 500.2,371q0.3,0.2 0.5,0.6l0.1,0.8q0,0.7 -0.6,1 -0.5,0.5 -1.2,0.6l1.2,1.5v0.1l0.2,0.3h0.2l0.3,0.1h0.3v0.9l-2,-0.1 -1.9,-2.8h-0.5v1.6h0.6v1l-3,-0.3v-0.8h0.6l0.2,-0.2v-0.3l0.2,-3.5v-0.3l-0.3,-0.2h-0.4zM497,371.4 L496.9,373h0.9q0.5,0.1 0.8,0 0.3,0 0.4,-0.2t0.2,-0.5v-0.4l-0.4,-0.3 -0.8,-0.1h-1zM502.4,370.8 L505.4,371.2v0.9h-0.3q-0.3,-0.1 -0.4,0l-0.2,0.4 -0.4,3.4v0.4h0.1l0.3,0.2h0.3l-0.1,0.9 -3,-0.4 0.1,-0.9h0.2q0.3,0.1 0.4,0h0.1l0.1,-0.3 0.4,-3.5v-0.5h-0.4l-0.3,-0.1zM508.9,371.7 L510.3,372 511.3,377q0,0.4 0.2,0.5l0.4,0.2 -0.2,0.8 -2.9,-0.5 0.2,-0.8h0.5l0.1,-0.1v-1l-2,-0.3 -0.3,0.6v0.2q-0.1,0.1 0,0.2h0.1l0.4,0.2 -0.1,0.8 -2.4,-0.4 0.1,-0.8h0.4l0.2,-0.1 0.2,-0.3zM508.1,375 L509.4,375.2 509.1,373.4zM514,372.6 L516.7,373.3 516.9,377.3 518.9,373.9 521.6,374.6 521.4,375.4h-0.3q-0.2,-0.2 -0.3,0 -0.2,0 -0.2,0.3l-0.9,3.4v0.4l0.3,0.1 0.3,0.1 -0.2,0.9 -2.9,-0.8 0.2,-0.8h0.3l0.3,0.1h0.2v-0.4l1,-3.7 -2.7,4.7 -0.8,-0.3 -0.3,-5.1 -0.9,3.6v0.2l0.1,0.2h0.3l0.3,0.1 -0.2,0.9 -2.5,-0.6 0.2,-0.9h0.3l0.3,0.1h0.1l0.2,-0.4 0.8,-3.4v-0.4l-0.3,-0.1 -0.3,-0.1zM526.5,376.1 L529.5,377.2 530.5,377.7 531,378.3 531.2,379 531.1,379.8q-0.3,0.7 -1,1 -0.8,0.3 -1.8,0l-1,-0.4 -0.4,1v0.5l0.3,0.2h0.3l-0.3,0.9 -2.9,-1 0.3,-0.9h0.2l0.4,0.2h0.1l0.2,-0.4 1.2,-3.2v-0.5l-0.3,-0.2h-0.2zM528.2,377.8 L527.6,379.5 528.1,379.7 528.8,379.8 529.3,379.7 529.6,379.2q0.1,-0.4 -0.1,-0.7t-0.6,-0.4zM532.8,378.4 L536.3,380 537,380.4q0.4,0.2 0.6,0.6l0.3,0.7 -0.2,0.8q-0.3,0.7 -0.9,0.9h-1.3l0.5,1.9 0.2,0.3 0.1,0.2 0.3,0.2h0.2l-0.3,0.9 -1.8,-0.8 -0.8,-3.3 -0.5,-0.2 -0.5,1.1v0.6l0.2,0.1 0.3,0.2 -0.4,0.7 -2.7,-1.2 0.3,-0.8 0.3,0.1 0.3,0.1h0.2l0.1,-0.3 1.4,-3.2 0.1,-0.3v-0.2l-0.2,-0.1h-0.2l-0.2,-0.1zM534.5,380.3 L533.8,381.8 534.7,382.1q0.4,0.3 0.7,0.3l0.5,-0.1q0.2,-0.1 0.3,-0.4 0.1,-0.2 0,-0.4l-0.1,-0.4 -0.7,-0.4 -1,-0.4zM543,383.2q1.3,0.7 1.7,1.9t-0.3,2.5q-0.4,0.8 -1.1,1.2 -0.7,0.5 -1.6,0.5 -0.8,0 -1.7,-0.5 -1.2,-0.6 -1.7,-1.7 -0.5,-1.3 0.3,-2.6 0.6,-1.3 1.9,-1.7t2.5,0.4m-0.4,0.9q-0.6,-0.4 -1.3,-0.1t-1.2,1.3q-0.5,0.9 -0.4,1.7 0.1,0.6 0.8,1 0.6,0.3 1.2,0t1.2,-1.2q0.4,-0.7 0.4,-1.2t-0.2,-1zM547.6,385.6 L548.1,385.9v0.2l3,1.9 0.1,-0.1 0.5,0.3 -0.5,2.2 -0.8,-0.2v-0.8q0,-0.4 -0.2,-0.7l-0.3,-0.4h-0.2l-0.1,0.2 -1.9,3 -0.2,0.4v0.1l0.1,0.1 0.4,0.3 -0.4,0.7 -2.6,-1.6 0.5,-0.7 0.1,0.1 0.4,0.2h0.1l0.3,-0.4 2,-3v-0.3h-0.1q-0.2,-0.2 -0.6,-0.2l-0.6,0.1 -0.7,0.4 -0.6,-0.6zM553.2,389.4 L557.4,392.4 556.8,394.4 556,394.2v-1l-0.2,-0.6q-0.2,-0.3 -0.6,-0.5l-0.7,-0.6 -1,1.3 0.3,0.2 0.4,0.2h0.3l0.2,-0.4 0.2,-0.2 0.6,0.5 -1.5,2.1 -0.6,-0.4 0.1,-0.2 0.2,-0.4v-0.2l-0.4,-0.4 -0.3,-0.1 -0.8,1.1 -0.1,0.3 0.2,0.3 0.4,0.3 1,0.4q0.4,0.1 0.8,0t1,-0.5l0.4,0.7 -2,1.4 -4.3,-3 0.5,-0.8 0.1,0.1 0.4,0.2h0.2l0.2,-0.3 2,-2.8 0.2,-0.3v-0.1l-0.3,-0.3 -0.2,-0.1zM560.1,398.2 L562.4,400.2 561.9,400.8 561.7,400.7 561.5,400.5h-0.2l-0.3,0.3 -1.2,1.5 -0.7,-0.6 0.1,-0.5 -1,-0.2 -0.7,-0.5q-0.7,-0.6 -1,-1.3 -0.2,-0.8 0,-1.6 0,-0.8 0.6,-1.6 0.6,-0.7 1.4,-1t1.6,-0.1 1.5,0.6l0.5,0.6 0.4,0.7 0.3,-0.1 0.5,0.4 -0.9,2 -0.8,-0.4v-1.1l-0.1,-0.7 -0.4,-0.5 -0.8,-0.3 -0.9,0.2q-0.4,0.2 -0.9,0.8l-0.6,1v1.1q0.1,0.4 0.5,0.7l0.5,0.3h0.5l0.4,-0.2 0.1,-0.2 0.2,-0.3v-0.1l-0.3,-0.3 -0.2,-0.2zM566.8,400.2 L567.8,401.2 565.9,406q-0.2,0.3 -0.1,0.5l0.2,0.3 -0.6,0.7 -2,-2 0.5,-0.7 0.1,0.2 0.3,0.1h0.2l0.2,-0.3 0.2,-0.5 -1.4,-1.3 -0.6,0.3 -0.2,0.1v0.3l0.3,0.3 -0.6,0.6 -1.7,-1.6 0.6,-0.6 0.2,0.2h0.2l0.4,-0.1zM564.4,402.5 L565.3,403.4 566,401.7zM570.9,403.9 L571.3,404.3 571.2,404.5 573.6,407h0.2l0.4,0.3 -1,2 -0.8,-0.3 0.2,-0.8v-0.7q0,-0.3 -0.2,-0.5h-0.2l-0.2,0.1 -2.5,2.5 -0.3,0.3v0.2l0.3,0.4 -0.6,0.6 -2,-2.2 0.6,-0.6v0.2l0.4,0.2h0.2l0.3,-0.2 2.6,-2.5 0.1,-0.2v-0.1l-0.5,-0.3h-0.7l-0.8,0.2 -0.3,-0.8 2,-1z"/>
  <path
      android:pathData="M0,0h320v240H0Z"
      android:fillColor="#012169"/>
  <path
      android:pathData="m37.5,0 l122,90.5L281,0h39v31l-120,89.5 120,89V240h-40l-120,-89.5L40.5,240H0v-30l119.5,-89L0,32V0Z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M212,140.5 L320,220v20l-135.5,-99.5ZM120,150.5 L123,168 27,240L0,240ZM320,0v1.5l-124.5,94 1,-22L295,0ZM0,0l119.5,88h-30L0,21Z"
      android:fillColor="#c8102e"/>
  <path
      android:pathData="M120.5,0v240h80V0ZM0,80v80h320V80Z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M0,96.5v48h320v-48ZM136.5,0v240h48V0Z"
      android:fillColor="#c8102e"/>
</vector>
