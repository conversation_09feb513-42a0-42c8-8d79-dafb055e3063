<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0Z"
      android:fillColor="#3e5eb9"/>
  <path
      android:pathData="M0,90h640v300H0Z"
      android:fillColor="#ffd900"/>
  <path
      android:pathData="M0,120h640v240H0Z"
      android:fillColor="#b10c0c"/>
  <path
      android:strokeWidth="1"
      android:pathData="m548.28,185.16 l-29.16,10.32 29.04,10.2 29.16,-10.2z"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M107.88,192L528.96,192A3.42,3.72 90,0 1,532.68 195.42L532.68,195.42A3.42,3.72 90,0 1,528.96 198.84L107.88,198.84A3.42,3.72 90,0 1,104.16 195.42L104.16,195.42A3.42,3.72 90,0 1,107.88 192z"
      android:fillColor="#ffd900"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M522.48,198.84a3.6,3.6 0,0 0,3.48 -3.36,3.6 3.6,0 0,0 -3.48,-3.48m-3.36,6.84a3.6,3.6 0,0 0,3.36 -3.36,3.6 3.6,0 0,0 -3.36,-3.48m-3.48,6.84a3.6,3.6 0,0 0,3.48 -3.36,3.6 3.6,0 0,0 -3.48,-3.48"
      android:fillColor="#ffd900"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m520.32,205.68l-29.16,10.32 29.04,10.2 29.16,-10.2z"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M79.92,212.52L501,212.52A3.42,3.72 90,0 1,504.72 215.94L504.72,215.94A3.42,3.72 90,0 1,501 219.36L79.92,219.36A3.42,3.72 90,0 1,76.2 215.94L76.2,215.94A3.42,3.72 90,0 1,79.92 212.52z"
      android:fillColor="#ffd900"
      android:strokeColor="#000"/>
  <path
      android:strokeWidth="1"
      android:pathData="M494.52,219.36a3.6,3.6 0,0 0,3.48 -3.36,3.6 3.6,0 0,0 -3.48,-3.48m-3.36,6.84a3.6,3.6 0,0 0,3.36 -3.36,3.6 3.6,0 0,0 -3.36,-3.48m-3.48,6.84a3.6,3.6 0,0 0,3.48 -3.36,3.6 3.6,0 0,0 -3.48,-3.48"
      android:fillColor="#ffd900"
      android:strokeColor="#000"/>
  <path
      android:pathData="M33.6,234.84L606.36,234.84A5.16,5.04 90,0 1,611.4 240L611.4,240A5.16,5.04 90,0 1,606.36 245.16L33.6,245.16A5.16,5.04 90,0 1,28.56 240L28.56,240A5.16,5.04 90,0 1,33.6 234.84z"
      android:strokeWidth="1.32"
      android:fillColor="#ffd900"
      android:strokeColor="#000"/>
  <path
      android:pathData="M488.2,240c-34.3,34.3 -80,102.9 -171.4,102.9 -80,0 -137.1,-68.6 -171.4,-102.9 34.2,-34.3 91.4,-102.9 171.4,-102.9 91.4,0 137.1,68.6 171.4,102.9z"
      android:strokeWidth="1.2"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M304.8,173c9.2,10.2 1.5,14.3 11.2,15 10.2,0.7 4.4,10.8 13,11.2 6,0.2 -0.6,24.2 5.1,32 5.9,8.3 10.8,2.5 11,8.4 0,6.2 -16,5.5 -16.3,24.5 -0.5,10.9 -13.6,11.6 -14.2,18.6 -0.8,6.6 25.8,10.3 25.4,16.2 -0.3,6 -28.7,5 -30,11.7 -0.6,6 39,11 42.2,28.5 -6,2 -22.7,3.7 -35.4,3.8 -80,0 -137.1,-68.6 -171.4,-103 34.3,-34.2 91.4,-102.8 171.4,-102.8 0,0 -23.7,21.7 -12,35.8z"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M213.6,230.88l0,-27.48m16.56,0l0,27.48m16.56,-27.48l0,27.48"
      android:strokeWidth="8.28"
      android:strokeColor="#fff"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M213.6,276.6l0,-27.48m16.56,0l0,27.48m16.56,-27.48l0,27.48"
      android:strokeWidth="8.28"
      android:strokeColor="#fff"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M263.52,230.88l0,-27.48m16.56,0l0,27.48m16.56,-27.48l0,27.48"
      android:strokeWidth="8.28"
      android:strokeColor="#fff"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M263.52,276.6l0,-27.48m16.56,0l0,27.48m16.56,-27.48l0,27.48"
      android:strokeWidth="8.28"
      android:strokeColor="#fff"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M339.36,230.88l0,-27.48m16.56,0l0,27.48m16.56,-27.48l0,27.48"
      android:strokeWidth="8.28"
      android:strokeColor="#fff"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M339.36,276.6l0,-27.48m16.56,0l0,27.48m16.56,-27.48l0,27.48"
      android:strokeWidth="8.28"
      android:strokeColor="#fff"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M389.28,230.88l0,-27.48m16.56,0l0,27.48m16.56,-27.48l0,27.48"
      android:strokeWidth="8.28"
      android:strokeColor="#fff"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M389.28,276.6l0,-27.48m16.56,0l0,27.48m16.56,-27.48l0,27.48"
      android:strokeWidth="8.28"
      android:strokeColor="#fff"/>
  <path
      android:strokeWidth="1"
      android:pathData="M570.4,236c-5.2,8.9 4,-3 26.4,12a22,22 0,0 1,7.8 15.3c-1,-0.7 -1.9,-2.3 -3,-3.2 -1.6,1.6 1.6,9 2,12.5 -3.5,-2 -3.2,-2.8 -4.3,-5.2 0.2,2.8 -0.7,10.8 0.8,14 -2.8,-0.8 -2.6,-3 -4,-3.5 1,3.5 -0.9,7.2 -0.4,11.2 -1.6,-1.6 -3.4,-2.9 -4.1,-3.5 -0.2,2 -3.1,6.8 -3.2,8.6a5,5 0,0 1,-2 -3,59 59,0 0,0 -8,12.4c-4.7,-4 -16.6,-14 -18.2,-19.4 -1.4,3 -3.4,4 -7.2,6.2 -1.6,-8.4 -7.5,-17.8 -4.4,-25.1l-6.1,4.2a50,50 0,0 1,28 -33.6z"
      android:fillColor="#333"
      android:strokeColor="#000"/>
  <path
      android:pathData="M551,280.5c2.2,-4.3 4.2,-5.7 5.6,-8.5 2.4,-4.8 2.8,-8.8 5,-8.2 2,0.6 2,2.6 -0.7,7.4s-4,5.8 -9.9,9.3m14.6,10.2c-0.2,-3.2 0.7,-4.6 0.6,-6.7 -0.4,-3.8 -1.9,-6.3 0.1,-6.5 2,-0.1 2.9,1.1 2.9,4.8 0,3.8 -0.6,4.8 -3.6,8.4m11.1,6.4c-0.9,-4.5 -0.2,-6.5 -0.8,-9.5 -1,-5.2 -2.9,-8.6 -1,-9.1s3,1.2 3.7,6.5c0.6,5.2 0.3,6.7 -1.9,12.2zM588.2,265.1c-2.7,-2 -4.3,-2.2 -6,-3.5 -3,-2.4 -4.6,-4.8 -5.6,-3.6s-0.3,2.6 3,4.7 4.2,2.3 8.6,2.4m-1.9,15.2c-1.4,-3 -3,-3.7 -3.8,-5.6 -1.6,-3.4 -1.6,-6.3 -3.5,-5.6 -1.7,0.8 -1.9,2.3 0,5.6 2,3.2 3,3.8 7.2,5.6z"
      android:fillColor="#b3b3b3"/>
  <path
      android:pathData="M53.8,244.6c-0.4,-2.6 0.2,-3 -1.4,-5 2.4,1 2.4,3.7 5.3,1.8 1,-0.5 1.5,-0.5 0.2,-3.5 3,0.2 12.7,3.5 14.3,3.6 4.1,0.2 11.6,-4.3 16.8,1.2 5,5 3.2,10.3 3.2,17.2 -2,-1 -1,-1.3 -3.2,-3.9 1.6,6.2 0,17.1 0,23.6 -0.8,-1.6 -0.7,-0.9 -1.5,-2.5 -2,5.9 -4.6,7 -4.6,14.1 -0.8,-2.8 0,-2.2 -0.9,-3.6 -2,4.5 -16,8.3 -10.6,12.8 -4.8,-2.8 -7,-2.6 -9,-5q-1.5,0.9 -2.6,3.2c-8.5,-3.8 -5.5,-12.2 -12.1,-17.9 -1.2,2.4 -0.7,2.1 -2,5.9 -1.2,-5.3 -1.6,-8.7 -3.1,-12.6 -1.3,2.2 -1.2,1.3 -3.6,4.6 -1,-6.4 -2.5,-8 -2,-12.3 -2.4,1.8 -0.8,1 -3.3,3.2 2.4,-16.5 12.6,-28.8 20,-25z"
      android:strokeWidth="1.3"
      android:fillColor="#333"
      android:strokeColor="#000"/>
  <path
      android:pathData="M43.4,271.7c2.1,-5.4 4,-7 5.5,-10.4 2.5,-6.1 2.9,-11 5,-10.3 2,0.8 2,3.2 -0.8,9.2s-4,7.2 -9.7,11.5m8.2,13.3c0.8,-4.8 2.2,-6.4 2.8,-9.6 1,-5.4 0.5,-9.4 2.4,-9.2 2,0.2 2.4,2.3 1.1,7.6 -1.3,5.5 -2.2,6.8 -6.4,11.2zM61.4,294.4c0.3,-4.6 1.4,-6.5 1.6,-9.5 0.3,-5.2 -0.8,-9 1,-9 2,0 2.6,1.9 2,7.1s-1.2,6.6 -4.6,11.4M82.6,264c-4,-4 -6.4,-4.8 -9,-7.4 -4.6,-4.6 -6.9,-8.8 -8.6,-7.3s-0.6,3.7 4.3,8c4.8,4.5 6.5,5.1 13.4,6.7zM80.2,279.6c-2.6,-4 -4.6,-5.1 -6.3,-7.8 -3,-4.5 -4,-8.4 -5.7,-7.5s-1.3,3 1.9,7.5 4.5,5.2 10.1,7.8"
      android:fillColor="#b3b3b3"/>
  <path
      android:strokeWidth="1"
      android:pathData="M261.4,155.4c-4.8,9.2 8.3,-3 28.8,12.4a35,35 0,0 1,9.2 19.3,62 62,0 0,1 -12,-5.3s9,9.2 9,19.6c-3.2,-2 -4.8,-1.7 -6,-4.1 0,3.3 3,5.5 3,10.7a42,42 0,0 0,-6.3 -4.2c3,5.3 -5.5,15.5 -1.2,18.8a27,27 0,0 1,-18.1 -11c-1.6,1 -1.8,2.7 -1.9,4.6 0.3,0.1 -11.3,-8.7 -10.2,-12a10,10 0,0 0,-2.2 5.8,32 32,0 0,1 -9.6,-13.8c-1.8,2.4 -2,2.4 -3.9,5 -1.4,-8.7 -1.4,-8.4 1.4,-15.8l-5.6,4.3a50,50 0,0 1,25.6 -34.3z"
      android:fillColor="#333"
      android:strokeColor="#000"/>
  <path
      android:pathData="M249.6,200.2c0.8,-6.3 2.4,-8.6 3,-12.8 0.8,-7 -0.2,-12.4 2.4,-12.2 2.4,0.2 3.2,2.8 2,10 -1.3,7.1 -2.4,8.8 -7.3,15.1zM262.9,211.8c-0.5,-5.2 0.5,-7.6 0.1,-11 -0.5,-6 -2.4,-10.2 -0.2,-10.5s3.4,1.6 3.6,7.8c0.2,6 -0.3,7.7 -3.5,13.7m20.8,9.6c-2.3,-4.7 -4.3,-6 -5.7,-9 -2.6,-5.3 -3.2,-9.5 -5.1,-8.8 -2,0.8 -1.9,3 1,8.2 2.8,5 4.1,6.1 9.7,9.6zM290.8,179.9c-5.8,-3 -8.7,-3 -12.4,-5 -6.5,-3.4 -10.3,-7.1 -11.6,-5 -1.4,2 0.4,4.1 7.1,7.2s8.8,3.2 16.8,2.8zM292.8,197.2c-4,-3.4 -6.6,-4 -9.3,-6.2 -4.8,-4 -7.1,-7.7 -8.6,-6.2 -1.6,1.6 -0.6,3.6 4.4,7.3s6.7,4.1 13.6,5.1z"
      android:fillColor="#b3b3b3"/>
</vector>
