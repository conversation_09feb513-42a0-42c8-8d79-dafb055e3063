<?xml version="1.0" encoding="utf-8"?>
<!-- The android:opacity=”opaque” line — this is critical in preventing a flash of black as your theme transitions. -->
<layer-list xmlns:android="http://schemas.android.com/apk/res/android" android:opacity="opaque">

    <item>

        <shape android:shape="rectangle" >

            <gradient
                    android:angle="45"
                    android:startColor="#31C369"
                    android:endColor="#19944A"
                    android:type="linear" />

            <corners
                    android:radius="0dp"/>

        </shape>

    </item>

    <item android:gravity="center"
          android:top="24dp"
          android:bottom="24dp"
          android:left="24dp"
          android:right="24dp"
          android:drawable="@drawable/ic_logo_white"/>

</layer-list>