<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0Z"
      android:fillColor="#006"/>
  <path
      android:pathData="M0,0h320v240H0Z"
      android:fillColor="#012169"/>
  <path
      android:pathData="m37.5,0 l122,90.5L281,0h39v31l-120,89.5 120,89V240h-40l-120,-89.5L40.5,240H0v-30l119.5,-89L0,32V0Z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M212,140.5 L320,220v20l-135.5,-99.5ZM120,150.5 L123,168 27,240L0,240ZM320,0v1.5l-124.5,94 1,-22L295,0ZM0,0l119.5,88h-30L0,21Z"
      android:fillColor="#c8102e"/>
  <path
      android:pathData="M120.5,0v240h80V0ZM0,80v80h320V80Z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M0,96.5v48h320v-48zM136.5,0v240h48V0Z"
      android:fillColor="#c8102e"/>
  <path
      android:pathData="m384.11,102.2 l194.05,-0.7 -0.39,172.82s6.82,26.35 -81.37,66.26c31.7,-3.25 66.26,-37.04 66.26,-37.04s14.03,-18.06 20.92,-7.98 13.25,15.19 18.29,19.14 8.99,14.72 1.47,22.63c-7.59,7.98 -19.45,8.99 -22.71,-0.7 -5.04,2.56 -35.96,39.99 -99.35,41.77C416.81,377.31 381.55,336.24 381.55,336.24s-8.6,13.72 -20.92,2.94c-11.86,-14.1 -2.87,-23.09 -2.87,-23.09s10.07,-5.73 13.02,-9.69c4.65,-5.42 6.12,-12.63 13.95,-12.63 9.45,0.77 13.02,8.29 13.02,8.29s32.39,34.18 67.34,38.52c-78.81,-37.82 -81.76,-61.22 -81.37,-66.96z"
      android:fillColor="#fff"/>
  <path
      android:pathData="m388.76,106.92 l185.06,-1.08l0,166.31c0.39,21.7 -36.04,43.63 -92.92,71.68 -58.67,-30.22 -92.53,-48.59 -92.84,-72.07z"
      android:strokeWidth="1.47"
      android:fillColor="#006129"
      android:strokeColor="#000"/>
  <path
      android:pathData="m413.95,131.49 l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M427.04,125.44a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M415.96,143.35l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM417.2,139.94l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M410.77,131.64s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M409.92,131.49c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M426.89,115.91a2.01,2.01 135,1 1,-4.03 0,2.01 2.01,135 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="m413.33,163.5l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M426.42,157.45a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M415.34,175.35l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM416.58,171.94l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M410.15,163.65s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M409.3,163.5c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M426.27,147.92a2.01,2.01 135,1 1,-4.03 0,2.01 2.01,135 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="m413.09,195.04l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M426.19,188.99a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M415.11,206.89l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM416.35,203.48l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M409.92,195.19s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M409.06,195.04c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M426.04,179.46a2.01,2.01 135,1 1,-4.03 0,2.01 2.01,135 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="m433.78,229.21l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M446.88,223.17a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M435.8,241.07l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM437.04,237.66l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M430.61,229.37s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M429.75,229.21c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M446.73,213.64a2.01,2.01 135,1 1,-4.03 0,2.01 2.01,135 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="m410.38,257.19l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M423.48,251.14a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M412.4,269.05l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM413.64,265.64l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M407.2,257.34s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M406.35,257.19c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M423.32,241.61a2.01,2.01 135,1 1,-4.03 0,2.01 2.01,135 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="m411,290.2l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M424.1,284.16a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M413.02,302.06l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM414.26,298.65l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M407.82,290.36s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M406.97,290.2c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M423.94,274.63a2.01,2.01 0,1 1,-4.03 0,2.01 2.01,0 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="m527.32,132.03l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M540.42,125.99a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M529.34,143.89l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM530.58,140.48l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M524.15,132.19s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M523.29,132.03c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M540.27,116.46a2.01,2.01 135,1 1,-4.03 0,2.01 2.01,135 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="m526.24,163.42l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M539.34,157.37a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M528.25,175.28l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM529.49,171.87l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M523.06,163.57s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M522.21,163.42c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M539.18,147.84a2.01,2.01 135,1 1,-4.03 0,2.01 2.01,135 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="m524.84,195.58l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M537.94,189.53a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M526.86,207.44l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM528.1,204.03l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M521.67,195.73s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M520.81,195.58c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M537.79,180a2.01,2.01 135,1 1,-4.03 0,2.01 2.01,135 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="m526.47,227.12l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M539.57,221.08a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M528.49,238.98l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM529.73,235.57l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M523.29,227.28s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M522.44,227.12c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M539.41,211.54a2.01,2.01 135,1 1,-4.03 0,2.01 2.01,135 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="m526.08,258.74l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M539.18,252.69a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M528.1,270.6l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM529.34,267.19l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M522.91,258.89s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M522.05,258.74c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M539.03,243.16a2.01,2.01 135,1 1,-4.03 0,2.01 2.01,135 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="m526.24,290.51l10.85,-16.04 11.08,16.12"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#ffc72c"/>
  <path
      android:pathData="M539.34,284.47a2.17,2.17 45,1 1,-4.34 0,2.17 2.17,45 0,1 4.34,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M528.25,302.37l16.97,0s0.23,-2.17 -1.78,-3.49c8.91,-1.24 6.59,-8.99 13.95,-9.45 1.55,0.23 -3.87,3.33 -3.87,3.33s-4.42,3.1 -2.32,4.73c1.55,1.32 2.32,-0.77 2.48,-2.32s7.21,-2.56 6.2,-6.97c-1.63,-3.64 -11.39,2.48 -11.39,2.48l-6.97,-0.08c-0.46,-0.77 -2.32,-3.87 -4.34,-3.87 -2.32,0 -4.03,3.87 -4.03,3.87l-15.73,0s-0.54,4.11 7.44,4.88c1.78,2.32 3.18,2.94 4.8,3.56q-1.55,1.24 -1.39,3.33zM529.49,298.96l14.1,0"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M523.06,290.67s1.08,6.59 6.35,8.14"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M522.21,290.51c0.46,-2.48 1.47,-2.94 2.48,-6.2 0.15,-3.1 -2.48,-2.71 -1.7,-4.65q2.01,-3.41 -1.94,-5.97c0.54,2.87 -3.41,5.58 -3.41,7.9s2.01,1.86 1.78,5.42c0.15,2.01 -0.54,1.55 -0.77,3.49z"
      android:strokeWidth="0.7"
      android:strokeColor="#000">
    <aapt:attr name="android:fillColor">
      <gradient 
          android:startX="407.51"
          android:startY="126.77"
          android:endX="399.41"
          android:endY="120.66"
          android:type="linear">
        <item android:offset="0" android:color="#FFFF0000"/>
        <item android:offset="1" android:color="#FFFFFF00"/>
      </gradient>
    </aapt:attr>
  </path>
  <path
      android:pathData="M539.18,274.94a2.01,2.01 0,1 1,-4.03 0,2.01 2.01,0 0,1 4.03,0z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M492.22,305.47s4.42,10.23 9.45,3.87c5.04,-6.2 3.25,-8.91 3.25,-8.91l-11.31,-6.12 -3.33,6.97z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M501.98,304.15s0.77,0.08 1.32,-1.01c0.62,-1.08 -1.32,-1.55 -2.25,-2.79l-0.93,1.94zM476.41,301.36 L466.49,306.79s-4.96,1.01 -5.27,0c-0.39,-0.93 0.08,-1.78 2.71,-1.94 2.56,-0.08 9.53,-6.51 9.53,-6.51l2.94,3.1zM476.56,124.75 L476.87,128.31c0.15,1.24 -1.94,3.87 -2.01,3.72s-1.16,0.15 -1.01,0.85 1.55,1.01 1.55,1.01 -0.54,2.56 0,2.71c0.62,0.08 -1.55,3.25 0,4.11 1.63,0.85 4.34,2.01 5.58,1.78s0,4.73 0,4.73l-3.49,7.36 18.91,-1.94 -3.87,-6.2s-1.86,-1.32 -1.39,-4.88c0.54,-3.49 -0.23,-19.61 -0.23,-19.61l-13.48,-1.86zM473.08,152.8s-6.12,2.79 -5.89,10.38c-1.55,7.36 -2.4,14.72 -2.4,14.72s-7.28,8.21 -9.45,11.24c-2.25,2.94 -5.58,8.91 -6.74,10.54 -1.24,1.55 -6.04,6.82 -5.97,8.83 0.15,1.94 -1.08,10.69 3.72,11.62 1.24,0.54 5.19,-10.07 5.19,-10.07s0.23,-4.49 -1.16,-5.42c-1.32,-0.77 2.94,-3.72 2.94,-3.72l10.07,-7.52c1.86,-1.55 6.9,-7.13 6.9,-7.13z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc6b5"
      android:strokeColor="#000"/>
  <path
      android:pathData="M480.13,146.99s1.55,4.34 5.11,3.56 7.75,-4.03 7.75,-4.03 3.33,-0.15 3.8,0.39 8.99,8.68 8.68,11.24c-0.23,2.63 -3.87,1.86 -5.27,3.56s-3.56,6.04 -2.94,9.3c0.62,3.1 2.48,7.28 2.25,8.91s-1.55,2.09 -1.55,2.94 1.08,2.32 1.08,3.87c0,1.63 -1.55,3.95 -1.24,5.58 0.23,1.55 0.31,6.2 0.31,6.2l-0.31,21.7s1.24,0.7 1.32,1.94c0.15,1.16 8.37,36.89 8.37,36.89s-0.39,1.16 -1.24,1.01 3.33,5.5 3.41,7.13 4.34,14.1 4.18,15.81 -0.77,5.58 -1.08,5.66c-0.39,0.15 2.71,7.9 2.17,9.07 -0.46,1.24 -5.42,1.16 -5.42,1.16l-1.39,-0.31s0.08,1.63 -0.85,1.78 -8.21,-0.39 -8.21,-0.39 -2.09,3.18 -3.33,3.1c-1.24,-0.15 -2.87,-2.32 -3.18,-2.01 -0.39,0.39 1.08,2.48 0.77,3.1 -0.46,0.62 -6.66,1.94 -7.9,-1.01s0.77,-2.17 0.31,-2.79c-0.31,-0.62 -3.1,-2.25 -4.03,-1.78 -0.77,0.54 2.25,1.24 2.09,2.48s-2.71,3.1 -3.64,3.1 -3.33,-4.57 -6.82,-4.11c-3.41,0.54 -5.58,1.39 -5.58,1.39s-4.11,1.7 -5.81,1.32c-1.7,-0.31 -2.48,-1.7 -2.48,-2.4 0,-0.77 1.24,-3.95 1.16,-4.96 -0.15,-0.93 -1.16,-1.94 -1.16,-3.41s2.87,-6.51 2.87,-6.51l-0.15,-22.63s-2.56,0 -2.71,-1.55c-0.08,-1.55 3.95,-35.73 4.65,-37.97l2.17,-10.07s-1.86,0.93 -2.01,0c-0.08,-0.77 5.58,-20.3 5.58,-20.3s0.93,-9.76 0.93,-12.32 -0.46,-6.12 -0.46,-6.12 -5.04,-2.17 -5.19,-5.42c-0.46,-5.19 4.8,-8.06 5.42,-9.76l2.48,-6.97s2.71,-4.65 7.13,-5.35z"
      android:strokeWidth="0.7"
      android:fillColor="#fff"
      android:strokeColor="#000"/>
  <path
      android:pathData="M478.27,302.91s-11.93,6.2 -13.64,6.74 -2.79,-2.09 -1.08,-2.56c1.7,-0.54 4.42,-0.77 4.42,-0.77s-4.03,-3.18 -3.87,-3.33l5.58,-2.01c0.15,0 1.7,3.1 2.79,2.94a24.02,24.02 0,0 0,4.34 -2.71z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M495.01,306.32c1.16,1.47 1.55,3.95 4.18,2.48 2.56,-1.47 -1.32,-4.18 -1.32,-4.18z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc6b5"
      android:strokeColor="#000"/>
  <path
      android:pathData="M499.73,306.09s1.24,1.01 2.32,-0.15c1.08,-1.08 -2.17,-3.41 -2.17,-3.41l-1.63,1.86zM505,158.77c0.15,0 4.03,12.09 4.57,15.19 0.46,3.1 2.09,15.34 1.47,17.05s-6.9,9.92 -7.59,12.01 -5.19,10.07 -5.19,10.07 -1.08,7.9 -1.55,8.21 1.24,2.32 1.08,2.94c-0.23,0.77 -3.56,4.18 -5.04,3.87s-3.87,-2.09 -3.87,-3.72c-0.23,-1.55 0,-6.9 1.16,-8.21 1.08,-1.39 6.9,-14.96 7.21,-15.81 0.39,-0.93 5.27,-11.62 5.42,-13.41s-1.55,-6.12 -3.25,-7.67c-3.87,-11.39 -2.32,-18.21 5.58,-20.54z"
      android:strokeWidth="0.7"
      android:fillColor="#ffc6b5"
      android:strokeColor="#000"/>
  <path
      android:pathData="M476.1,124.05s2.79,0.39 4.26,-0.46 3.18,-1.24 4.42,0.46 2.09,1.55 2.09,1.55 -1.86,4.65 0,5.11c1.78,0.46 2.71,0.46 2.79,1.08s-1.55,1.94 -1.08,2.56 1.32,1.32 1.47,1.86 -1.08,2.56 -0.77,3.1c0.39,0.46 1.55,2.4 2.25,2.4 0.77,0 0.23,3.1 2.48,2.32 2.17,-0.77 2.09,-2.71 2.09,-2.71s2.32,-0.31 2.94,-2.4 2.01,-2.63 2.01,-2.63 2.94,-1.55 -0.93,-4.03c0,-17.2 -11.31,-15.34 -11.31,-15.34s-1.32,-3.1 -3.56,-2.71 -2.32,2.94 -3.87,2.71c-1.63,-0.23 -2.01,-1.32 -2.09,-1.24 -0.15,0.15 -1.55,2.63 -1.55,3.18 0,0.62 -4.26,-0.77 -3.87,2.32 0.39,3.25 2.48,3.1 2.32,2.87z"
      android:strokeWidth="0.7"
      android:fillColor="#9c5100"
      android:strokeColor="#000"/>
  <path
      android:pathData="M496.09,151.79s-10.93,6.97 -10.69,9.45m13.17,-8.21s-2.48,2.71 -2.63,2.71m5.89,0.23s-5.42,4.42 -4.57,7.21m-20.92,-12.01s-1.63,3.33 -1.24,4.42 3.1,5.27 3.41,7.83c0.39,2.63 0,4.42 0,4.42m-5.04,-10.54s0.46,3.87 1.24,4.65 2.71,4.11 2.94,5.42m-5.19,6.28s3.25,1.55 6.2,-4.57m4.73,-4.26c-0.08,0 -2.32,5.97 1.63,8.21s6.9,1.94 8.52,1.32c1.78,-0.54 3.64,-1.7 3.64,-1.7m-15.5,-0.46s0.23,8.06 12.17,16.04m-11.62,-8.45s-0.15,7.13 4.49,10.31m-6.97,-18.68s-3.56,10.62 -6.43,11.62m5.35,-3.87s-0.15,7.59 -1.16,10.31m-0.93,1.86s2.4,3.1 5.11,2.79 3.87,-3.41 5.66,-2.94c1.86,0.54 3.56,2.01 7.83,1.63m-6.12,3.18s0,6.28 1.16,6.9c1.08,0.62 0.54,6.35 0.54,6.35m-16.66,-16.2s-0.15,5.89 -0.85,7.98 -2.25,5.66 -1.94,8.76m-4.73,3.41c0.62,-0.23 2.71,-2.09 2.71,-2.09m1.08,1.32s-5.27,22.63 -3.8,36.11m5.04,-34.95s-2.71,16.89 -1.47,20.15m0.15,-20.69 l10.31,0.77m1.47,-1.39s2.79,1.55 6.74,1.24m-7.98,7.05s-0.46,28.91 -1.24,35.34m16.35,-28.75s3.18,25.42 5.04,27.74m-11.16,-24.57s1.94,22.47 3.1,24.49m-31,7.83s3.87,-1.24 7.28,-4.88c3.87,5.27 9.92,0.23 9.92,0.23s9.3,6.35 13.48,-0.77c6.43,4.18 9.61,-0.62 9.61,-0.62s2.32,3.56 4.03,3.18m-10.15,2.48s4.73,22.47 11.86,28.83"
      android:strokeWidth="0.7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M471.45,260.06s0.54,18.75 1.63,32.01m-2.56,-10.77s-0.62,12.24 -1.32,13.17m-5.89,1.16s1.32,5.35 8.06,0.39c6.74,-4.88 6.9,1.86 7.13,2.56 0.23,0.77 1.32,6.04 3.87,1.63m5.42,-7.21s-1.08,10.85 8.6,2.94c9.69,-7.98 11.31,-0.15 11.62,2.32m-20.07,-178.4s-0.85,5.42 4.88,4.88c-0.77,3.02 1.55,3.95 1.55,3.95m4.18,3.33c0.15,0 2.63,1.55 0,3.56m-8.29,0.7s1.24,1.55 2.56,1.16c1.39,-0.39 3.56,1.32 3.56,1.32s1.86,0.62 2.09,0.23m-15.73,1.94s4.18,1.63 6.97,-4.96m-14.65,-2.01 l2.4,0.15M447.81,211.47l0.23,5.35m-5.27,-5.81s3.49,5.81 3.1,8.83"
      android:strokeWidth="0.7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M475.4,136.45l2.25,0l-1.94,0.85M489.04,223.09s2.56,-0.31 2.48,4.03c1.63,-5.42 4.96,-5.5 4.96,-5.5"
      android:strokeLineJoin="round"
      android:strokeWidth="0.7"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M478.5,129.55c0.39,0 1.7,-0.46 1.94,-0.08 0.31,0.31 -1.47,0.62 -1.94,0.08z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.32"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="M480.21,346a124,124 74.41,0 0,84.94 -39.29c-0.31,-0.31 6.9,-10.38 12.63,-8.99s14.03,18.37 24.1,22.01c5.04,7.9 -1.39,15.11 -3.56,16.51s-11.93,5.42 -13.33,-0.31c-1.47,-5.81 -4.34,-4.65 -4.34,-4.65s-46.11,44.95 -99.35,43.17c-55.02,0.31 -100.44,-43.24 -100.44,-43.24l-3.95,4.34s-4.34,4.65 -6.51,4.34 -11.55,-6.51 -12.24,-12.63 5.73,-10.07 5.73,-10.07 15.89,-12.24 17.67,-18.68c3.64,-3.64 10.46,2.48 10.46,2.48s41.77,47.89 88.19,44.95z"
      android:strokeWidth="1.47"
      android:fillColor="#ffc72c"
      android:strokeColor="#000"/>
  <path
      android:pathData="M362.88,318.1s4.26,-1.08 5.81,0.7 12.4,12.4 12.4,12.4"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m372.64,323.14 l-4.34,3.18s10.85,2.17 8.37,9.3m221.33,-17.82s-2.09,-1.16 -5.42,1.32c-3.41,2.56 -11.86,12.01 -11.86,12.01"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:pathData="m588.39,322.83 l4.65,3.56s-9.69,0.7 -7.59,9.84"
      android:strokeWidth="1.47"
      android:fillColor="#00000000"
      android:strokeColor="#000"/>
  <path
      android:fillColor="#FF000000"
      android:pathData="M417.74,329.57c-0.31,0.77 -1.08,0 -1.7,0.15 -1.55,0 -2.94,1.08 -4.34,1.86l-14.26,8.29q-0.54,-0.31 -0.23,-0.93l3.87,-17.82c0.15,-1.01 0.46,-2.32 -0.54,-3.1 -0.39,-0.31 0.23,-0.85 0.54,-0.31l7.28,5.19c-0.23,0.7 -0.7,0.15 -1.08,-0.08 -1.01,-1.01 -2.25,-0.08 -2.17,1.16l-2.63,11.7q4.88,-2.79 9.76,-5.73c1.08,-0.46 1.94,-2.01 0.77,-2.94 -0.46,-0.31 -1.24,-1.01 -0.46,-1.16zM422.86,354.37q-0.23,0.7 -0.77,0.23l-8.52,-4.65q0.08,-0.77 0.7,-0.31c0.77,0.54 2.01,0.77 2.63,-0.15l3.1,-5.66 4.34,-7.9c0.62,-1.01 -0.08,-2.17 -1.01,-2.56 -0.31,-0.15 -0.77,-0.31 -0.39,-0.62 0.15,-0.23 0.62,0.23 0.85,0.31l8.21,4.49q-0.15,0.7 -0.7,0.23c-0.77,-0.54 -2.01,-0.77 -2.63,0.15l-3.1,5.66 -4.34,7.9c-0.62,0.93 0.08,2.17 1.01,2.56zM455.87,346.62 L453.55,353.13c-1.01,-0.08 -0.31,-1.32 -0.62,-1.94a7.36,7.36 0,0 0,-5.42 -6.35c-2.32,-0.62 -4.65,0.54 -6.04,2.4a17.82,17.82 0,0 0,-3.41 9.45,5.42 5.42,0 0,0 2.48,4.96 7.75,7.75 0,0 0,4.57 1.16l1.63,-4.88c0.23,-1.08 -0.93,-1.78 -1.78,-2.01 -0.15,-0.31 0.15,-0.77 0.54,-0.39l8.52,3.1c0,1.01 -1.24,-0.15 -1.94,0.31 -1.01,0.39 -1.08,1.63 -1.55,2.56l-1.01,2.94q-5.89,0.39 -11.16,-2.48c-3.18,-2.01 -5.42,-5.97 -4.73,-9.84 0.46,-3.33 2.56,-6.43 5.58,-7.9 2.79,-1.55 6.2,-1.47 9.14,-0.31q2.32,0.93 4.18,2.63c0.77,1.01 2.17,1.01 2.79,-0.08zM469.74,368.94c0,0.31 0,0.7 -0.46,0.46l-9.76,-1.47q-0.08,-0.7 0.46,-0.46c0.77,0.15 2.01,0.15 2.32,-0.77q0.54,-2.01 0.77,-4.03l1.7,-11.31c0.23,-0.77 0,-1.7 -0.85,-2.01q-0.77,-0.23 -1.55,-0.39 -0.15,-0.54 0.46,-0.46l9.76,1.55q0.15,0.62 -0.46,0.46c-0.77,-0.23 -1.94,-0.15 -2.32,0.77q-0.54,1.94 -0.77,3.95l-1.7,11.31c-0.23,0.77 0,1.78 0.93,2.09l1.55,0.31zM496.25,362.59l-0.39,6.9 -17.51,0.85q-0.15,-0.62 0.46,-0.54c0.85,0 2.01,-0.39 2.17,-1.39l-0.23,-5.11 -0.54,-10.38c0.08,-1.01 -0.77,-1.78 -1.78,-1.7 -0.31,-0.15 -1.16,0.31 -1.08,-0.23 -0.15,-0.46 0.54,-0.23 0.77,-0.31l9.76,-0.54q0.15,0.77 -0.54,0.62c-0.85,0 -2.01,0.15 -2.32,1.24q0,2.25 0.15,4.42l0.54,10.85c0,0.85 0.77,1.7 1.7,1.55 1.39,-0.08 2.87,0 4.26,-0.39 1.94,-0.54 2.94,-2.48 3.49,-4.26 0.39,-0.46 0,-1.7 0.77,-1.55zM509.81,345.69c-0.46,0 -0.23,0.62 -0.39,0.93q-1.08,8.76 -2.32,17.36c-0.15,1.32 -0.46,2.79 -1.7,3.41 -0.46,0.23 -0.08,0.93 0.39,0.54l5.81,-1.78c-0.08,-0.93 -1.24,0 -1.86,-0.23 -1.32,0 -1.55,-1.63 -1.32,-2.63q0,-1.08 0.23,-2.09l6.66,-2.01q1.32,1.55 2.56,3.25c0.62,1.08 -0.77,1.55 -1.55,1.78 -0.46,0 -0.15,0.77 0.23,0.46l8.76,-2.63c0,-1.01 -1.24,0 -1.78,-0.77 -1.7,-1.16 -2.87,-2.87 -4.18,-4.42zM509.81,352.67 L514.46,358.32 508.8,360.03zM539.72,332.67 L542.28,337.32c-0.77,0.77 -1.24,-0.85 -1.94,-1.08a3.87,3.87 0,0 0,-4.26 -0.39l-1.55,0.85 7.59,14.18c0.39,1.16 1.86,1.39 2.87,0.77q0.54,-0.62 0.77,0.08 -1.94,1.16 -4.03,2.17l-5.04,2.71q-0.46,-0.54 0.31,-0.77a1.86,1.86 0,0 0,0.85 -2.87l-7.52,-13.95c-1.39,0.77 -3.1,1.39 -3.49,3.1 -0.39,1.16 0.15,2.48 0.15,3.41 -0.39,0.46 -0.54,-0.15 -0.77,-0.46l-2.09,-4.03zM551.58,325.62 L556.39,332.05c1.08,-0.77 2.01,-2.01 1.7,-3.41 0.15,-1.08 -1.39,-2.32 -0.85,-3.1 0.31,-0.15 0.54,0.62 0.77,0.85l5.58,7.36c-0.77,0.77 -1.24,-0.85 -2.01,-1.08 -1.08,-0.77 -2.56,-1.16 -3.72,-0.31q-1.16,0.46 -0.23,1.32 1.86,2.56 3.87,5.04c0.77,0.77 1.86,0 2.56,-0.62 1.86,-1.24 3.49,-3.25 3.41,-5.58 0.15,-1.08 -0.54,-2.17 -0.54,-3.1 0.46,-0.54 0.54,0.31 0.77,0.62l2.63,4.65 -13.64,10.31c-0.54,-0.39 0,-0.7 0.39,-0.93 1.24,-0.77 0.85,-2.4 0,-3.25L548.25,329.26c-0.77,-1.01 -2.17,-0.46 -2.87,0.23 -0.39,0.46 -0.77,-0.39 -0.23,-0.46l12.79,-9.69 3.49,4.65c-0.77,0.77 -1.55,-1.01 -2.4,-1.08 -1.7,-0.93 -3.72,-0.23 -5.11,0.93l-2.4,1.78"/>
</vector>
