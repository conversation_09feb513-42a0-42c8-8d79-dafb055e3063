<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,320h640v160H0z"
      android:fillColor="#000001"/>
  <path
      android:pathData="M0,160h640v160H0z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M0,0h640v160H0z"
      android:fillColor="#ce1126"/>
  <path
      android:pathData="m320.64,241.92 l54.8,50.88 -3.92,-92.4c-0.56,-14 -12.72,-10.8 -21.6,-5.76 -8.88,5.76 -19.2,5.76 -29.92,2 -10.8,3.76 -21.04,3.76 -29.92,-2 -8.8,-5.04 -21.04,-8.24 -21.6,5.76L264.56,292.8z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.04"
      android:fillColor="#fff"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="m274.8,197.2 l-3.84,89.84 -6.4,5.76 3.92,-92.4a19.2,19.2 0,0 1,6.32 -3.2m7.68,6.4 l-3.2,75.2 -6.4,6.56 3.84,-86.8c1.28,1.28 5.04,4.4 5.76,5.04m6.96,5.76 l-2.48,62.72 -5.2,5.04 3.2,-71.52c1.28,1.2 3.84,3.04 4.48,3.76m7.6,3.2 l-2.48,53.44 -5.04,4.08 2.48,-59.44c1.28,0.56 3.76,1.92 5.04,1.92m7.04,0 l-1.84,44.56 -5.2,5.04 2,-49.04c1.2,0 4.48,0 5.04,-0.56"
      android:fillColor="#c09300"
      android:strokeColor="#00000000"/>
  <path
      android:strokeWidth="1"
      android:pathData="m365.2,197.2l3.84,89.84 6.4,5.76 -3.92,-92.4a19.2,19.2 0,0 0,-6.32 -3.2m-7.68,6.4l3.2,75.2 6.4,6.56 -3.84,-86.8c-1.28,1.28 -5.04,4.4 -5.76,5.04m-6.96,5.76l2.48,62.72 5.2,5.04 -3.2,-71.52c-1.28,1.2 -3.84,3.04 -4.48,3.76m-7.6,3.2l2.48,53.44 5.04,4.08 -2.48,-59.44c-1.28,0.56 -3.76,1.92 -5.04,1.92m-7.04,0l1.84,44.56 5.2,5.04 -2,-49.04c-1.2,0 -4.48,0 -5.04,-0.56"
      android:fillColor="#fff"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="m322.56,252 l7.68,35.04 -2.56,2.56 -2.64,-2 -4.32,-31.2 1.84,31.2 -2.56,3.2 -2.48,-3.2 1.84,-31.2 -4.4,31.2 -2.64,2 -2.56,-2.56 7.68,-34.96l5.12,0z"
      android:strokeWidth="0.88"
      android:fillColor="#c09300"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="m302.8,236.64 l-15.28,54.16 21.04,3.2 8.88,-40.72z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.96"
      android:fillColor="#fff"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="m297.76,255.2 l1.84,4.4 9.92,-9.44"
      android:strokeLineJoin="round"
      android:strokeWidth="1.04"
      android:fillColor="#00000000"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="m304.64,244 l2.08,19.44 6.32,-8.32m-2.56,3.2 l3.44,12m1.36,-4.4 l-6.96,10.56m2.16,10.56 l-2.24,-10.56 -1.92,-10.72 -4.72,6.32 -2,-7.28 -6.56,6.72 3.28,12.16 4.64,-7.52 2.48,7.68 4.8,-7.36"
      android:strokeLineJoin="round"
      android:strokeWidth="1.04"
      android:fillColor="#00000000"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="m292,289.6 l4.24,-6 2.72,9.2 3.84,-6.4 2.48,7.68"
      android:strokeLineJoin="round"
      android:strokeWidth="1.04"
      android:fillColor="#00000000"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="m337.2,236.64l15.28,54.16 -21.04,3.2 -8.88,-40.72z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.96"
      android:fillColor="#fff"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="m342.24,255.2l-1.84,4.4 -9.92,-9.44"
      android:strokeLineJoin="round"
      android:strokeWidth="1.04"
      android:fillColor="#00000000"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="m335.36,244l-2.08,19.44 -6.32,-8.32m2.56,3.2l-3.44,12m-1.36,-4.4l6.96,10.56m-2.16,10.56l2.24,-10.56 1.92,-10.72 4.72,6.32 2,-7.28 6.56,6.72 -3.28,12.16 -4.64,-7.52 -2.48,7.68 -4.8,-7.36"
      android:strokeLineJoin="round"
      android:strokeWidth="1.04"
      android:fillColor="#00000000"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="m348,289.6l-4.24,-6 -2.72,9.2 -3.84,-6.4 -2.48,7.68"
      android:strokeLineJoin="round"
      android:strokeWidth="1.04"
      android:fillColor="#00000000"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="M320,315.04c16,0 31.2,-1.28 40.16,-3.76 3.76,-0.72 3.76,-2.64 3.76,-5.2 3.84,-1.28 1.92,-5.76 4.56,-5.76 -2.72,0.8 -3.2,-4.4 -6.4,-3.76 0,-4.48 -4.56,-5.04 -8.32,-3.76 -7.6,2.48 -21.04,3.12 -33.76,3.12 -12.8,-0.64 -26.08,-0.64 -33.76,-3.2 -3.76,-1.2 -8.24,-0.64 -8.24,3.84 -3.2,-0.64 -3.76,4.48 -6.4,3.76 2.64,0 0.64,4.56 4.48,5.76 0,2.56 0,4.48 3.84,5.2 8.8,2.48 24.16,3.76 40.08,3.76"
      android:strokeLineJoin="round"
      android:strokeWidth="1.92"
      android:fillColor="#fff"
      android:strokeColor="#c09300"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M298.32,290.8c5.12,0.72 10.88,1.28 15.36,0.72 2.56,0 4.4,4.4 -0.72,5.04 -4.4,0.56 -11.44,0 -15.2,-0.64a184.8,184.8 72.38,0 1,-14.72 -3.2c-4.48,-1.92 -1.28,-5.6 1.28,-5.12a84,84 0,0 0,14 3.2m43.36,0c-5.12,0.72 -10.88,1.28 -15.2,0.72 -2.72,0 -4.56,4.4 0.56,5.04 4.48,0.56 11.44,0 15.2,-0.64 3.2,-0.64 10.24,-1.84 14.72,-3.2 4.48,-1.92 1.28,-5.6 -1.28,-5.12a84,84 0,0 1,-14 3.2"
      android:strokeLineJoin="round"
      android:strokeWidth="1.04"
      android:fillColor="#fff"
      android:strokeColor="#c09300"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M282.4,288.32c-3.84,-0.72 -5.6,3.76 -4.4,6.32 0.56,-1.28 3.2,-1.28 3.76,-2.56 0.72,-1.92 -0.56,-1.92 0.72,-3.76zM297.76,300.08c0,-2.56 2.48,-2.24 2.48,-4.8 0,-1.2 -0.64,-3.2 -1.92,-3.2a2.72,2.72 0,0 0,-2.56 2.56c-0.56,2.48 2,2.88 2,5.44m18.16,-7.28c3.76,0 3.36,5.04 1.6,7.6 0,-1.84 -3.2,-2.56 -3.2,-3.84 0,-1.92 2.88,-1.92 1.6,-3.76m41.6,-4.48c3.92,-0.72 5.76,3.76 4.48,6.32 -0.56,-1.28 -3.2,-1.28 -3.76,-2.56 -0.72,-1.92 0.56,-1.92 -0.72,-3.76M342.4,300c0,-2.56 -2.56,-2.24 -2.56,-4.8 0,-1.2 0.64,-3.2 1.92,-3.2a2.72,2.72 45,0 1,2.56 2.56c0.56,2.48 -2,2.88 -2,5.44zM324,292.8c-3.76,0 -3.36,5.04 -1.6,7.6 0,-1.84 3.2,-2.56 3.2,-3.84 0,-1.92 -2.88,-1.92 -1.6,-3.76"
      android:strokeLineJoin="round"
      android:strokeWidth="1.04"
      android:fillColor="#fff"
      android:strokeColor="#c09300"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M283.76,289.6c1.28,0 3.2,0.56 3.76,1.28zM290.08,291.52c0.64,0 3.2,0.56 4.4,1.28zM312.96,294.08c-1.2,0 -3.76,0 -4.4,0.56l4.4,-0.64zM306,294.08c-0.72,-0.72 -3.2,-0.72 -4.48,0zM356.24,289.6a6.4,6.4 0,0 0,-3.76 1.28zM350,291.52c-0.8,0 -3.2,0.56 -4.48,1.28zM327.04,294.08c1.2,0 3.76,0 4.48,0.56l-4.48,-0.64zM334,294.08c0.72,-0.72 3.2,-0.72 4.48,0z"
      android:strokeLineJoin="round"
      android:strokeWidth="0.72"
      android:fillColor="#fff"
      android:strokeColor="#c09300"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M282.64,299.68q-0.64,-0.24 -0.48,-0.8 0.24,-0.8 0.8,-0.64c0.32,0 0.72,0.48 0.72,0.64l-0.32,0.56 -0.16,0.08q-0.08,0.24 -0.56,0.16m44,3.12q-0.32,-0.16 -0.48,-0.56c0,-0.32 0.48,-0.8 0.8,-0.8l0.64,0.32q0.4,0.48 -0.08,0.96 -0.4,0.24 -0.88,0zM326.88,304.8q-0.4,-0.16 -0.48,-0.64t0.4,-0.72l0.32,-0.16 0.32,0.16q0.48,0.16 0.56,0.64 0,0.4 -0.56,0.64 -0.32,0.24 -0.56,0z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.04"
      android:fillColor="#c09300"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M286.24,296q-0.56,0.08 -0.96,0.48c-0.48,0.08 -1.12,0.4 -1.6,0 -0.48,-0.08 -1.12,0 -1.2,0.64 0.08,0.56 0.8,0.8 1.28,0.48 0.32,-0.4 1.2,-0.72 1.36,0 -0.4,0.64 -0.32,1.36 -0.64,1.92q-0.08,0.56 -0.4,1.04 -0.64,0 -1.12,0.24a1.6,1.6 0,0 0,-1.28 0.64q-0.56,0.72 -0.72,1.6c0.08,0.48 0.8,0.64 1.28,0.72l1.6,0.48 2.56,0.72q1.92,0.48 3.84,0.88l0.4,0.08c0.56,0.16 0.8,-0.4 0.8,-0.8l0.8,-2.88c0.16,-0.4 0.4,-1.2 -0.32,-1.04q-0.56,0.48 -1.28,0.64c-0.72,0 -0.32,0.8 0,1.04q0,0.8 -0.4,1.44 -0.72,0.24 -1.44,-0.16c-0.4,0 -1.36,-0.24 -0.96,-0.8l0.4,-1.44q0.24,-0.8 0.4,-1.6c-0.32,-0.56 -0.8,0.24 -1.2,0.32s-1.28,0.24 -0.8,0.8c0.48,0.32 0.16,0.88 0,1.36 0,0.56 -0.64,0.8 -1.12,0.56 -0.48,0 -1.2,-0.4 -0.8,-0.88l0.4,-1.44 0.56,-1.6q0,-0.8 0.48,-1.44c0,-0.56 0.4,-1.04 0.4,-1.6q0,-0.32 -0.32,-0.32zM283.28,301.84l0.24,0q0.24,0.24 0,0.48l-0.24,0.16q-0.56,0 -0.48,-0.24 0,-0.08 0.24,-0.24zM282.48,305.84q-0.56,-0.4 0.24,-1.04 0.32,-0.24 0.72,0.08 0.72,0.48 0,1.04 -0.16,0.24 -0.4,0.16t-0.56,-0.16zM284.88,306.64a0.8,0.8 0,0 1,-0.48 -0.88q0.08,-0.48 0.72,-0.4 0.72,-0.08 0.72,0.56 0,0.4 -0.24,0.56zM356.24,306.64q-0.24,-0.08 -0.24,-0.4 0,-0.48 0.64,-0.72 0.72,-0.4 0.96,0.16 0.4,0.64 -0.08,0.96 -0.16,0.32 -0.64,0.24 -0.56,0 -0.64,-0.16zM288.24,307.6q-0.48,-0.08 -0.48,-0.64 0,-0.32 0.16,-0.48l0.48,-0.16 0.48,0.08q0.64,0.48 0.24,0.96t-0.96,0.24zM305.68,308.4 L305.52,308.16q0,-0.72 0.16,-1.36 0,-0.56 0.16,-1.2l0.32,-2.24q0,-0.64 0.16,-1.12 0,-0.96 0.16,-1.76c0,-0.24 0.24,-0.8 0.48,-0.48q0.48,0.64 1.12,1.2c0.32,0.24 0,0.56 -0.24,0.64q-0.48,0.24 -0.4,0.8l-0.16,0.96q0,0.8 -0.16,1.6l-0.08,1.44 -0.16,0.96q0.08,0.56 -0.32,0.88 -0.48,0.24 -0.8,-0.08zM329.44,300.56 L328.4,301.52c-0.48,0.4 0.4,0.56 0.48,0.88q0.24,0.72 0.16,1.44 0.24,0.72 0.16,1.36c0,0.56 -0.64,0.4 -0.96,0.72q-0.48,0.32 -0.8,0.8l-0.32,1.28c0,0.4 -0.24,0.8 0,1.12l0.08,0.16l0.4,0l1.2,-0.08q1.52,-0.16 2.96,-0.24l1.76,-0.16c0.48,0.08 0.72,-0.4 0.72,-0.8 -0.32,-0.56 -0.08,-1.12 -0.32,-1.6q-0.16,-0.96 -0.16,-1.76c0,-0.48 -0.56,-0.64 -0.8,-0.32q-0.56,0.32 -0.96,0.72c-0.24,0.48 0.48,0.48 0.64,0.8l0.08,1.2c0.08,0.48 -0.4,0.48 -0.8,0.56q-0.72,0.32 -1.2,-0.24 -0.16,-0.56 -0.16,-1.2 -0.08,-1.12 -0.24,-2.16 -0.16,-1.2 -0.24,-2.56c0,-0.4 -0.08,-0.96 -0.64,-0.88m-0.48,6.56l0.24,0l0,0.64l-0.24,0.08 -0.32,0.08l-0.16,0q-0.24,-0.24 0.08,-0.64zM304.96,299.76c-0.24,0 -0.64,0.56 -0.96,0.48 -0.72,0.08 -0.64,0.8 -0.08,1.04l0,0.64c0.08,0.64 -0.32,1.2 -0.24,1.84q-0.08,0.96 -0.24,2.08 -0.32,1.12 -0.32,2.16c-0.08,0.64 -0.56,0.4 -0.96,0.24l0,-0.8q0,-0.96 -0.96,-0.96 -0.8,-0.08 -0.64,-0.88 0.48,-0.32 1.2,-0.24c0.8,0.16 0.72,-0.88 0.32,-1.28s-0.96,-0.8 -1.12,-1.28q0,-1.04 -0.96,-1.68c-0.88,-0.08 -1.6,0.64 -1.84,1.44q-0.64,0 -1.12,0.32c-0.56,0.16 -1.36,1.12 -0.56,1.52 0.4,0.08 1.76,0.4 1.12,0.96q-0.56,0.8 -1.52,0.48c-0.56,0 -1.2,-0.32 -1.2,-0.96q0,-0.96 -0.32,-1.84c-0.16,-0.64 -0.88,-0.48 -0.96,0.16q-0.8,0.8 -0.08,1.6 0.24,0.96 -0.24,1.76c-0.16,0.8 -0.96,0.8 -1.52,0.96 -0.24,0.16 -1.28,0 -0.88,0.56a3.2,3.2 0,0 0,1.92 0.24q0.96,-0.16 1.6,-1.12c0.4,-0.4 1.12,-0.16 1.76,-0.16 0.56,0 1.2,0.4 1.84,0.08 0.16,-0.4 0.96,-1.2 1.2,-0.4q0.16,1.12 1.2,0.96c0.72,0 0.4,0.48 0.4,0.96q0.16,1.04 1.2,1.44l0.64,0q0.96,-0.24 1.44,-1.12 0.24,-0.88 0.4,-1.76 0.24,-1.36 0.32,-2.72 0.24,-1.2 0.32,-2.56l0.24,-1.84q0,-0.48 -0.4,-0.32m-5.36,3.28q0.16,0 0.16,0.4t0.4,0.64l0,0.24l-0.64,0.16q-0.56,0 -0.96,-0.4l-0.16,-0.16 0.24,-0.16 0.4,-0.4q0.32,-0.32 0.56,-0.32m52.8,-6.32 l-1.36,0.24c-0.8,0 -1.2,0.8 -0.4,1.28 0.48,1.2 1.2,-0.32 2,-0.16 1.12,0.16 1.2,1.44 1.44,2.4q0.08,1.28 0.56,2.64c0.8,0.88 -0.56,1.44 -1.12,0.8 -0.48,-0.56 -1.6,-1.2 -2.16,-0.48 -0.72,0.32 -0.8,1.28 -1.36,1.6 -0.96,0.24 -1.04,-0.96 -1.6,-1.44 -0.48,-0.72 -1.36,-0.8 -2.08,-0.96 -0.32,-0.72 -0.16,-1.92 -0.8,-2.4 -0.64,0.24 -1.76,1.36 -0.8,1.92 0.8,0.8 -0.4,1.12 -0.8,1.6q-0.88,1.12 -0.8,2.4c-1.04,0.56 -1.2,-0.64 -1.36,-1.36 0,-0.88 -0.8,-0.64 -1.36,-0.32q-1.04,0.64 -1.6,1.6l0,1.6c0.16,0.72 0.96,0.4 1.44,0.32 0.8,-0.32 1.12,0.56 0.48,1.04 -0.48,0.48 -1.76,0.24 -1.6,1.12l1.28,0c0.8,-0.24 1.76,-0.88 1.84,-1.84 0.16,-0.8 1.36,-0.72 2,-0.88 0.8,-0.24 1.84,-0.32 2.08,0.8 0.56,0.56 1.76,1.2 2.4,0.4a1.6,1.6 0,0 0,0.88 -1.84c-0.08,-0.64 1.04,-0.64 1.36,-0.24 0.4,0.64 1.68,0.48 2.32,0s0.8,-1.28 1.76,-1.28l4.32,-1.2c1.12,-0.24 -0.16,-0.96 -0.48,-1.44 -0.8,-0.4 -1.6,1.36 -2.56,0.48 -0.8,-0.56 -0.72,-1.6 -0.96,-2.48 -0.24,-1.12 -0.16,-2.4 -0.96,-3.2q-0.8,-0.8 -2,-0.72m-5.44,7.6q0.16,0 0.4,0.16 0.48,0.32 0.56,0.8 0,0.16 -0.24,0.24l-0.4,0.16q-0.4,-0.08 -0.56,-0.4l0,-0.32q-0.24,-0.4 0,-0.48zM352.16,304.64q0.4,0 0.64,0.24 0.16,0.4 -0.32,0.64l-0.4,0.16 -0.32,-0.24q-0.24,-0.08 -0.24,-0.32 0.08,-0.4 0.64,-0.56zM344.96,304.64q0.24,0 0.32,0.16 0.24,0.16 0.24,0.48l0,0.48q0.16,0.24 -0.48,0.16c-0.64,-0.08 -0.56,0 -0.56,-0.48q0,-0.4 0.24,-0.56t0.32,-0.16zM340.64,305.84 L340.96,306.16q0,0.32 -0.16,0.48 -0.48,0.16 -0.8,0l0,-0.08q-0.24,-0.16 0,-0.24l0,-0.08l0.16,-0.24a0.8,0.8 0,0 1,0.48 -0.16m7.36,0.96 l0.4,0.08l0,0.48l-0.96,0l-0.08,-0.16q0,-0.24 0.16,-0.32zM334.32,310.64q-0.24,0 -0.08,-0.16 0.24,-0.32 0.64,-0.48a2.4,2.4 0,0 0,1.12 -0.8q0.16,-0.4 0.32,-0.8l-0.08,-1.12a1.6,1.6 0,0 0,-0.64 -0.8q-0.48,0 -0.64,-0.48 0.08,-0.32 0.32,-0.72l0.48,-0.96q0.48,-0.24 0.64,0.16l0.4,0.8q0.32,0.32 0.48,0.8 0.24,0.48 0.32,0.8l0.16,1.04q0,0.64 -0.16,1.28l-0.48,0.64q-0.32,0.4 -0.8,0.56 -0.32,0.24 -0.72,0.32L334.4,310.72zM328.8,310.96q-0.24,-0.16 -0.24,-0.48c0,-0.32 0,-0.24 0.24,-0.48q0.32,-0.48 1.12,0t0.8,0.08q0.16,-0.32 0.8,-0.4l0.4,0.16q0.24,0.16 0.24,0.56c0,0.4 0,0.4 -0.24,0.64q-0.24,0.16 -0.56,0.16a0.8,0.8 0,0 1,-0.64 -0.48q0,-0.32 -0.16,-0.08l-0.16,0.08q-0.32,0.08 -0.64,0.4l-0.4,0.08q-0.32,0 -0.56,-0.24m-15.84,-7.12q-0.96,0.08 -1.2,1.04c0.16,0.56 0.64,1.2 0.32,1.76 0.24,0.8 -0.64,1.12 -1.2,0.72q-0.32,-1.2 -1.04,-2.4c-0.64,-0.16 -0.88,0.88 -1.36,1.28 0.24,0.56 0.88,1.12 0.96,1.84q0.08,1.28 -0.88,1.92c-0.56,0.56 -1.36,0.4 -2,0.48 -0.8,0.48 0.56,0.64 0.96,0.64q1.28,0.24 2.4,-0.64c0.56,-0.24 0.48,-1.36 1.2,-1.44l3.84,0.24c0.64,0.16 1.68,-0.08 2.16,0.4 0,0.8 0.8,1.44 1.52,1.76 0.4,0.16 0.8,-0.4 1.2,-0.48 0.8,-0.24 0.56,-1.2 1.04,-1.6l4,0c0.16,0 0.24,-0.4 0.48,-0.56l-0.16,-1.52c-0.24,-0.64 0,-1.44 -0.4,-2 -0.72,-0.16 -1.28,0.4 -1.92,0.48q-1.28,0.56 -1.44,1.84c-0.4,0.72 -1.12,-0.16 -0.8,-0.8l-0.08,-0.8c-0.4,-0.4 -1.12,-0.24 -1.68,-0.32l-2.4,-0.16q-1.2,0.08 -2.16,-0.08c-0.8,0 -0.8,-0.8 -0.8,-1.44zM314.56,306.8 L315.36,306.88l1.04,0q1.2,0.24 2.4,0.16 0.24,0 0.48,0.32l0,1.6q0,0.4 -0.16,0.64l-0.16,0.16 -0.56,-0.4l0,-1.12l-0.56,-0.24 -2.08,-0.08 -1.6,-0.08q-0.4,0 -0.72,-0.32 -0.16,-0.32 0.32,-0.56zM323.6,307.04q0.16,0 0.16,0.24l0.16,0.4q0,0.16 -0.16,0.16 -0.64,0.24 -0.96,0 0,-0.24 0.16,-0.48z"
      android:strokeLineJoin="round"
      android:strokeWidth="1.04"
      android:fillColor="#c09300"
      android:strokeColor="#00000000"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M320,261.76c26.08,-20 23.84,-49.44 23.84,-49.44l-2,0.16c-5.52,0 -18.56,-3.2 -21.6,-7.12 -3.2,3.6 -16.64,7.2 -22.08,7.2l-2,-0.24s-2.32,29.36 23.84,49.44z"
      android:strokeWidth="0.88"
      android:fillColor="#fff"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="M341.76,214.4l-0.64,0c-4.96,0 -16.48,-2.48 -20.96,-6.32 -4.64,3.52 -16.4,6.4 -21.28,6.4l-0.64,-0.08a58.4,58.4 126.11,0 0,2.08 14.96,56.8 56.8,98.74 0,0 19.68,29.68 56.8,56.8 58.02,0 0,19.68 -29.76,58.4 58.4,62.91 0,0 2.08,-14.88z"
      android:strokeWidth="0.72"
      android:fillColor="#fff"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="M311.52,212a49.6,49.6 0,0 1,-13.28 2.4l0.08,3.28a58.4,58.4 0,0 0,2 11.6,56.8 56.8,123.64 0,0 11.2,21.44zM328,212l0,39.36a56.8,56.8 109.65,0 0,11.68 -22.08,58.4 58.4,0 0,0 2,-11.6l0.08,-3.2l-0.64,0c-3.04,0 -8.32,-0.96 -13.12,-2.56z"
      android:fillColor="#c09300"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M329.84,202.4c0.56,0.08 -0.72,-2.8 -0.72,-2.8 1.44,1.44 6.72,1.84 6.72,1.84 -3.2,-1.44 -6.4,-12.08 -6,-20.64 0.32,-8.48 -1.2,-11.84 -2.4,-13.12 -1.6,-1.6 -6.8,-3.04 -10.16,-3.2 -2,-0.08 -1.6,1.44 -1.6,1.44 -3.6,-0.88 -7.2,-1.28 -8.8,-0.16 -1.44,0.96 -1.76,6 -0.64,5.12 2.64,-2.16 4.96,-0.16 6.56,2.16 1.44,2 1.36,7.76 -0.72,14.56a48,48 117.28,0 1,-8 14.16c3.2,0 7.68,-2.8 7.68,-2.8l-1.04,4.4c3.36,-1.6 6,-4.08 6,-4.08l3.2,3.36c1.04,-1.44 3.2,-3.36 3.2,-3.36s2.64,2.8 6.72,3.2z"
      android:strokeWidth="0.96"
      android:fillColor="#fff"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="M316.88,182.08s-1.76,13.12 -5.12,16.8m8,-17.2s-0.8,13.36 -3.04,17.6m5.52,-17.04s0,14.56 0.8,17.04m2.4,-16.32s0.64,12.24 3.68,16.64"
      android:strokeWidth="1.04"
      android:fillColor="#00000000"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="M313.6,175.68a6.4,6.4 0,0 0,-0.8 -2.64c-1.6,-2.4 -3.92,-4.32 -6.56,-2.16 0,0 0.88,-2.8 2.88,-2.88 1.44,-0.08 4.88,1.12 7.92,6.24 0,0 -2.24,-0.48 -2.8,0 -0.96,0.8 -0.56,1.44 -0.56,1.44z"
      android:strokeWidth="0.24"
      android:fillColor="#c09300"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="M305.92,167.44q0.32,-1.28 1.04,-1.68c1.6,-1.12 5.12,-0.8 8.72,0.16 0,0 -0.32,-1.52 1.6,-1.44 3.36,0.16 8.48,1.6 10.08,3.2a6.4,6.4 0,0 1,1.2 1.92c-0.8,-1.12 -3.04,-1.04 -3.6,-0.96 -0.8,0.08 -1.36,0 -2.48,0.32q-0.96,0.24 -1.84,0.64c-0.32,0.32 -0.64,1.28 -1.12,1.28 -0.8,0 -0.8,-0.16 -1.04,-0.4q-0.32,-0.8 -0.72,-0.8 -1.28,0.16 -4,-2c-1.84,-1.44 -2.56,-1.76 -4.8,-1.6 -2.4,0.16 -3.04,1.52 -3.04,1.52z"
      android:strokeWidth="0.24"
      android:fillColor="#c09300"
      android:strokeColor="#c09300"/>
  <path
      android:pathData="M319.04,168.56m-0.96,0a0.96,0.96 135,1 1,1.92 0a0.96,0.96 0,1 1,-1.92 0"
      android:strokeWidth="1.04"
      android:fillColor="#fff"
      android:strokeColor="#00000000"/>
</vector>
