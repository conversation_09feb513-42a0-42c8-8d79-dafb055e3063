<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:fillColor="#d0103a"/>
  <path
      android:pathData="M0,0h435.2v480H0z"
      android:fillColor="#fedf00"/>
  <path
      android:pathData="M0,0h204.8v480H0z"
      android:fillColor="#0018a8"/>
  <path
      android:pathData="M300.4,136.6c7.7,0 10.9,6.6 18.6,6.6 4.7,0 7.5,-1.5 11.7,-3.9 2.9,-1.6 4.7,-2.5 8,-2.5 3.4,0 5.5,1 7.3,4 1,1.6 1.8,4.9 1.3,6.7a40,40 0,0 1,-2.7 8.3c-0.7,1.6 -1.3,2.5 -1.3,4.2 0,4.1 5.6,5.5 9.4,5.6 0.8,0 7.7,0 12,-4.2 -2.3,-0.1 -4.9,-2 -4.9,-4.3 0,-2.6 1.8,-4.3 4.3,-5.1 0.5,-0.1 1.3,0.3 1.7,0 0.7,-0.3 0.4,-1 1,-1.4 1.2,-1 2,-1.6 3.6,-1.6q1.3,-0.1 2.5,0.7 0.5,0.7 1,0.8c1.2,0 1.8,-0.8 3,-0.8a5,5 0,0 1,2.3 0.6c0.6,0.3 0.6,1.5 1.4,1.5 0.4,0 2.4,-0.9 3.5,-0.9 2.2,0 3.4,0.8 4.8,2.5 0.4,0.5 0.6,1.4 1,1.4a6,6 0,0 1,4.8 3c0.3,0.4 0.7,1.4 1.1,1.5 0.6,0.3 1,0.2 1.7,0.7a6,6 0,0 1,2.8 4.8q-0.1,1.2 -0.5,2.2c-1.8,6.5 -6.3,8.6 -10.8,14.3 -2,2.4 -3.5,4.3 -3.5,7.4 0,0.7 1,2.1 1.3,2.7 -0.2,-1.4 0.5,-3.2 2,-3.3a4,4 0,0 1,4 3.6l-0.3,1.8a10,10 0,0 1,4 -1.4h1.9c3.3,0 7,1.9 9.3,3.8a21,21 0,0 1,7.3 16.8c-0.8,5.2 -0.3,14.8 -13.8,18.6 2.5,1 4.2,3 4.2,5.2a4.5,4.5 0,0 1,-4.4 4.7,4 4,0 0,1 -3.5,-1.4c-2.8,2.8 -3.3,5.7 -3.3,9.7 0,2.4 0.4,3.8 1.4,6s1.8,3.5 3.7,5.1q1.3,-2.4 4,-2.6 2.7,-0.1 3.9,2.2c0.2,0.5 0,0.9 0.3,1.4 0.3,0.6 0.8,0.7 1.1,1.3 0.5,1 0,1.8 0.5,2.7 0.3,0.7 0.9,0.8 1.2,1.4 0.4,1 0.5,1.6 0.5,2.7 0,3 -2.7,5.2 -5.7,5.2 -1,0 -1.4,-0.4 -2.3,-0.3 1.7,1.7 3,2.5 4.3,4.5a18,18 0,0 1,3 10.3,22 22,0 0,1 -2.8,11.2 20,20 0,0 1,-7 8.5,35 35,0 0,1 -16,6.4 74,74 0,0 1,-11 1.4l-14.1,0.8c-7.2,0.4 -12.2,1.5 -17.3,6.6 2.4,1.7 4,3.5 4,6.4q-0.2,4.7 -4.7,6.2c-0.7,0.2 -1.2,0 -1.9,0.4s-0.7,1.3 -1.4,1.7a6,6 0,0 1,-3.8 1,8 8,0 0,1 -6.4,-2.5c-2.2,1.8 -3,3.4 -5.5,4.9 -0.8,0.4 -1.2,1 -2.1,1 -1.5,0 -2.2,-1 -3.4,-1.8a23,23 0,0 1,-4.4 -4c-2.3,1.3 -3.6,2.4 -6.3,2.4a7,7 0,0 1,-4 -1c-0.6,-0.5 -0.8,-1.2 -1.5,-1.6s-1.3,-0.3 -2.1,-0.7c-3,-1.3 -5,-3.5 -5,-6.8 0,-2.9 1.8,-4.7 4.4,-6 -5,-5 -10,-5.8 -17,-6.2l-14,-0.8c-4.4,-0.3 -6.8,-0.7 -11,-1.4 -3.3,-0.5 -5.2,-0.7 -8.2,-2.1 -10.2,-4.8 -16.8,-11.3 -18,-22.5 -0.2,-1 -0.2,-1.5 -0.2,-2.5 0,-5.8 2.3,-9.4 6.4,-13.5 -1,-0.3 -1.7,0 -2.8,-0.3 -2.5,-1 -4.4,-2.7 -4.4,-5.5q-0.1,-1.4 0.5,-2.6c0.4,-0.6 1,-0.7 1.2,-1.4 0.2,-1 0,-1.6 0.4,-2.5 0.3,-0.5 0.8,-0.6 1,-1.2 1,-1.9 2,-3.4 4.1,-3.4q2.7,0.1 3.8,2.5c1.8,-0.8 2.2,-2.1 3.2,-3.7a16,16 0,0 0,1.4 -13.3c-0.4,-1.5 -0.6,-2.5 -1.8,-3.7q-1.4,1.4 -3.4,1.4c-2.9,0 -5,-2.5 -5,-5.3a5,5 0,0 1,3 -4.6c-1.6,-1.4 -3,-1.5 -4.7,-2.6 -2.6,-1.6 -3.5,-3.4 -5.2,-6 -1.2,-1.6 -1.5,-2.8 -2,-4.7a19,19 0,0 1,-1 -7.8c0.6,-5 1.5,-8 4.6,-11.9 1.8,-2.3 3,-3.7 5.8,-4.9 2.3,-1 3.7,-1.7 6.2,-1.7l2,0.1a7,7 0,0 1,2.8 0.8c0.4,0.2 1.1,0.9 1.1,0.4s-0.3,-0.8 -0.3,-1.3c0,-2 1.5,-4 3.6,-4 1.5,0 2.1,1.4 2.9,2.7q0.6,-1 0.7,-2.3c0,-3.4 -1.9,-5.2 -4,-7.9 -4.7,-5.8 -10.5,-8.5 -10.5,-16q0,-3.2 3,-4.9c0.5,-0.3 1.3,0 1.8,-0.3s0.4,-1 0.7,-1.4q0.7,-0.9 1.6,-1.6c1,-1 2,-0.6 3.1,-1.5q0.8,-0.7 1.2,-1.4c1.3,-1.6 2.5,-2.4 4.6,-2.4q1.3,-0.1 2.5,0.4l1,0.5q0.5,-0.5 1.5,-1.1a4,4 0,0 1,2.2 -0.6c1.1,0 1.8,0.6 3,0.6q0.3,-0.2 0.8,-0.6c1,-0.7 1.5,-1 2.7,-1s1.8,0.3 2.8,1c1,0.5 1,1.3 2,1.8q0.8,0.3 1.5,0.4c2.6,0.9 4.5,2.6 4.5,5.3q0.1,2.1 -1.4,3.5c-0.9,0.7 -1.7,0.6 -2.8,1a16,16 0,0 0,11.3 3.5c4.2,0 9.3,-1.7 9.3,-5.9 0,-2 -1,-3 -1.8,-4.8a19,19 0,0 1,-2.1 -8.5c0,-2.8 0.3,-4.5 1.9,-6.7s3.6,-2.9 6.5,-2.9"
      android:fillColor="#c7b37f"/>
  <path
      android:pathData="M272.4,159a4,4 0,0 0,2.4 2.4c0.8,0.3 2.7,0.2 3.8,-1.4 1,-1.2 1,-2.8 0.6,-4a5,5 0,0 0,-1.7 -2.2z"
      android:strokeLineJoin="round"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M401,236.1c-1.2,-2.9 -4.3,-1.6 -4.4,0 -0.5,3.7 2.7,4.8 5,4.2a4,4 0,0 0,2.5 -2q1,-1.6 0.4,-3.7l-0.8,-1.6 -1.3,-1.2q-1.2,-0.7 -3.4,-0.6c-5.5,0 -10.4,6.5 -12,13.4 -0.6,2.2 -1.3,7.3 -0.3,12a22,22 0,0 0,5.9 11.3,26 26,0 0,0 9.9,5.8 8,8 0,0 0,4 0.1c3.2,-0.7 4.7,-3.8 3,-7 -1.3,-2.5 -5.3,-4 -7.2,-0.6q-0.3,0.5 -0.4,1.5c0,0.9 0.4,2 1,2.4 1.5,0.9 3.8,0.6 3.7,-2"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M383.8,274a11,11 0,0 1,6.6 -3.7q4.4,-0.4 8.2,2a19,19 0,0 1,10.8 17c0,3.6 -1,7.5 -2,9.4 -0.8,1.7 -3,9 -15.3,14 -7.1,3 -18,3.6 -25.7,4 -10.4,0.3 -20,0.7 -25.5,7.6"
      android:strokeWidth=".8"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M386.4,285.7q-0.4,-1.5 0.8,-3.3c1.2,-1.6 3.7,-2.1 6,-1a7,7 0,0 1,2.5 2.2l1.1,1.6q1,1.7 1,2.5c2.5,7 -1.4,14.5 -6.5,17.6 -4,2.4 -8.7,3.4 -14.4,4 -2.5,0.4 -4,0.3 -6.5,0.5h-16.8c-2.9,0.3 -5,0.4 -7.6,0.8q-2.4,0.3 -5.4,1 -0.9,0 -1.8,0.4l-1.2,0.3q-5.5,1.6 -9.8,4.2 -1.3,0.7 -2.5,1.7l-1.3,1.2c-2,2 -3.9,4 -4.4,6.7v1.6c0,1.8 1.4,4.3 5.4,5m5.5,-170c0.8,1.4 1.3,2.3 0.8,3.9q-0.9,2.7 -3.6,2.8c-4,0 -6.3,-4.8 -4.5,-7.8 3.2,-5.3 9.3,-2.3 15,0.3 -0.3,-1.3 -0.8,-1.8 -0.7,-3.5 0.1,-4.2 3.2,-6 4.5,-10 0.7,-2.3 1,-4.3 -0.7,-6q-2.2,-1.8 -5.1,-0.6c-3.8,1.5 -8.5,5.9 -16.6,6 -8.2,-0.1 -12.8,-4.5 -16.7,-6q-3,-1.2 -5.1,0.7c-1.7,1.6 -1.4,3.6 -0.7,6 1.3,3.8 4.4,5.7 4.5,10 0,1.6 -0.4,2 -0.7,3.4 5.7,-2.6 12,-5.9 15,-0.3 1.7,3.2 -0.5,7.7 -4.5,7.7q-2.7,0 -3.6,-2.7 -0.5,-2.2 0.8,-4"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M314.6,159.9a5,5 0,0 1,2.4 5c-0.2,2.5 -0.8,3.1 -2.8,4.5m2.4,-3.8q0,2.2 -2.3,3.1"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m276.7,153.3 l0.7,0.5 0.8,0.8 0.5,1 0.2,0.8v1.9l-0.2,0.8 -0.5,0.6 -0.6,0.6 -0.9,0.5 -1,0.2 -1,0.2 -1,-0.5 -0.9,-0.6 -0.5,-0.8 -0.4,-1v-0.4z"
      android:fillColor="#c7b37f"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M275.2,157.2c-0.3,-1.7 -2.2,-2 -3,-1 -1.1,1.5 -0.3,4 2,4.7a4,4 0,0 0,3.9 -1.4c1,-1.3 0.9,-2.8 0.5,-4a5,5 0,0 0,-1.7 -2.2c-2.7,-2 -7.1,-1.6 -8.6,2 -1.8,4.4 2.2,7.8 6,10.3 4.6,3.2 10,3.8 14,3.8 9.2,-0.1 16.2,-4.5 20.7,-7q1.7,-0.9 2.7,0.2a2,2 0,0 1,-0.3 2.7"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m248,281.2 l-2,0.7 -2,1.6 -1,1.3 -1.1,2 -0.5,1.5 -0.4,1.8 -0.2,1.4m19,-10.1 l-0.1,1.8 -0.3,1.2 -1,2.2 -1.3,1.8 -1.5,1.2 -1.1,0.5 -1.6,0.4"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M319.7,329.1c-0.3,1.7 -1.9,3.6 -5.3,4.2l-0.6,0.2"
      android:strokeWidth=".8"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M404.2,276.2a18,18 0,0 1,5.6 13.5c0,3.6 -1,7.5 -2,9.4 -0.8,1.7 -3,9 -15.3,14a85,85 0,0 1,-25.6 4c-10.3,0.3 -19.8,0.7 -25.4,7.3"
      android:strokeWidth=".9"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M387.5,282.9c0.8,-1 3.5,-2.4 5.8,-1.1a6,6 0,0 1,2.3 2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="m401.6,273.8 l1.4,0.5a7,7 0,0 0,4 0c2.8,-0.8 4.6,-3.4 3.2,-6.9a6,6 0,0 0,-1.8 -2.1"
      android:strokeWidth=".9"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M240.3,199.8c-2,1.1 -3.3,1.4 -4.8,3.1a28,28 0,0 0,-2.6 6.8m46,-51.7q-0.1,2.7 -3,3.2"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M397.1,192a19,19 0,0 1,18.6 19.8c0,16 -9.9,18.5 -13.8,19.6"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M398.4,192c8.1,-0.3 16.5,5.7 16.9,20.7 0.3,11.7 -8,17 -12,18"
      android:strokeWidth=".7"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="m393.8,248.4 l0.1,-1.6 0.6,-2.5 0.7,-2 0.9,-1.6 1,-1.3m7.8,-3.4v1.5l-0.5,1 -0.7,1.1 -0.8,0.6 -1.2,0.5h-1.1l-0.8,-0.1m-14.3,-52.8 l0.3,-1.7 0.8,-1.6 1,-1.5 1.6,-2.2 1.4,-1.4 2,-2.2 2,-1.9 1.1,-1.3 1.5,-1.9 1.4,-2 0.8,-1.7 0.5,-2.2 0.1,-2.7 -0.2,-0.8m-12.3,128.2 l1.6,-0.4 1.2,-0.6 0.7,-0.7 0.5,-0.8 0.3,-1.2v-0.9m-158.2,-12.1h2.7l1.6,-0.6m5,-36.5 l-0.2,1.4 -0.4,0.6 -0.4,0.6 -0.7,0.5 -0.7,0.3 -1,0.1h-0.6m9.9,-15.5 l-0.3,2.1 -0.5,1 -0.8,1.2 -1.2,0.9 -1.2,0.6 -2.3,0.5m15.3,-39.7 l-0.5,1.3 -0.5,1 -0.8,1 -1,1 -1.2,0.5 -1.1,0.3 -0.6,-0.1m0.3,-6.2v1"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M254.3,224a7,7 0,0 1,-2.1 1.4m150.5,44.8 l0.5,0.2c1.4,0.8 4.2,-0.2 3.4,-2.4"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M397.8,239.6c1,1.3 2.9,1.7 4.4,1.3a4,4 0,0 0,2.5 -2q1,-1.6 0.4,-3.7l-0.9,-1.6 -1.3,-1.5 -0.4,-0.2m6.4,34 l0.1,-0.7a4,4 0,0 0,-1.3 -3l-0.8,-0.8m0.4,0.5c0,-1.8 -1.5,-3.2 -3.4,-3.5m-4.2,2.8 l-1.3,-1a16,16 0,0 1,-4.3 -10.7c0,-4.2 1.6,-8.4 3.6,-10M341.2,324l1.8,-1.6 1.2,-1 2.3,-1.4 2.2,-1 1.6,-0.5 3,-0.6 3.6,-0.6m-29.5,19.4a17,17 0,0 1,-7.6 6.1,18 18,0 0,1 -7.6,-6.1"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M314.4,332.6a10,10 0,0 1,-2.2 4.2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m314.7,330.5 l-0.4,2.2M312,337l-1,1 -1.7,0.9 -2,0.6m-5.6,-177.8c0.3,-0.8 0.5,-1.4 0.5,-2.6 -0.1,-4.2 -3.2,-6.1 -4.5,-10 -0.7,-2.3 -1,-4.3 0.7,-6q2.2,-2 5,-0.6c4,1.5 8.6,5.8 16.7,6 -8.1,-0.2 -12.8,-4.5 -16.6,-6 -2,-0.8 -3.8,-1 -5.3,0.5 -1.7,1.6 -1.2,3.8 -0.5,6.1 1.3,3.9 4.2,5.8 4.3,10q-0.1,1.6 -0.5,2.6M320,148c8,-0.4 14.9,-5.8 17.1,-6.3 2,-0.4 3,-0.2 4.5,1.1 -1.4,-1.3 -3,-1.2 -5,-0.5 -3.8,1.5 -8.4,5.8 -16.6,6m79.6,112.9a16,16 0,0 1,-6.2 -12.4c0,-4.1 1.7,-8.4 3.6,-10m-70,97.6c-1.3,2 -4.3,5 -7.6,6.2a18,18 0,0 1,-7.6 -6.2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="m306.7,163.7 l2.3,-1.3c1,-0.6 2.3,-0.5 2.9,0.2s0.7,2 -0.2,2.8"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M294.7,169.3c5.5,-1.2 10,-3.6 13.4,-5.5M340.3,328c0.5,0.3 0.8,1 0.8,1q0.2,0.3 0.3,0.8c0.3,1.5 -0.7,2.4 -2,2.6 -1.7,0.2 -3,-0.8 -3.5,-2M294.4,169c5.5,-1.1 10,-3.6 13.4,-5.5m97.6,106.9c-1,0.4 -1.6,0.3 -3,-0.2l-1.8,-1a21,21 0,0 1,-8.4 -9,19 19,0 0,1 -1.7,-4.6 12,12 0,0 1,-0.5 -3.3,26 26,0 0,1 4.7,-15.3c1.1,-1.6 2.1,-2.5 4.2,-2.6m-143.7,-39.3a7,7 0,0 1,2.7 5.7c0,3.1 -2.6,8.2 -9,10a8,8 0,0 1,-6.3 -0.8"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M256.3,205.6q1.6,1 1.6,3.3 0,1.6 -1.9,3.7a12,12 0,0 1,-8.8 4q-3,0.1 -6,-1.7a9,9 0,0 1,-3.8 -5.4"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M256.2,212.3q1.8,1.8 1.7,4.6 0.1,3.9 -3.7,7 -0.8,0.8 -2,1.5m129.5,-22.1v3.5m-0.3,-4.4v5m0.3,-15.8v6.6m-0.3,-8v8.9m-1.9,82a19,19 0,0 1,-4.2 5.6,20 20,0 0,1 -5.8,4.1 25,25 0,0 1,-6.6 2.2,33 33,0 0,1 -6.8,0.9c-2.5,0 -3.9,0 -6.4,-0.2s-4,-0.6 -6.7,-0.8c-2.2,-0.2 -3.4,-0.4 -5.6,-0.3a28,28 0,0 0,-11 1.8c-2.6,1 -5.7,3 -6.3,3.8a22,22 0,0 0,-6.4 -3.8,22 22,0 0,0 -5.1,-1.4c-2.3,-0.4 -3.5,-0.4 -5.8,-0.4s-3.4,0.1 -5.6,0.3c-2.6,0.3 -4,0.6 -6.7,0.8 -2.5,0.2 -3.9,0.3 -6.4,0.2a33,33 0,0 1,-13.4 -3,20 20,0 0,1 -6.4,-4.8m42.1,53.4 l1.8,-0.2m30.3,-2.4 l1.8,-0.1 1.7,-0.7 1.2,-0.8 1.7,-2 0.3,-0.6 0.3,-1.7v-0.8m47,-136.7c0.7,-2.6 -0.2,-5.4 -2.8,-5.3m-132,46.5a8,8 0,0 1,-3.5 4.7m3.6,-46.7a7,7 0,0 1,-3.6 4c-1.9,0.8 -4,0 -5.2,-0.8"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M243.8,202.4c1.5,0.8 3.1,-0.4 2.8,-2.4a3,3 0,0 0,-2.5 -2.2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M250.2,286.6q0.3,0.5 0.8,0.8c0.7,0.2 1.2,0.4 1.9,-0.5 0.8,-1.1 0.3,-2.8 -0.5,-3.9a5,5 0,0 0,-5.8 -1q-1.2,0.6 -2.6,2.2l-1.1,1.6q-1,1.7 -1.1,2.4c-2,5.9 0.4,12 4.1,15.7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="m340.2,327.8 l0.7,0.8 0.2,0.9c0.3,1.5 -0.7,2.4 -2,2.6 -1.6,0.2 -2.8,-0.8 -3.3,-2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M389.4,154.8a7.4,7.4 0,0 1,6.3 7c0,4.4 -1.5,6 -3.8,9.2 -2.5,3.4 -10.7,9.6 -10.7,16.7q-0.2,6.4 4.3,8.4c2,1 4.3,0 5.4,-1 2.6,-2.4 1.5,-6.5 -1.2,-7 -3.2,-0.6 -3.9,4.6 -0.7,4.3m17.9,69a4,4 0,0 0,-3.6 -3,3.7 3.7,0 0,0 -3.7,3.7q0,1.6 1,2.6"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M383.9,195.1a7,7 0,0 0,-2.7 5.7c0,3.1 2.6,8.2 9,10 2.4,0.7 4.8,0.6 6.2,-0.3m-156,-10.3a9,9 0,0 0,-4.8 3.5,17 17,0 0,0 -2.2,12.7 16,16 0,0 0,2.3 5.6l1,1.2 1.2,1m64,92c4.9,2.1 8.4,3.7 11.4,8.5a10,10 0,0 1,1.2 4.9c0,2.7 -1,5.7 -3.3,7.6a8,8 0,0 1,-6.7 2c-1.9,-0.2 -3.7,-1.6 -4,-2.6M254,224.1c2.7,2.2 3.9,4.2 3.9,7.5a8,8 0,0 1,-4 7.5"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M251.5,236.4c4,5.1 6.3,8.1 6.4,14.1 0.1,5.7 -1.7,9.6 -5,13.7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M329.8,169.3a4,4 0,0 0,1.5 -2.2q0.8,-2.2 -0.2,-4 1.3,2 0.7,4c-0.1,1 -0.8,1.5 -1.6,2.3m51.5,86.1v16.2l-0.1,2.5 -0.3,1.7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M381.4,254v19.9l-0.5,2.6m0.5,-43v14.6m0.3,-13.4v11.8m0,-26.8v8.8m-0.3,-9.9v11m0.3,-19v3.5m-0.3,-4.2v5m-1.8,65.2 l-0.4,0.7a19,19 0,0 1,-4.1 5.7,20 20,0 0,1 -5.9,4 25,25 0,0 1,-6.5 2.2c-2.7,0.6 -4.2,0.8 -6.9,0.9 -2.5,0 -3.9,0 -6.3,-0.2 -2.7,-0.2 -4.1,-0.5 -6.8,-0.8 -2.2,-0.2 -3.4,-0.3 -5.6,-0.3s-3.5,0 -5.7,0.4a22,22 0,0 0,-5.2 1.4c-2.7,1.1 -5.7,3 -6.4,3.8 -0.6,-0.8 -3.7,-2.7 -6.3,-3.8a22,22 0,0 0,-5.2 -1.4c-2.2,-0.4 -3.5,-0.4 -5.8,-0.4s-3.4,0.1 -5.6,0.3c-2.6,0.3 -4,0.6 -6.7,0.8 -2.5,0.2 -3.9,0.3 -6.3,0.2a33,33 0,0 1,-13.5 -3,20 20,0 0,1 -5.8,-4.1l-2.5,-2.8m-2,-3.2a10,10 0,0 1,-2.3 7.7c-0.8,0.9 -2.6,2.6 -5,2.6 -3.7,0 -4.8,-2.5 -5,-3.2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M255.6,278.9q1,1 1.9,2.5c1,1.8 0.6,4.8 -0.1,6.2l-0.3,0.4m-20.3,18q3.3,3.8 10.9,7.1c7.1,3 18.1,3.6 25.7,4 10,0.3 19.3,0.7 25,7m17.3,-4a12,12 0,0 1,4 5.5m-7.3,11.5 l-0.7,0.7a8,8 0,0 1,-6.6 2c-2,-0.2 -3.8,-1.6 -4.3,-2.6m-5.4,-2.9 l0.3,0.4a8,8 0,0 0,5.1 2.4m27,0a18,18 0,0 1,-7.7 6.1,18 18,0 0,1 -7.6,-6.1l-0.3,-0.5m15.6,0.4 l0.7,0.7a8,8 0,0 0,6.7 2,6 6,0 0,0 4,-2.5l0.5,-0.7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="m339,336.6 l-0.7,1.2 -1.1,1 -1.7,0.7h-1.6"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M343,325.3a8,8 0,0 1,2.4 2.9q0.4,1 0.5,2.3a6,6 0,0 1,-1.5 4.2,8 8,0 0,1 -5.4,2.4h-0.4m0.2,-0.2a7,7 0,0 1,-5.2 -2.2m63.7,-67.9a24,24 0,0 1,-4.8 -6.4,19 19,0 0,1 -1.7,-4.5 12,12 0,0 1,-0.5 -3.3,26 26,0 0,1 4.6,-15.3c0.7,-0.8 1.4,-1.8 2.1,-2.2m-1.3,-75.9c2.5,0.2 4.8,3 4.8,5.7 0,3.8 -1.3,5.5 -4.4,9.3 -2.6,3.2 -10.6,9 -10.3,14.5q0.1,1.6 1.1,2.8m-3.2,3.5a7,7 0,0 0,2 1.4,5 5,0 0,0 4.3,-0.3M369,153a6,6 0,0 1,2.2 2.6c1.8,4.5 -2.2,7.9 -6,10.4a21,21 0,0 1,-8.3 3.3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M364.6,161.6a4,4 0,0 1,-3.1 -1.5l-0.7,-1m-15,4.9 l-1.2,-1q-1.7,-1.4 -0.8,-4.4c0.6,-1.9 3.7,-7.2 3.8,-10.9 0.2,-5.6 -2,-9 -5.3,-10.2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="m347.3,146.5 l-0.1,2 -0.6,2.2 -1,3 -1,1.9 -0.8,1.9 -0.4,1.3 -0.2,1 0.1,0.9m38,126.3 l0.6,0.8c0.7,1 3.2,3 5.5,3 3.7,0 4.6,-2.6 4.7,-3.2 0.5,-2.9 -0.5,-3.6 -2,-4.5 0,0 -0.8,-0.4 -1.9,-0.2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M237,274.4a7,7 0,0 1,-3.7 0c-2.9,-0.9 -5.2,-3.6 -4,-7m13.4,-31.8q0.4,0.5 0.4,1c0.4,3.8 -2.8,4.8 -5,4.2a6,6 0,0 1,-3 -2.3,5 5,0 0,1 -0.7,-2.3m22,-23.6q0.9,0.7 1.3,1.7m-1.1,-8.5q0.8,0.7 1.1,1.3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M257.9,210.5a9,9 0,0 1,-1.6 2.4,12 12,0 0,1 -8.8,4q-3,0.1 -6,-1.7a10,10 0,0 1,-4 -5.6"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M255.4,195.3a8,8 0,0 1,2.4 3.4"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M257.8,203.2c-0.9,3 -3.5,6.6 -8.6,7.9 -2.4,0.6 -5.6,-0.2 -6.6,-1"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M240,202.6c0.3,2.6 2,4.6 5.4,4.6 4.7,0.1 7.6,-6.7 3.4,-11.5"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M229.4,225.5q1,1.3 2.4,2.4a17,17 0,0 0,6 3.3m5.2,0.5c4.2,-0.5 6.6,-3.7 6,-7.3 -0.3,-2.8 -2.8,-5 -4.6,-5.1"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M249.8,188.1c1.9,0 3,1.6 2.9,3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M249.4,163a12,12 0,0 0,5 5.9m144.2,31c1.7,2.3 0.6,7 -4,7a5,5 0,0 1,-4.5 -2.5"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M381.7,169.1V185"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M243.8,202.3c1.4,1 3.3,-0.7 2.5,-2.6 -0.5,-1.2 -2.2,-2.6 -4.7,-0.9 -2.8,1.9 -2,7.8 3.2,7.9 4.7,0 7.6,-6.8 3.4,-11.6 -4,-4.6 -11.3,-3.6 -16,0.2A21,21 0,0 0,225 207a23,23 0,0 0,0 9.2,21 21,0 0,0 3,7.5l1.3,1.7c0.8,0.8 1,1.2 2,2a15,15 0,0 0,10.4 3.7c4.6,-0.2 7.3,-3.4 6.8,-7.3 -0.4,-3.8 -4.2,-5.7 -6.7,-3.9 -1.7,1.2 -2.3,4.9 0.7,5.8 1.6,0.5 3.1,-1.7 2,-3M374,150.9q4,-2.2 6.3,1a10,10 0,0 1,1.6 7.2,9 9,0 0,1 -3.5,5.8"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M380.5,152c3.1,-2 6.5,-1.1 8.3,1.6 1.3,2 1.7,3.6 1.6,6.1a11,11 0,0 1,-5.7 9.2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M395,159.2c2.6,0.2 4.6,2.5 4.6,5.1 0,3.8 -1,5.5 -4,9.3 -2.7,3.3 -10.6,9 -10.4,14.6 0,2.1 1.8,4 3.3,4.2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M395.4,202.3c-1.5,1 -3.3,-0.6 -2.5,-2.4 0.5,-1.2 2.2,-2.8 4.7,-1.1 2.7,1.9 2,7.8 -3.3,7.9 -4.7,0 -8,-6.6 -3.4,-11.6 4,-4.6 11.7,-3.7 16.5,0.1 2,1.6 6.1,6 7,12 1,7 0.9,15.6 -6.4,21 -3,2.1 -7,3.1 -10.6,3 -4.6,-0.2 -7.3,-3.5 -6.8,-7.4 0.5,-3.8 4,-5.4 6.7,-3.9s2.3,5.4 -0.7,5.8c-1.7,0.2 -3.1,-1.7 -2,-3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M392.9,199.9c0.8,-3.5 3.7,-3.8 6.2,-3.8 6.5,0.1 11.1,8 11.2,15.5 0,9.5 -4,15.2 -11,15.5 -1.9,0 -5,-0.8 -5,-3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M397,198.3c6.9,1.6 9.3,7.8 9.3,13.8 0,4.9 -0.5,11.6 -10,13.9"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="square"/>
  <path
      android:pathData="M408.4,265.3a3.9,3.9 0,1 0,-6.3 2.4"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M394.4,259.4c1.4,2 3,4.1 6.3,6m-1.3,10.5c-3.2,-2.2 -9.5,-5 -15,-2.2a8,8 0,0 0,-4.4 4.4,10 10,0 0,0 1.8,9.5c0.9,1 2.7,2.6 5,2.7 3.8,0 4.7,-2.6 4.8,-3.2 0.4,-2.8 -1.2,-3.9 -2,-4.1 -0.7,-0.3 -2.8,-0.2 -3.2,1.3q-0.3,0.9 0.2,2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M340.5,328.4c1,2.2 -0.2,3.2 -1.6,3.4 -2.2,0.3 -3.3,-1.4 -3.4,-3a4.4,4.4 0,0 1,4.3 -4.7c2.3,0 4.1,1.5 5,3.5q0.5,1 0.5,2.4a6,6 0,0 1,-1.4 4.1,8 8,0 0,1 -5.4,2.5c-4.2,0.1 -7.5,-3.8 -7.5,-7.8 0,-7.7 11.4,-12 16,-13a84,84 0,0 1,17.9 -2.4c3.5,-0.1 6.2,0 10.1,-0.5 3.5,-0.3 5.4,-0.5 9,-1.3a27,27 0,0 0,12.6 -6.4c2.9,-2.7 4.5,-4.5 5.9,-8.2a17,17 0,0 0,-1.3 -13.9,14 14,0 0,0 -10.3,-6.8c-3.7,-0.5 -7,1.1 -9,4.8 -1,1.8 -0.6,4.8 0.1,6.2a6,6 0,0 0,4.8 3c3.8,0 4.7,-2.6 4.8,-3.2 0.4,-2.8 -1.2,-3.9 -2,-4.2 -0.7,-0.2 -2.8,-0.1 -3.2,1.4q-0.3,0.9 0.2,2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M337.2,316.2c-4.8,2.1 -8.4,3.7 -11.4,8.5a10,10 0,0 0,-1.2 4.9c0,2.7 1.1,5.7 3.3,7.6a8,8 0,0 0,6.7 2c2,-0.2 3.7,-1.6 4,-2.6"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M385.1,224.1c-2.3,0.8 -3.9,4.2 -3.9,7.5a8,8 0,0 0,4 7.5"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M387.6,236.4c-4,5.1 -6.3,8.1 -6.4,14.1 0,5.7 1.7,9.6 5.1,13.7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m365.9,152 l0.3,-0.5c1.7,-2.4 4.7,-3.1 6.9,-1.5 2.6,2 3.3,5.4 2.6,9q-0.9,3.3 -4,5.5"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M265.1,150.8q-3.9,-1.9 -6.3,1a9,9 0,0 0,-1.6 7.2c0.6,2.7 1.4,3.8 3.5,5.8"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M258.6,152a6,6 0,0 0,-8.3 1.6,9 9,0 0,0 -1.6,6.1c0.2,4.2 2.8,7.6 5.8,9.2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M249.7,154.8a7,7 0,0 0,-6 6.6c0,4.5 1,6.3 3.5,9.6 2.5,3.4 10.7,9.6 10.7,16.7q0.2,6.4 -4.3,8.4c-2,1 -4.3,0 -5.4,-1 -2.6,-2.4 -1.5,-6.5 1.2,-7 3.3,-0.6 3.9,4.6 0.7,4.3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M244,159.2c-2.5,0.2 -5,2.3 -5,5 0,3.8 1.5,5.6 4.6,9.4 2.6,3.3 10.1,9 9.9,14.5 0,2 -1.5,4.6 -2.9,4.3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M238,236.1c1.3,-2.9 4.4,-1.6 4.6,0 0.4,3.7 -2.8,4.8 -5.1,4.2a4,4 0,0 1,-2.5 -2,5 5,0 0,1 -0.4,-3.7l0.9,-1.6 1.2,-1.2q1.3,-0.7 3.4,-0.6c5.5,0 10.4,6.5 12,13.4 0.6,2.2 1.3,7.3 0.3,12a22,22 0,0 1,-5.8 11.3,26 26,0 0,1 -10,5.8 7,7 0,0 1,-3.9 0.1c-2.8,-0.9 -4.6,-3.5 -3.2,-7 1.2,-2.6 5.4,-4 7.3,-0.6q0.3,0.5 0.4,1.5c0,0.9 -0.4,2 -1,2.4 -1.4,0.9 -3.7,0.6 -3.6,-2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M233.8,270.4c1,0.4 1.6,0.3 2.9,-0.2l1.8,-1c2.6,-1.5 5.6,-3.8 8.4,-9.1a19,19 0,0 0,1.7 -4.5q0.4,-1.6 0.6,-3.3a26,26 0,0 0,-4.8 -15.3c-1.1,-1.6 -2,-2.5 -4.2,-2.6m-9.5,31a3.9,3.9 0,1 1,6.3 2.3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M232.2,261.4a4,4 0,0 1,3.7 -3,3.7 3.7,0 0,1 3.6,3.7 4,4 0,0 1,-1 2.6"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M239.4,261.3a16,16 0,0 0,6.2 -12.4c0,-4.1 -1.6,-8.4 -3.6,-10"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M244.7,259.4a17,17 0,0 1,-6.3 6"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M254.6,273.7q-1.4,-3.2 -5.8,-3.5 -4.3,-0.3 -8.2,1.9a19,19 0,0 0,-10.8 17,25 25,0 0,0 2,9.5c0.9,1.6 3,9 15.3,14a86,86 0,0 0,25.7 3.9c10.4,0.4 20,0.8 25.6,7.6"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M239.7,275.9c3.3,-2.2 9.5,-5 15.1,-2.2a8,8 0,0 1,4.3 4.4,10 10,0 0,1 -1.8,9.5c-0.9,1 -2.7,2.6 -5,2.7 -3.8,0 -4.7,-2.6 -4.8,-3.2 -0.4,-2.8 1.2,-3.9 2,-4.2 0.7,-0.2 2.8,-0.1 3.2,1.4q0.4,0.9 -0.2,2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M252.7,285.7q0.6,-1.6 -0.8,-3.3a5,5 0,0 0,-6 -1q-1.1,0.6 -2.4,2.2 -0.7,0.7 -1.2,1.6 -1,1.7 -1,2.5c-2.5,7 1.5,14.4 6.5,17.6 4.4,2.8 8.8,3.6 14.4,4 2.5,0.3 4,0.3 6.5,0.5h16.8c3,0.3 5.1,0.4 7.6,0.8q2.5,0.3 5.4,1 0.9,0 1.8,0.4l1.2,0.3q5.5,1.6 9.8,4.2 1.3,0.7 2.5,1.7l1.3,1.2c2,2 4,4 4.4,6.7v1.6c0,1.8 -1.4,4.3 -5.3,5"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M298.6,328.4c-1,2.2 0.2,3.2 1.6,3.4 2.2,0.3 3.3,-1.4 3.5,-3a4.4,4.4 0,0 0,-4.4 -4.7,6 6,0 0,0 -5,3.5 7,7 0,0 0,-0.5 2.4,6 6,0 0,0 1.4,4.1 8,8 0,0 0,5.4 2.5c4.2,0.1 7.5,-3.8 7.5,-7.8 0,-7.7 -11.4,-12 -16,-13a84,84 0,0 0,-17.9 -2.4c-3.5,-0.1 -6.2,0 -10.1,-0.5 -3.5,-0.3 -5.4,-0.5 -9,-1.3a27,27 0,0 1,-12.5 -6.4,17 17,0 0,1 -4.7,-22 14,14 0,0 1,10.3 -6.9q5.9,-0.7 9,4.8c1,1.8 0.6,4.8 -0.1,6.2a6,6 0,0 1,-4.8 3c-3.8,0 -4.7,-2.6 -4.8,-3.2 -0.4,-2.8 1.2,-3.9 2,-4.2 0.7,-0.2 2.8,-0.1 3.2,1.4q0.3,0.9 -0.2,2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="m273.3,152 l-0.4,-0.5c-1.7,-2.4 -4.7,-3.1 -6.9,-1.5 -2.6,2 -3.3,5.4 -2.5,9a9,9 0,0 0,4 5.5"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M366.8,159.6c-4,4.4 -8.1,5.8 -14.1,6 -2,0 -5.5,-0.6 -7.6,-2.1 -1.3,-1 -2.8,-2.6 -1.9,-5.5 0.6,-1.9 3.7,-7.2 3.8,-10.9 0.3,-5.6 -1.9,-8.7 -5.3,-9.9 -6.2,-2.2 -13,4 -17,5.4 -2.1,0.7 -3.2,0.8 -5.1,0.8 -2,0 -3,-0.1 -5.2,-0.8 -4,-1.4 -10.7,-7.6 -17,-5.4 -3.4,1.2 -5.5,4.3 -5.3,10 0.1,3.6 3.2,9 3.8,10.8 1,2.9 -0.5,4.5 -1.9,5.5 -2,1.5 -5.7,2.1 -7.5,2 -6,-0.1 -10.1,-1.5 -14.1,-5.9"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M297.3,314.4c0.8,0.3 0.2,-0.2 5.3,2a22,22 0,0 1,11.3 8.9,11 11,0 0,1 0.9,7.3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M297.7,336a8,8 0,0 0,3.2 0.9c4.2,0.1 7.5,-3.8 7.5,-7.8 0,-2.8 -1.5,-5.2 -3.6,-7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M298.6,328.4c-1,2.3 0.4,3.5 1.8,3.7 2.2,0.2 3.4,-1.4 3.6,-3a5,5 0,0 0,-2.2 -4.2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M390.1,154.8c3.2,0 6,3.6 6,7.2 0,4.3 -2.2,6.9 -3.9,8.8q-1.9,2.3 -4.4,4.7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M386.3,151.4a9,9 0,0 1,2.8 2.4c1.3,2 1.7,3.7 1.6,6.2 -0.2,4.2 -3.2,7.1 -6,9m-4.7,-17.6 l0.6,0.7c1.9,2.2 2,5.4 1.6,7.2a8,8 0,0 1,-3.8 5.4m-5,-14.4c2.6,2 3.4,5.4 2.5,9q-1,3.6 -4.2,5.2m11.1,41.1c0.3,1 0.9,1.3 1.5,2a14,14 0,0 0,6.2 3.5q3.7,0.9 6.3,-0.9m-163,54q2,0.1 3.3,2.3 0.3,0.4 0.4,1.5 0,1.5 -1,2.2c-1.5,1 -4,0.5 -4,-2"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M241.5,231.3c5,1 9.7,6.9 11.2,13.3 0.6,2.3 1.3,7.3 0.3,12a22,22 0,0 1,-6 11.4l-2.1,1.9 -1,0.7m-8,-12.1c2,0 3.8,1.9 3.8,4a4,4 0,0 1,-1 2.6"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M234.6,260.7c2.1,0 4.1,2 4.1,4.2a4,4 0,0 1,-1.4 3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M254,239.5a18,18 0,0 1,3.8 7.7m0,8.5a17,17 0,0 1,-1.5 4,18 18,0 0,1 -3.6,4.7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M254.3,224.3q2.7,2.2 3.5,4.8"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M257.9,219.5a10,10 0,0 1,-3.4 4.6m-9.2,-17.2 l2.2,-0.6 1.3,-1 0.8,-1.1 0.7,-1.8 0.3,-1.5"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M241,199.3q-1.1,0.3 -2.5,0.8a9,9 0,0 0,-3.5 3,17 17,0 0,0 -2.2,12.7 16,16 0,0 0,2.3 5.6l1,1.4c1.4,1.3 2.6,2 4.6,1.7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M253,189.8c-0.3,1.3 -1,2.9 -3,2.7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M245.7,198.5c-2,-1.9 -6,-2.4 -10.1,0.2L234,200l-1.4,1.6a18,18 0,0 0,-2.4 5c-0.7,3 -0.7,5.6 -0.6,6.3q0,1.5 0.3,2.7 1,4.2 2.3,6.2c0.9,1.5 3,5 7.7,5.4 1.8,0.1 4.8,-0.7 5,-3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M363.8,157c0.3,-1.6 2.3,-1.9 3,-1 1.2,1.6 0.4,4.2 -2,4.9a4,4 0,0 1,-3.8 -1.4c-1,-1.3 -0.9,-2.8 -0.5,-4q0.4,-1.2 1.7,-2.2c2.7,-2 7.1,-1.6 8.6,2 1.8,4.4 -2.2,7.8 -6,10.3 -4.6,3.2 -10,3.8 -14,3.7 -9.2,0 -16.1,-4.4 -20.7,-7q-1.7,-0.7 -2.7,0.3a2,2 0,0 0,0.3 2.7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M365.6,155.5c1,0 1.2,0.4 1.5,0.8 1.2,1.5 0.3,4.1 -2,4.9m17.8,51.5c-3.5,3.8 -0.2,10.3 2.4,11.8 0.9,0.7 1.3,0.3 2,0.7"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M383.1,205.4q-1.5,1 -1.6,3.3a5,5 0,0 0,1.4 4,14 14,0 0,0 9.3,3.7q3,0.1 6,-1.7a9,9 0,0 0,3.8 -5.4m-20.8,61.8 l-0.2,2.5a19,19 0,0 1,-2 7,19 19,0 0,1 -4.2,5.6 20,20 0,0 1,-5.9 4,25 25,0 0,1 -6.5,2.3 44,44 0,0 1,-13.2 0.6c-2.7,-0.2 -4.1,-0.5 -6.8,-0.8 -2.2,-0.1 -3.4,-0.3 -5.6,-0.3a28,28 0,0 0,-10.9 1.9c-2.7,1 -5.7,3 -6.4,3.8 -0.6,-0.9 -3.7,-2.8 -6.3,-3.8a22,22 0,0 0,-5.2 -1.5c-2.2,-0.4 -3.5,-0.4 -5.8,-0.4s-3.4,0.2 -5.6,0.4c-2.6,0.2 -4,0.6 -6.7,0.7 -2.5,0.2 -3.9,0.3 -6.3,0.2a33,33 0,0 1,-7 -0.8,25 25,0 0,1 -6.5,-2.2 20,20 0,0 1,-5.8 -4.1,19 19,0 0,1 -4.2,-5.7 19,19 0,0 1,-2 -6.9c-0.2,-1 -0.2,-2.5 -0.2,-2.5V169.3h123.2z"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M248,285.6a2.5,2.5 0,1 1,5 0,2.5 2.5,0 0,1 -5,0zM232.5,268q0.2,-2.1 1.8,-2.3c1.6,-0.2 1.7,1 1.7,2.3q-0.2,2 -1.7,2.2 -1.6,-0.2 -1.8,-2.2z"
      android:strokeWidth=".3"
      android:fillColor="#c7b37f"
      android:strokeColor="#c7b37f"/>
  <path
      android:pathData="M241.3,223.6q0.2,-1.7 1.7,-1.8 1.6,0.2 1.7,1.8c0.1,1.6 -0.7,1.8 -1.7,1.8s-1.7,-0.8 -1.7,-1.8M272,158c0,-1 0.5,-2 1.4,-2q1.5,0 1.8,1.6c0,1 -0.5,2 -1.4,2q-1.4,0 -1.8,-1.6"
      android:fillColor="#c7b37f"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="M239.3,234q-0.6,0.1 -0.8,0.5 -0.4,0.3 -0.6,0.9l-0.2,1.2m4.7,26.7 l1,-1 0.6,-1 0.5,-1 0.7,-1.3m-1.3,14 l-1.5,0.7 -1.1,0.6 -1.3,0.8 -1.2,1m15,-37.9 l-0.8,-0.8 -1,-0.8 -0.9,-0.8"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#c7b37f"
      android:strokeLineCap="round"/>
  <path
      android:pathData="m254.2,225 l-1.2,0.5 -1.5,0.3"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#c7b37f"
      android:strokeLineCap="butt"/>
  <path
      android:pathData="M237.4,208.4q0.2,0.8 0.5,1.5 0.3,1 0.9,1.7a8,8 0,0 0,2.6 2.7l1.5,0.8m-1,-5.8 l1.3,0.6a7,7 0,0 0,3 0.6l1.8,-0.1m7.2,-40.7 l-2,-1.2q-1.2,-0.7 -2,-1.5l-1.1,-1.3 -0.8,-1.3m7.5,-4.6 l0.6,1.7 1.4,2c1,1 1.7,1.3 2.8,2.2m1.4,-6q0.3,0.9 0.7,1.6t0.8,1.2l1.3,1.3q1,0.7 2,1.1"
      android:strokeWidth=".6"
      android:fillColor="#00000000"
      android:strokeColor="#c7b37f"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M333.3,151.6c0,-1.7 -1.7,-1.8 -2.4,-1.8 -1.8,0 -2.3,1.1 -4.6,2.3a12,12 0,0 1,-6.7 2,12 12,0 0,1 -6.7,-2c-2.3,-1.2 -2.7,-2.3 -4.6,-2.3a2.3,2.3 0,0 0,-2.2 2.4v0.9l0.3,0.2q0,-1 0.5,-1.7a2,2 0,0 1,1.6 -0.8c1.8,0 2.5,1.2 4.8,2.4 3,1.6 4.2,1.9 6.7,2a12,12 0,0 0,6.8 -2c2.3,-1.2 3,-2.5 4.8,-2.5q0.9,0 1.3,1v0.9l0.2,0.1q0.1,-0.3 0.2,-1z"
      android:strokeWidth=".2"
      android:fillColor="#703d29"
      android:strokeColor="#703d29"/>
  <path
      android:pathData="M264.4,294c0.5,-0.5 0.9,-0.3 1,-0.6q0,-0.2 -0.3,-0.3l-0.9,-0.2 -0.8,-0.4q-0.2,-0.1 -0.5,0c-0.1,0.4 1,0.4 0.6,1.4l-0.8,1.2 -2.6,3 -0.2,0.1v-4.3l0.1,-1.8c0.2,-0.4 0.8,0 0.9,-0.4q0,-0.2 -0.3,-0.3t-1.1,-0.3l-1,-0.5q-0.4,-0.1 -0.6,0l0.1,0.3q0.5,0.2 0.5,1v7.4q0,0.7 0.2,0.7 0.1,0 0.4,-0.3z"
      android:fillColor="#703d29"/>
  <path
      android:pathData="M267.5,295.2c0.3,-1.1 1,-0.4 1,-0.8q0.1,-0.2 -0.2,-0.3l-1.3,-0.4q-0.6,-0.1 -1.2,-0.4 -0.1,0 -0.4,0c-0.1,0.5 1.1,0.5 0.8,1.5l-1.7,5.5c-0.3,1 -1,0.6 -1.1,1v0.1l1.2,0.4 1.6,0.5h0.3c0.2,-0.4 -1.2,-0.3 -0.7,-1.7zM271.2,296.2q0.3,-0.7 0.9,-0.4 1.6,0.6 1,2.5c-0.2,0.6 -0.4,1.2 -2,0.8q-0.6,-0.1 -0.6,-0.5l0.7,-2.3zM268.4,301.2c-0.5,1.4 -1.2,0.8 -1.3,1.2q0,0.2 0.3,0.3l1.6,0.4 0.8,0.3h0.4c0.1,-0.5 -1,-0.3 -0.7,-1.5l0.6,-2q0,-0.6 0.6,-0.3 0.8,0.1 0.8,0.8l0.3,2q0.1,1.4 1,2 1,0.1 1.4,-0.4l-0.2,-0.2h-0.3s-0.3,0 -0.3,-0.3l-0.7,-3.6q0.1,-0.2 0.8,-0.3a2,2 0,0 0,1 -1.3c0.1,-0.5 0.4,-2.2 -1.8,-2.9l-2.1,-0.5 -1.2,-0.4h-0.3c-0.1,0.5 1.1,0.4 0.7,1.7zM276.8,303.7c-0.4,1.4 -1.4,0.5 -1.5,1q0,0.3 0.3,0.3l1.5,0.3 1.4,0.4q0.4,0.2 0.6,-0.1c0,-0.3 -1.3,-0.3 -1,-1.8l1.3,-5.2q0,-0.8 0.6,-0.5l1,0.2c1.1,0.3 0.5,1.5 1,1.6q0.2,-0.1 0.2,-0.6l0.1,-1v-0.4l-3.3,-0.7 -3.2,-0.8q-0.2,0 -0.2,0.2l-0.5,1.5q-0.2,0.3 0,0.4c0.5,0.1 0.5,-1.5 1.7,-1.2l0.9,0.2q0.6,0 0.4,0.8zM289.5,300.4c0.4,-0.6 0.8,-0.5 0.9,-0.7q0,-0.2 -0.4,-0.3h-0.9l-0.9,-0.3q-0.3,-0.1 -0.4,0.1c-0.1,0.4 1,0.2 0.8,1.3q0,0.3 -0.6,1.3l-2,3.3 -0.3,0.2v-0.2l-0.7,-4 -0.1,-1.8c0,-0.5 0.7,-0.2 0.7,-0.5q0.1,-0.2 -0.4,-0.3l-1.1,-0.1q-0.6,0 -1,-0.3 -0.4,-0.1 -0.6,0.1l0.1,0.2q0.7,0.2 0.7,0.9l1.3,7.3q0.2,0.7 0.3,0.7t0.4,-0.3zM290.1,307.2q0,0.3 0.2,0.5 0.8,0.4 1.7,0.7c1.4,0.2 2.6,-0.7 2.8,-2.2 0.3,-1.5 -0.3,-2.1 -1.4,-2.9 -1.3,-0.9 -1.8,-1.1 -1.7,-2q0.3,-1 1.4,-1c1.8,0.3 1.6,2.6 1.8,2.6q0.4,0 0.3,-0.4l0.2,-1.6v-0.4h-0.6c-0.4,0 -0.7,-0.5 -1.6,-0.7q-2,-0.1 -2.5,2 -0.1,1.6 1.2,2.4c1.6,1.1 2.2,1.4 2,2.4q-0.3,1.5 -1.7,1.3c-1.2,-0.2 -1.6,-1.4 -1.8,-2.6q0,-0.3 -0.2,-0.3 -0.2,0.1 -0.2,0.5v1.7zM305.9,302.7c0.3,-0.7 0.8,-0.6 0.8,-0.9q0,-0.2 -0.4,-0.2h-0.9l-0.9,-0.1q-0.3,0 -0.4,0.2c0,0.4 1,0 1,1.1q0,0.3 -0.5,1.4l-1.8,3.5 -0.1,0.3 -0.1,-0.3 -1.1,-4 -0.3,-1.6c0,-0.5 0.7,-0.3 0.7,-0.6q0.1,-0.2 -0.4,-0.2h-1.2l-1,-0.2q-0.4,-0.1 -0.6,0.1l0.2,0.2q0.6,0.2 0.7,0.8l2.1,7.1 0.4,0.7q0.2,0.1 0.3,-0.4z"
      android:fillColor="#703d29"/>
  <path
      android:pathData="M307.6,308.5c0,1.2 -1,1 -1,1.5q0,0.2 0.3,0.1h2.2l0.4,-0.1c0,-0.6 -1.4,0.2 -1.4,-2v-4.2l0.1,-0.1 0.2,0.1 5.1,6.3 0.3,0.1 0.2,-0.3v-6.7c0,-1.3 1,-1 1,-1.3q0,-0.1 -0.3,-0.2h-2.3q-0.2,0 -0.2,0.2c0,0.4 1.3,0.2 1.3,1.3v4l-0.1,0.4 -0.4,-0.3 -4.2,-5.3q-0.1,-0.4 -0.4,-0.3h-1.8l-0.2,0.1c0,0.6 1.2,-0.2 1.2,2.1zM318,303c0,-1.1 0.8,-0.7 0.8,-1.1q0.1,-0.2 -0.4,-0.2h-2.6s-0.3,0 -0.3,0.2c0,0.4 1.1,0 1.1,1.2v5.7c0,1.1 -0.8,0.8 -0.8,1.2q0,0.1 0.2,0.2h2.8q0.3,0 0.3,-0.2c0,-0.4 -1.2,0.2 -1.2,-1.3zM322.5,308.5c0,1.5 -1.2,1 -1.2,1.4q0,0.3 0.4,0.2h3q0.5,0 0.5,-0.3c0,-0.3 -1.4,0 -1.4,-1.4L323.8,303q-0.1,-0.7 0.5,-0.6h1c1.2,-0.1 0.8,1.2 1.3,1.2q0.2,-0.1 0.1,-0.6l-0.1,-1q0,-0.3 -0.2,-0.4l-3.3,0.1h-3.3l-0.2,0.3 -0.1,1.6 0.1,0.4c0.5,0 0.2,-1.6 1.4,-1.6h0.9q0.5,-0.1 0.6,0.6v5.6zM328.8,306.3h-0.4l0.1,-0.5 0.7,-2.2v-0.2l0.2,0.1 1,2.1 0.2,0.4q0,0.3 -0.4,0.2zM330.6,306.8c0.3,0 0.3,0 0.8,1l0.2,0.8c0,0.7 -0.7,0.6 -0.7,1q0,0.2 0.4,0h1.2l1.3,-0.1q0.4,0 0.4,-0.2c0,-0.4 -0.6,0 -1,-0.7l-3.4,-7 -0.3,-0.4q-0.2,0 -0.3,0.4L327,309c-0.2,0.7 -0.8,0.7 -0.7,1h2.3q0.4,0 0.5,-0.3c0.1,-0.3 -1.2,0 -1.3,-0.9l0.2,-1q0.3,-1 0.6,-0.8l2.1,-0.2zM338.9,301.8c-0.1,-0.8 0,-0.8 1.2,-1 2,-0.2 1.4,1.3 2,1.2q0.2,-0.1 0,-0.6l-0.1,-1.1q0,-0.2 -0.3,-0.2 -1.4,0 -2.4,0.3l-2.8,0.4q-0.3,0 -0.3,0.2c0.1,0.5 1.3,0 1.4,1l0.7,5.5c0.2,1.5 -0.7,1 -0.6,1.5q0,0 0.2,0l1.4,-0.1 1.2,-0.1q0.5,0 0.5,-0.3c0,-0.3 -1.2,0.1 -1.4,-1.2l-0.2,-1.7q-0.2,-1 0.3,-1h0.8c1.1,-0.2 1,1.1 1.3,1q0.4,-0.2 0.1,-0.5l-0.3,-2.1q-0.1,-0.4 -0.2,-0.3c-0.3,0 -0.1,1.1 -1,1.2l-0.7,0.1q-0.6,0.2 -0.6,-0.5zM342.9,304.6c0.4,2.3 2.1,3.7 4.2,3.3 3.4,-0.7 3.5,-3.6 3.2,-5.3 -0.5,-2.5 -2.3,-3.7 -4.4,-3.3 -2.5,0.5 -3.5,2.7 -3,5.3m1.1,-1c-0.3,-1.6 0,-3.4 1.7,-3.7 1.4,-0.3 3,0.8 3.4,3.4 0.3,2 0,3.6 -1.8,4s-3,-2 -3.3,-3.6zM352.3,299.5q0,-0.9 0.6,-0.9 1.6,-0.2 2.1,1.6c0.2,0.7 0.3,1.4 -1.3,1.8q-0.5,0.1 -0.8,-0.2l-0.5,-2.3zM352.3,305.2c0.4,1.4 -0.5,1.3 -0.5,1.6q0.2,0.3 0.4,0.1 0.8,-0.1 1.6,-0.4l1,-0.2q0.3,0 0.2,-0.2c0,-0.4 -1,0.3 -1.3,-1l-0.5,-2c0,-0.4 -0.2,-0.4 0.4,-0.5q0.6,-0.3 1.1,0.3l1.3,1.6c0.5,0.6 1,1.3 1.8,1.1q0.9,-0.2 1,-0.9l-0.2,-0.1 -0.3,0.1s-0.3,0.1 -0.4,0l-2.4,-2.9 0.5,-0.6q0.4,-0.6 0.2,-1.6c-0.1,-0.5 -0.7,-2.1 -3,-1.6l-2.1,0.6 -1.2,0.2q-0.3,0 -0.2,0.2c0,0.5 1.1,-0.2 1.4,1zM361,303.2c0.3,1.4 -1,1.2 -0.9,1.6q0.1,0.3 0.5,0.2l1.4,-0.5 1.5,-0.3q0.5,0.1 0.4,-0.4c0,-0.3 -1.3,0.4 -1.7,-1l-1.3,-5.3q-0.2,-0.7 0.3,-0.7l1,-0.2c1.1,-0.4 1.1,1 1.5,0.9s0,-0.5 0,-0.7l-0.4,-1s0,-0.3 -0.2,-0.2l-3.2,0.9 -3.2,0.7v0.3l0.1,1.6q0,0.4 0.3,0.4c0.5,-0.1 -0.3,-1.6 1,-1.9l0.8,-0.2q0.6,-0.2 0.7,0.5zM366.5,295.9c-0.3,-1 0.6,-0.9 0.4,-1.3h-0.3l-1.4,0.4 -1.2,0.3s-0.3,0 -0.3,0.2c0.1,0.4 1.2,-0.2 1.5,0.8l1.6,5.6c0.2,1 -0.6,1 -0.5,1.3q0,0.2 0.2,0.1l1.1,-0.3 1.6,-0.4q0.4,0 0.3,-0.3c-0.1,-0.3 -1.1,0.5 -1.5,-0.9zM368.8,298.6c0.7,2.3 2.6,3.4 4.7,2.7 3.2,-1.1 3,-4.1 2.4,-5.7 -0.8,-2.4 -2.8,-3.3 -4.8,-2.7 -2.4,0.9 -3.2,3.2 -2.3,5.7m1,-1c-0.6,-1.7 -0.6,-3.5 1.1,-4 1.3,-0.5 3,0.4 3.9,2.9 0.6,1.8 0.5,3.6 -1.2,4.2 -1.8,0.6 -3.2,-1.5 -3.8,-3.2zM377.4,292.1q-0.2,-0.9 0.4,-1 1.7,-0.3 2.4,1.4c0.2,0.6 0.4,1.3 -1.1,1.9q-0.6,0.2 -0.8,0zM378.2,297.7c0.6,1.4 -0.4,1.4 -0.2,1.7q0,0.3 0.4,0.1l1.5,-0.7 0.9,-0.2q0.3,-0.1 0.2,-0.3c-0.2,-0.4 -1,0.4 -1.4,-0.8l-0.8,-1.9q-0.4,-0.5 0.3,-0.7 0.6,-0.3 1.1,0.3l1.6,1.4c0.5,0.5 1.1,1.1 2,0.8 0.3,-0.2 0.9,-0.7 0.7,-1l-0.2,-0.1 -0.2,0.2h-0.5l-2.8,-2.5 0.4,-0.7a2,2 0,0 0,0 -1.6c-0.1,-0.6 -1,-2 -3.1,-1.2l-2,0.9 -1.2,0.4 -0.2,0.2c0.2,0.4 1.1,-0.4 1.6,0.8l2,5z"
      android:fillColor="#703d29"/>
  <path
      android:pathData="M264.13,175.55l52.54,0l0,52.48l-52.54,0z"
      android:fillColor="#d52b1e"/>
  <path
      android:pathData="M288.77,216.83s0,1.92 -0.51,3.39c-0.64,1.73 -0.64,1.73 -1.22,2.56a8.32,8.32 0,0 1,-2.43 2.56q-1.92,1.22 -3.84,1.02c-3.46,-0.26 -5.12,-4.1 -5.89,-7.17 -0.83,-3.26 -3.2,-5.12 -4.8,-3.84q-1.28,1.09 -0.19,2.94a5.76,5.76 0,0 0,2.62 1.79l-1.86,2.37s-4.03,-0.51 -4.8,-4.74c-0.32,-1.6 0.45,-4.54 3.14,-5.44 3.39,-1.15 5.5,1.28 6.59,3.33 1.41,2.82 2.05,7.94 6.02,7.17 2.18,-0.45 3.2,-3.58 3.2,-5.06l1.54,-1.66 2.37,0.77z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M292.03,216.83s-0,1.92 0.51,3.39c0.64,1.73 0.64,1.73 1.22,2.56a8.32,8.32 0,0 0,2.43 2.56q1.92,1.22 3.84,1.02c3.46,-0.26 5.12,-4.1 5.89,-7.17 0.83,-3.26 3.2,-5.12 4.8,-3.84q1.28,1.09 0.19,2.94a5.76,5.76 0,0 1,-2.62 1.79l1.86,2.37s4.03,-0.51 4.8,-4.74c0.32,-1.6 -0.45,-4.54 -3.14,-5.44 -3.39,-1.15 -5.5,1.28 -6.59,3.33 -1.41,2.82 -2.05,7.94 -6.02,7.17 -2.18,-0.45 -3.2,-3.58 -3.2,-5.06l-1.54,-1.66 -2.37,0.77z"
      android:fillColor="#fedf00"/>
  <path
      android:pathData="m295.1,194.56 l6.91,-7.49s1.02,-0.83 1.02,-2.18l-1.41,0.26 -0.32,-0.77 -0.06,-0.7 1.92,-0.45L303.17,182.4l0.19,-0.83 -2.05,0.13 0.19,-0.9 0.32,-0.64 1.22,-0.26l1.22,0c1.15,-2.18 5.89,-4.1 9.22,-0.64 2.43,2.56 1.92,7.17 -1.28,8.45a3.84,3.84 0,0 1,-4.35 -0.7l1.28,-2.56c1.73,1.09 3.2,-0.19 3.07,-1.54 -0.13,-1.73 -1.28,-2.75 -2.75,-2.88q-2.24,-0.06 -3.2,1.92c-0.38,0.83 -0.19,1.41 -0.32,2.3 -0.13,0.96 0,1.47 -0.32,2.43a5.76,5.76 0,0 1,-1.54 2.3l-7.04,7.68 -27.52,29.7 -2.05,-1.92z"
      android:fillColor="#fedf00"/>
  <path
      android:pathData="M274.88,197.12s1.73,8.58 7.62,21.44c3.01,-1.09 4.74,-1.79 7.94,-1.79s4.86,0.64 7.87,1.79A109.44,109.44 0,0 0,305.92 197.12l-15.49,-19.84z"
      android:fillColor="#fff"/>
  <path
      android:pathData="m291.9,183.94 l10.75,13.89s-1.41,6.72 -5.76,16.83c-1.73,-0.38 -3.2,-0.7 -4.99,-0.83zM288.9,183.94 L278.14,197.82s1.41,6.72 5.76,16.83c1.73,-0.38 3.2,-0.7 4.99,-0.83z"
      android:fillColor="#fedf00"/>
  <path
      android:pathData="M322.3,175.5h52.6V228h-52.6z"
      android:fillColor="#fedf00"/>
  <path
      android:pathData="M329.7,175.5h7.8L337.5,228h-7.8zM344.7,175.5h7.8L352.5,228h-7.8zM359.7,175.5h7.9L367.6,228h-7.9z"
      android:fillColor="#d52b1e"/>
  <path
      android:pathData="M264.3,273.5q0.1,1.6 1.4,4.3c1,1.5 0.6,1.4 2.7,3.8a15,15 0,0 0,4 2.9,33 33,0 0,0 15,2.6q4,-0.2 6.6,-0.7a71,71 0,0 1,11 -0.6q2.2,0 4.7,0.6c3.5,0.7 7,2 7,2v-54.7h-52.6V271l0.2,2.4z"
      android:strokeWidth=".5"
      android:fillColor="#fedf00"
      android:strokeColor="#00000000"/>
  <path
      android:pathData="m270.4,283.1 l2.5,1.5 3.4,1.2v-52.2h-5.9zM299.6,285.5v-51.9h-5.8v52.8l5.8,-0.7zM311.3,233.6h-5.8v52.1q2.8,0.4 5.8,1zM287.9,233.6L287.9,287s-3.8,0.2 -5.8,0v-53.4z"
      android:strokeWidth=".3"
      android:fillColor="#d52b1e"
      android:strokeColor="#d52b1e"/>
  <path
      android:pathData="M374.72,273.54a13.44,13.44 0,0 1,-1.41 4.22c-0.96,1.47 -0.64,1.47 -2.75,3.84a16.64,16.64 0,0 1,-8.32 4.48,33.28 33.28,57.69 0,1 -10.62,1.02q-4.03,-0.26 -6.59,-0.64c-2.43,-0.38 -4.29,-0.58 -7.04,-0.64l-3.97,0a53.12,53.12 0,0 0,-11.71 2.69L322.3,233.6l52.61,0l0,37.44z"
      android:fillColor="#fedf00"/>
  <path
      android:pathData="m335.74,238.08 l-0.38,0.13 -0.51,0.51q-0.32,0.32 -0.77,0.51l-0.38,0.32c-0.19,0.19 0,0.38 -0.19,0.64q-0.06,0.32 -0.38,0.64t-0.64,0.64l-0.77,0.64 -0.19,0.06l-0.38,0q-0.32,0.26 -0.51,0.51l0.19,0.38 0.51,0.9q0.13,0.38 0.32,0.51 0.45,0.19 0.83,0.06 0.64,0.13 1.28,0.32l0.96,0.51q0.38,0.26 0.83,0.32l1.15,0l0,0.19l1.28,0.64 -0.06,0.26q-0.13,0.32 -0.06,0.51 0.64,1.86 0.96,2.05 0.51,0.26 0.7,0.96l-0.19,0.19q-0.64,0.51 -1.09,1.15c-0.45,0.77 -0.77,0.77 -0.19,1.79l0.96,1.54q0.32,0.64 0.51,1.28t0.19,1.28l0.64,0.19 0.45,-0.38 0.38,-0.77l0,-0.64q-0.19,-0.13 -0.13,-0.45c0,-0.26 0.32,-0.19 0.45,-0.38 0.19,-0.32 -0.26,-0.51 -0.45,-0.7 -0.38,-0.45 -0.9,-0.58 -1.02,-1.22q-0.06,-0.19 0.26,-0.45l1.28,-1.15q0.26,0.13 0.64,0.06l0.83,0.26q0.51,0.06 0.77,0l0.26,0l0.06,0.38c0.06,0.64 -0.06,1.92 0.13,2.24l0.19,0.38 0.13,0.38l0,1.28l-0.13,1.09q0,0.38 -0.32,0.64t-0.64,0.45l0,0.64l0.7,0.32 0.83,0.19 0.45,-0.19 0.06,-0.38 0.32,-0.32q0.45,-0.06 0.58,-0.06 0.13,-0.19 0,-0.51 0,-0.51 -0.19,-1.02l-0.06,-1.79q0,-0.51 0.13,-0.96c0.06,-0.64 0.26,-0.9 0.38,-1.41q0.19,-0.83 0.26,-1.6a15.36,15.36 0,0 0,6.46 -0.38q0.77,0.64 1.73,1.02l0,0.64q0,0.26 0.13,0.45l0.19,0.19q0.26,0 0.45,-0.13t0.13,-0.45l0,-0.45l1.15,0l0,0.7q0.13,0.26 0.32,0.26l0.38,0q0.19,-0.26 0.19,-0.64l0,-0.45l0.64,-0.26l0,0.58l-0.19,0.58c-0.13,0.38 -0.32,0.51 -0.51,0.9q-0.26,0.51 -0.64,0.96l-0.38,0.45 -0.38,0.58 -0.58,0.64c-0.45,0.38 -0.77,0.13 -1.28,0.58l-0.19,0.64 0.9,0.38 0.83,0.13 0.26,-0.13q0,-0.32 0.19,-0.51t0.45,-0.26q0.38,0 0.64,-0.13 0.26,-0.38 0.45,-0.96a8.32,8.32 0,0 1,1.92 -2.5l1.09,-0.9q0.26,-0.26 0.32,-0.64l-0.13,-0.38 -0.13,-0.64c0.96,0.45 0.64,0.45 0.77,0.9 0.19,0.38 0,0.64 0.06,1.09 0.06,0.51 0.32,0.7 0.32,1.22q0,0.7 -0.19,1.47 0,0.64 -0.32,1.28a2.56,2.56 0,0 1,-0.7 0.96l-0.38,0.32 -0.06,0.64 0.7,0.26 1.02,0.26 0.26,-0.19c0.13,-0.45 0,-1.09 0.26,-1.09q0.38,0 0.51,-0.19l0,-0.45l0.45,-2.88 0.26,-1.22 0.26,-1.09c0.45,-1.28 -0.13,-1.47 -0.64,-2.3q-0.45,-0.58 -0.45,-0.96l0,-3.65l0.26,-0.13c0.77,-0.45 1.09,-0.58 1.54,-1.6l0.19,-0.96l0,-0.64l-0.26,-0.64 -0.38,-0.51c-0.45,-0.64 -1.09,-0.7 -1.73,-0.96 -0.96,-0.32 -1.6,-0.26 -2.56,-0.32 -1.15,-0.13 -1.73,-0.13 -2.82,0 -1.28,0 -1.98,0.26 -3.26,0.45l-3.14,0.26c-1.47,0 -2.82,-0.32 -3.71,-0.26 -1.54,0.13 -1.6,0.51 -3.97,0.7l-2.43,0.13 -1.41,-0.45c0.58,-0.19 0.7,-0.32 0.96,-0.64s0.13,-0.45 0.38,-0.7l0.45,-0.64 -0.58,-0.26l-0.64,0l-0.77,0.19 -0.51,0.38 -1.41,-0.77a5.76,5.76 0,0 0,-1.92 -0.58zM337.02,245.63"
      android:fillColor="#d52b1e"/>
  <path
      android:strokeWidth="1"
      android:pathData="m364.03,246.08 l-0.51,0.19q-0.77,0.32 -1.66,0.32c-1.66,0.13 -2.75,-0.7 -4.48,-0.58 -0.9,0.06 -1.28,0.77 -2.24,1.02l-1.09,0.13 0.32,-0.64s-0.77,0.19 -1.28,0.19l-1.02,-0.13 0.64,-0.64 -0.83,-0.13 -0.64,-0.45 1.09,-0.19c0.96,-0.26 1.28,-0.77 2.5,-0.9 0.7,0 1.92,0 4.86,0.51 1.92,0.32 2.82,0.13 3.52,-0.19q0.64,-0.32 0.7,-1.15 0,-0.77 -0.51,-1.15 -0.13,-0.06 -0.7,-0.26"
      android:fillColor="#00000000"
      android:strokeColor="#fedf00"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M335.87,240.38q-0.45,0 -0.83,0.19 -0.38,0.32 -0.64,0.7 0.45,0.19 0.77,0.19t0.51,-0.32q0.26,-0.32 0.26,-0.77z"
      android:strokeWidth="0.32"
      android:fillColor="#fcd900"
      android:strokeColor="#fedf00"
      android:strokeLineCap="butt"/>
  <path
      android:strokeWidth="1"
      android:pathData="m343.04,248.83 l0.64,1.47c0.13,0.51 0,0.77 0.13,1.28l0,1.02m4.35,-4.48 l-0.19,0.83 -0.64,2.24l0,0.45m-7.04,-2.56c0.58,0.13 0.38,2.11 1.22,2.56"
      android:fillColor="#00000000"
      android:strokeColor="#fedf00"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="m358.46,252.67 l0.26,-0.19a5.12,5.12 0,0 0,1.73 -1.15"
      android:fillColor="#00000000"
      android:strokeColor="#fedf00"
      android:strokeLineCap="butt"/>
  <path
      android:strokeWidth="1"
      android:pathData="M353.54,251.52c2.24,-0.58 3.78,-1.66 4.86,-1.86m-2.56,-0.96l0.51,0c0.96,-0.19 1.09,0.38 1.73,0.77 1.22,0.64 1.34,1.47 2.75,2.18l0.26,0.06 0.51,0.26"
      android:fillColor="#00000000"
      android:strokeColor="#fedf00"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M331.33,242.88l0.45,0l0.51,-0.13q0.26,0 0.45,0.13 0.13,0 0.19,0.19t0.06,0.32q0,0.19 -0.38,0.26 -0.19,0 -0.32,-0.19l0,-0.26a0.64,0.64 0,0 1,-0.58 0z"
      android:strokeWidth="0.32"
      android:fillColor="#fcd900"
      android:strokeColor="#fedf00"
      android:strokeLineCap="butt"/>
  <path
      android:pathData="m336.06,249.09 l-1.28,-0.58q0.38,-0.13 0.64,-0.32 0.19,-0.38 0.32,-0.83 0.06,-0.64 0.45,-0.9t0.7,-0.06q0.45,0.06 0.58,0.45 0,0.51 -0.19,0.96 -0.13,0.51 -0.13,0.9t0.26,0.64l-1.28,-0.26zM335.42,249.73a0.38,0.38 135,1 1,0.45 0.32,0.38 0.38,135 0,1 -0.45,-0.38zM334.34,239.1l-0.13,0q-0.26,-0.38 -0.38,-0.77l-0.19,-0.77l0,-1.28q0,-0.32 -0.13,-0.58c0,-0.13 -0.26,-0.19 -0.19,-0.26q0.06,0 0.26,0 0.32,0 0.64,0.26t0.38,0.64l0.26,0.96 0.19,0.51 0.32,0.38 -0.45,0.51zM336.64,245.89 L338.05,246.53a5.76,5.76 0,0 0,2.24 -2.43c0.58,-1.15 0.64,-1.73 0.9,-2.82l-1.15,-0.32l-0.26,0c-0.32,1.15 -0.45,1.73 -1.02,2.69q-0.77,1.28 -1.66,1.92zM339.84,257.54 L340.35,256.7 341.25,256l0.26,0a5.76,5.76 0,0 1,-0.32 1.79l-0.26,0.64 -0.32,0.32c-0.32,-0.51 -0.83,-0.83 -0.83,-1.28zM360.96,258.69 L361.86,259.07 362.82,259.65l0,0.32l-0.96,0.13l-1.47,0l-0.38,-0.26c0.32,-0.45 0.51,-1.02 0.9,-1.15zM354.69,257.41 L355.58,257.73 356.54,258.37q0,0.13 0,0.26a5.76,5.76 0,0 1,-1.73 0.19l-0.64,-0.06 -0.45,-0.19c0.38,-0.45 0.58,-1.09 0.96,-1.15m-11.14,1.34 l0.96,0.32 0.96,0.64l0,0.32a5.76,5.76 0,0 1,-1.79 0.13l-0.64,0l-0.38,-0.26c0.32,-0.45 0.51,-1.02 0.9,-1.15m-5.76,-19.07q-0.64,-0.38 -0.38,-1.02 0.13,-0.13 0.38,-0.26 0.13,-0.26 0,-0.51l-0.06,-0.64 -0.13,-0.64q-0.06,-0.51 0.26,-1.02 0.32,-0.32 0.51,-0.38 0.13,0.19 0,0.51 0,0.38 0.19,0.77l0.45,0.83q0.19,0.45 0.26,0.9 0,0.38 -0.13,0.77l-0.38,0.51 -0.51,0.26z"
      android:fillColor="#0065bd"/>
  <path
      android:pathData="m335.74,261.5l-0.38,0.13 -0.51,0.51q-0.32,0.32 -0.77,0.51l-0.38,0.32c-0.19,0.19 0,0.38 -0.19,0.64q-0.06,0.32 -0.38,0.64t-0.64,0.64l-0.77,0.64 -0.19,0.06l-0.38,0q-0.32,0.26 -0.51,0.51l0.19,0.38 0.51,0.9q0.13,0.38 0.32,0.51 0.45,0.19 0.83,0.06 0.64,0.13 1.28,0.32l0.96,0.51q0.38,0.26 0.83,0.32l1.15,0l0,0.19l1.28,0.64 -0.06,0.26q-0.13,0.32 -0.06,0.51 0.64,1.86 0.96,2.05 0.51,0.26 0.7,0.96l-0.19,0.19q-0.64,0.51 -1.09,1.15c-0.45,0.77 -0.77,0.77 -0.19,1.79l0.96,1.54q0.32,0.64 0.51,1.28t0.19,1.28l0.64,0.19 0.45,-0.38 0.38,-0.77l0,-0.64q-0.19,-0.13 -0.13,-0.45c0,-0.26 0.32,-0.19 0.45,-0.38 0.19,-0.32 -0.26,-0.51 -0.45,-0.7 -0.38,-0.45 -0.9,-0.58 -1.02,-1.22q-0.06,-0.19 0.26,-0.45l1.28,-1.15q0.26,0.13 0.64,0.06l0.83,0.26q0.51,0.06 0.77,0l0.26,0l0.06,0.38c0.06,0.64 -0.06,1.92 0.13,2.24l0.19,0.38 0.13,0.38l0,1.28l-0.13,1.09q0,0.38 -0.32,0.64t-0.64,0.45l0,0.64l0.7,0.32 0.83,0.19 0.45,-0.19 0.06,-0.38 0.32,-0.32q0.45,-0.06 0.58,-0.06 0.13,-0.19 0,-0.51 0,-0.51 -0.19,-1.02l-0.06,-1.79q0,-0.51 0.13,-0.96c0.06,-0.64 0.26,-0.9 0.38,-1.41q0.19,-0.83 0.26,-1.6a15.36,15.36 64.83,0 0,6.46 -0.38q0.77,0.64 1.73,1.02l0,0.64q0,0.26 0.13,0.45l0.19,0.19q0.26,0 0.45,-0.13t0.13,-0.45l0,-0.45l1.15,0l0,0.7q0.13,0.26 0.32,0.26l0.38,0q0.19,-0.26 0.19,-0.64l0,-0.45l0.64,-0.26l0,0.58l-0.19,0.58c-0.13,0.38 -0.32,0.51 -0.51,0.9q-0.26,0.51 -0.64,0.96l-0.38,0.45 -0.38,0.58 -0.58,0.64c-0.45,0.38 -0.77,0.13 -1.28,0.58l-0.19,0.64 0.9,0.38 0.83,0.13 0.26,-0.13q0,-0.32 0.19,-0.51t0.45,-0.26q0.38,0 0.64,-0.13 0.26,-0.38 0.45,-0.96a8.32,8.32 0,0 1,1.92 -2.5l1.09,-0.9q0.26,-0.26 0.32,-0.64l-0.13,-0.38 -0.13,-0.64c0.96,0.45 0.64,0.45 0.77,0.9 0.19,0.38 0,0.64 0.06,1.09 0.06,0.51 0.32,0.7 0.32,1.22q0,0.7 -0.19,1.47 0,0.64 -0.32,1.28a2.56,2.56 0,0 1,-0.7 0.96l-0.38,0.32 -0.06,0.64 0.7,0.26 1.02,0.26 0.26,-0.19c0.13,-0.45 0,-1.09 0.26,-1.09q0.38,0 0.51,-0.19l0,-0.45l0.45,-2.88 0.26,-1.22 0.26,-1.09c0.45,-1.28 -0.13,-1.47 -0.64,-2.3q-0.45,-0.58 -0.45,-0.96l0,-3.65l0.26,-0.13c0.77,-0.45 1.09,-0.58 1.54,-1.6l0.19,-0.96l0,-0.64l-0.26,-0.64 -0.38,-0.51c-0.45,-0.64 -1.09,-0.7 -1.73,-0.96 -0.96,-0.32 -1.6,-0.26 -2.56,-0.32 -1.15,-0.13 -1.73,-0.13 -2.82,0 -1.28,0 -1.98,0.26 -3.26,0.45l-3.14,0.26c-1.47,0 -2.82,-0.32 -3.71,-0.26 -1.54,0.13 -1.6,0.51 -3.97,0.7l-2.43,0.13 -1.41,-0.45c0.58,-0.19 0.7,-0.32 0.96,-0.64s0.13,-0.45 0.38,-0.7l0.45,-0.64 -0.58,-0.26l-0.64,0l-0.77,0.19 -0.51,0.38 -1.41,-0.77a5.76,5.76 0,0 0,-1.92 -0.58zM337.02,269.06"
      android:fillColor="#d52b1e"/>
  <path
      android:strokeWidth="1"
      android:pathData="m364.03,269.5l-0.51,0.19q-0.77,0.32 -1.66,0.32c-1.66,0.13 -2.75,-0.7 -4.48,-0.58 -0.9,0.06 -1.28,0.77 -2.24,1.02l-1.09,0.13 0.32,-0.64s-0.77,0.19 -1.28,0.19l-1.02,-0.13 0.64,-0.64 -0.83,-0.13 -0.64,-0.45 1.09,-0.19c0.96,-0.26 1.28,-0.77 2.5,-0.9 0.7,0 1.92,0 4.86,0.51 1.92,0.32 2.82,0.13 3.52,-0.19q0.64,-0.32 0.7,-1.15 0,-0.77 -0.51,-1.15 -0.13,-0.06 -0.7,-0.26"
      android:fillColor="#00000000"
      android:strokeColor="#fedf00"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M335.87,263.81q-0.45,0 -0.83,0.19 -0.38,0.32 -0.64,0.7 0.45,0.19 0.77,0.19t0.51,-0.32q0.26,-0.32 0.26,-0.77z"
      android:strokeWidth="0.32"
      android:fillColor="#fcd900"
      android:strokeColor="#fedf00"
      android:strokeLineCap="butt"/>
  <path
      android:strokeWidth="1"
      android:pathData="m343.04,272.26l0.64,1.47c0.13,0.51 0,0.77 0.13,1.28l0,1.02m4.35,-4.48l-0.19,0.83 -0.64,2.24l0,0.45m-7.04,-2.56c0.58,0.13 0.38,2.11 1.22,2.56"
      android:fillColor="#00000000"
      android:strokeColor="#fedf00"
      android:strokeLineCap="round"/>
  <path
      android:strokeWidth="1"
      android:pathData="m358.46,276.1l0.26,-0.19a5.12,5.12 97.51,0 0,1.73 -1.15"
      android:fillColor="#00000000"
      android:strokeColor="#fedf00"
      android:strokeLineCap="butt"/>
  <path
      android:strokeWidth="1"
      android:pathData="M353.54,274.94c2.24,-0.58 3.78,-1.66 4.86,-1.86m-2.56,-0.96l0.51,0c0.96,-0.19 1.09,0.38 1.73,0.77 1.22,0.64 1.34,1.47 2.75,2.18l0.26,0.06 0.51,0.26"
      android:fillColor="#00000000"
      android:strokeColor="#fedf00"
      android:strokeLineCap="round"/>
  <path
      android:pathData="M331.33,266.3l0.45,0l0.51,-0.13q0.26,0 0.45,0.13 0.13,0 0.19,0.19t0.06,0.32q0,0.19 -0.38,0.26 -0.19,0 -0.32,-0.19l0,-0.26a0.64,0.64 0,0 1,-0.58 0z"
      android:strokeWidth="0.32"
      android:fillColor="#fcd900"
      android:strokeColor="#fedf00"
      android:strokeLineCap="butt"/>
  <path
      android:pathData="m336.06,272.51l-1.28,-0.58q0.38,-0.13 0.64,-0.32 0.19,-0.38 0.32,-0.83 0.06,-0.64 0.45,-0.9t0.7,-0.06q0.45,0.06 0.58,0.45 0,0.51 -0.19,0.96 -0.13,0.51 -0.13,0.9t0.26,0.64l-1.28,-0.26zM335.42,273.15a0.38,0.38 0,1 1,0.45 0.32,0.38 0.38,0 0,1 -0.45,-0.38zM334.34,262.53l-0.13,0q-0.26,-0.38 -0.38,-0.77l-0.19,-0.77l0,-1.28q0,-0.32 -0.13,-0.58c0,-0.13 -0.26,-0.19 -0.19,-0.26q0.06,0 0.26,0 0.32,0 0.64,0.26t0.38,0.64l0.26,0.96 0.19,0.51 0.32,0.38 -0.45,0.51zM336.64,269.31L338.05,269.95a5.76,5.76 0,0 0,2.24 -2.43c0.58,-1.15 0.64,-1.73 0.9,-2.82l-1.15,-0.32l-0.26,0c-0.32,1.15 -0.45,1.73 -1.02,2.69q-0.77,1.28 -1.66,1.92zM339.84,280.96L340.35,280.13 341.25,279.42l0.26,0a5.76,5.76 0,0 1,-0.32 1.79l-0.26,0.64 -0.32,0.32c-0.32,-0.51 -0.83,-0.83 -0.83,-1.28zM360.96,282.11L361.86,282.5 362.82,283.07l0,0.32l-0.96,0.13l-1.47,0l-0.38,-0.26c0.32,-0.45 0.51,-1.02 0.9,-1.15zM354.69,280.83L355.58,281.15 356.54,281.79q0,0.13 0,0.26a5.76,5.76 0,0 1,-1.73 0.19l-0.64,-0.06 -0.45,-0.19c0.38,-0.45 0.58,-1.09 0.96,-1.15m-11.14,1.34l0.96,0.32 0.96,0.64l0,0.32a5.76,5.76 0,0 1,-1.79 0.13l-0.64,0l-0.38,-0.26c0.32,-0.45 0.51,-1.02 0.9,-1.15m-5.76,-19.07q-0.64,-0.38 -0.38,-1.02 0.13,-0.13 0.38,-0.26 0.13,-0.26 0,-0.51l-0.06,-0.64 -0.13,-0.64q-0.06,-0.51 0.26,-1.02 0.32,-0.32 0.51,-0.38 0.13,0.19 0,0.51 0,0.38 0.19,0.77l0.45,0.83q0.19,0.45 0.26,0.9 0,0.38 -0.13,0.77l-0.38,0.51 -0.51,0.26z"
      android:fillColor="#0065bd"/>
  <path
      android:pathData="M264.1,175.5h52.6L316.7,228h-52.6zM322.3,175.5h52.6L374.9,228h-52.6zM264.3,273.5q0.1,1.6 1.4,4.3c1,1.5 0.6,1.4 2.7,3.8a15,15 0,0 0,4 2.9,33 33,0 0,0 15,2.6q4,-0.2 6.6,-0.7a71,71 0,0 1,11 -0.6q2.2,0 4.7,0.6c3.5,0.7 7,2 7,2v-54.7h-52.6L264.1,271l0.2,2.4zM374.7,273.5a13,13 0,0 1,-1.4 4.3c-1,1.5 -0.6,1.4 -2.7,3.8a15,15 0,0 1,-4 2.9c-1.3,0.7 -2.3,1 -4.4,1.6a33,33 0,0 1,-10.6 1q-4,-0.3 -6.5,-0.7l-7.2,-0.6L334,285.8q-2.2,0 -4.7,0.6c-3.5,0.7 -7,2 -7,2v-54.8L375,233.6v37.5l-0.2,2.4z"
      android:strokeWidth=".5"
      android:fillColor="#00000000"
      android:strokeColor="#703d29"/>
</vector>
