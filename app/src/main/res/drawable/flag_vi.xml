<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <path
      android:pathData="M0,0h640v480H0z"
      android:fillColor="#fff"/>
  <path
      android:pathData="M204.3,315s-0.5,-3.6 6.2,-6.1c6.6,-2.6 7.2,-12.2 5.8,-18.6 0,0 -3.4,6 -7.7,8.1 0,0 -6.8,3.6 -7.2,10.4 0,0 0,2.5 -0.5,4.5 -0.3,1 -3.4,-8.6 2.4,-15.8 6,-7.3 7.6,-13.3 2.9,-25 0,0 -0.5,7 -5.1,11.1 -4.7,4 -5.3,4.4 -5.2,14 0,0 0,3.4 -1.7,4.5 0,0 -3.6,-5.3 -4.6,-8.8s-1.4,-5 2.8,-9c0,0 13.2,-8.7 3.7,-27.4 0,0 -0.3,6.4 -5,10.2 -4.5,3.9 -4.2,6.4 -4.4,13 -0.2,6.7 -0.6,6.2 -1.1,7.2 0,0 -8.5,-15.6 -2,-23.8s10.2,-10 3,-25.4c0,0 0.3,8 -5.5,11.8 -5.7,4 -4.4,13 -4.4,13s0.4,3.5 -0.8,6c0,0 -8.1,-15.9 -0.4,-24.4 6.6,-7.3 7,-12.4 3.5,-22.6 0,0 -0.4,4.7 -4,7.3 -3.7,2.6 -6.6,5.1 -5.9,15 0,0 0.3,7 -0.6,9 0,0 -3.4,-6.4 -4.4,-10.5 -1,-4.2 -1.3,-6.6 1.2,-10.7s12.5,-16 0.6,-33c0,0 -0.3,5.5 -3.4,10.5 -3,5 -1.6,10.9 -1,15.4s-1.5,8.9 -1.5,8.9 -5.2,-9.5 -4,-20.4q1.6,-16.6 -13.5,-26.4s-7.3,17 2.7,25.7c0,0 8.5,8.4 10.7,18.6 0,0 -6.5,-0.6 -12.3,-12s-18,-9.6 -18.8,-9.7c0,0 2,17.6 20.6,22.6 0,0 12,2 14.2,10.8 0,0 2,6 2.8,9.2 0,0 -3.8,-1.5 -7.3,-6.7 -3.5,-5 -3,-5.7 -14.8,-6.9 0,0 -4.7,-0.5 -7.2,-3.9 0,0 5,18.6 17.7,18.5 0,0 11.6,-1.3 18,14l-2.9,-2c-1.7,-1.1 -6,-3.3 -14.5,-2.3s-10.9,-0.4 -13,-1c0,0 8.6,15.7 20.7,11s18,12.4 18.2,12.7c0,0 -1.7,-1.2 -3.8,-3.3 -2.2,-2.1 -6.6,-4.7 -15,-2 0,0 -6.1,2.3 -12.3,0.4 0,0 6,11.5 19.8,9.5a15,15 0,0 1,17.3 10.2s-1.8,-1 -3,-2c-1.3,-1 -6,-3.7 -16,-0.8s-13.6,-0.4 -13.6,-0.4 5.5,10 16.2,11.3c0,0 6,-0.1 8.4,-1s9.2,-2.5 13.8,4.2c0,0 -1.3,0.2 -3.2,-0.7 0,0 -7,-2.5 -12.5,2 0,0 -4.8,4.9 -11.6,4 0,0 8.8,8.5 22.8,1.3 0,0 4.7,-3.4 8.2,-0.8 3.5,2.7 11.5,-2.6 11.5,-2.6z"
      android:strokeWidth="1.5"
      android:fillColor="#369443"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M282.2,103.7s-1.6,-14.5 10,-16a38,38 0,0 1,18 1.2l5.5,10.6 -2,7.2 -7.4,3.4s1.3,-11 -8.8,-11.2c-2.6,0 -4,1 -8.1,0.3s-6.3,3.8 -7.2,4.5z"
      android:strokeWidth="1.5"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M363.8,139.6c-1.8,2.2 -8.7,3 -8.7,3s7.1,5.5 10.2,13.3c3,7.7 -82.4,0 -82.4,0s3.8,-2.6 6.3,-7.6c0,0 -3.8,1.4 -7.6,-2.4 0,0 2.8,1 6.5,-4.6 0,0 5.5,-7 9.4,-9 0,0 -1.8,1.2 -6.1,-1.2 0,0 7.4,-1 10,-13.5 0,0 0.5,-2.8 3.3,-7 2.8,-4 2.2,0.9 7.4,-5.4 0,0 2.4,-4.4 0.3,-7 -2,-2.8 -4.3,-1.8 -8.6,-4.4 -4.4,-2.6 -6.3,-4 -4.8,-8s5.3,-3.4 5.9,-3.4 0.8,-3.5 5.5,-5.9 17.4,-1.3 19.2,-0.3c2,1 9.5,3.3 14,14.5s-1.2,15.4 10.8,32.4c0,0 -4.8,1 -7.9,0 0,0 6.3,11.2 17.3,16.5z"
      android:strokeWidth="1.5"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M311,92.2c-10,-0.5 -6.1,-9.8 -6.1,-9.8"
      android:strokeWidth="1.5"
      android:fillColor="#00000000"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M328,93.5s-1.7,-0.8 -3.2,-2.8c-2,-2.7 -6.7,-4 -9.1,-1.4 0,0 -2.5,2.8 -4.8,3 0,0 2.6,0.7 4.3,2.3s3.5,2.8 6.1,2.4 3,-1.7 4.2,-2.5 2.5,-1 2.5,-1z"
      android:strokeWidth="1.5"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M202.8,336.5s-0.2,-6.4 7.8,-6.8l23.5,32.7s-0.9,2.1 -11.5,1.9c0,0 -1.2,0 -1.8,1.2 -0.9,2 -18,-29 -18,-29z"
      android:strokeWidth="1.5"
      android:fillColor="#369443"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M228.67,298.38s-7.51,12.89 -16.59,13.1c0,0 -15.61,-1.9 -20.02,2.18 -3.09,2.85 -6.23,5.19 -7.53,12.47 -1.26,7.27 2.48,8.84 3.37,9.15 0,0 0.69,5.19 5.83,3.84 0,0 0.3,5.42 9.36,2.82s12.84,-1.78 14.6,-10.81c1.79,-9.05 3.43,-8.9 5.04,-9.98 2.17,-1.46 6.25,-2.83 9.27,-4.58 2.71,-1.57 13.13,-6.87 16.68,-7.27 3.55,-0.42 2.77,-12.38 2.77,-12.38l-9.18,0l-4.44,-6.55zM257.58,233.73s-9.18,7.48 -16.89,0c0,0 -3.1,3.73 -9.33,2.65 -6.2,-1.1 -7.24,-4.37 -7.83,-6.23 0,0 -5.39,3.13 -9.94,0.65 -4.52,-2.47 -4.52,-6.22 -4.52,-6.22s-7.9,1.57 -11.95,-3.79c-4.05,-5.34 -1.58,-10.64 0.6,-11.28 0,0 -8.54,2.24 -10.87,-5.09a8.43,8.43 0,0 1,2.47 -9.45s-17.98,-0.09 -25.35,-5.72c0,0 -6.13,-3.85 -2.47,-7.9 0,0 -16.18,-2.59 -20.23,-9.03 0,0 -1.78,-1.78 -1.19,-4.89 0,0 0.11,-1.76 1.88,-1.87 0,0 -18.46,-2.86 -23.79,-8.94 0,0 -2.56,-2.8 -1.28,-6.55 0,0 0.27,-0.98 0.71,-1.66 0,0 -16.8,-3.54 -25.08,-11.02 0,0 -4.79,-4.26 -2.32,-9.98 0,0 -28.08,-7.68 -21.57,-20.05 0,0 -13.13,-4.26 -8.97,-16.11 0,0 -10.85,-5.51 -6.13,-14.24 4.53,-8.36 18.46,1.46 34.93,6.02 0,0 44.62,14.03 66.59,17.76l62.42,92.93 36.42,14.03z"
      android:strokeWidth="1.51"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M258.01,165.29s-5.86,-4.4 -16.29,0.77c0,0 -3.51,2.42 -6.28,-0.23a5.62,5.62 0,0 1,0.65 -8.66c7.78,-5.77 10.66,-23.06 -1.17,-30.25 0,0 -17.16,-10.18 -80.14,-26.2 0,0 -5.27,-1.48 -8.1,-0.77a7.83,7.83 0,0 0,-5.66 5.69S138.19,113.68 147.68,118.28c0,0 4.28,1.96 8.69,3.22 0,0 -4.85,-1.05 -6.93,3.73 -1.96,4.58 0.9,9.26 9.79,12.27 0,0 2.41,0.93 5.83,1.76 0,0 -7.6,2.18 -3.94,8.33 0,0 3.25,6.44 14.75,7.59 0,0 -8.58,0.93 -2.53,9.09 0,0 2.66,4.47 11.05,6.13 0,0 -6.35,0.21 -3.16,5.92 3.22,5.72 10.76,8.94 18.2,9.47 0,0 4.02,0.21 6.83,-0.42 0,0 -7.08,3.54 -2.92,9.45 0,0 3.06,4.22 10.7,4 0,0 -1.97,6.62 3.63,9.12 4.37,1.96 7.99,-1.17 7.99,-1.17s-1.48,7.09 4.89,9.59c0,0 3.18,1.64 7.92,0 0,0 3.61,8.11 16.5,2.41 12.89,-5.68 3.04,-53.46 3.04,-53.46zM258.77,238.97s-1.88,10.15 -16.86,22.91c0,0 -11.56,10.19 -11.28,21.94 0.3,11.97 -2.06,13.31 -6.61,17.36 0,0 9.38,0.74 13.91,-5.51 0,0 -0.2,10.39 -1.58,11.34 0,0 2.95,0.18 7.21,-4.47 0,0 2.56,-2.71 4.94,-3.75 0,0 -2.77,7.18 -0.6,13.84 0,0 0.69,-2.6 4.35,-3.75 0,0 6.62,-1.35 9.18,-10.91 0,0 1.67,-6.32 12.04,-11.95 0,0 11.74,-4.43 11.44,-10.21 -0.3,-5.8 -26.14,-36.82 -26.14,-36.82z"
      android:strokeWidth="1.51"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M313.21,369.79s-2.69,11.86 -12.04,10.91c0,0 -6.61,-0.63 -6.11,-11.65 0,0 -9.77,5.21 -11.35,-7.68 0,0 -8.88,2.06 -8.78,-9.98 0,0 -8.28,1.45 -7.3,-9.26 0,0 -8.99,2.41 -8.99,-7.68 0,0 -21.02,-3.49 22.79,-43.45l39.08,23.28z"
      android:strokeWidth="1.51"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M317.56,316.51s-6.52,5.78 -11.16,-2.86c0,0 -6.32,-0.3 -7.3,-5.09 0,0 -5.13,-0.51 -6.13,-5.4 0,0 -7,-0.72 -6.8,-6.64 0,0 -12.74,-0.99 0,-15.42s34.34,23.94 34.34,23.94zM324.81,312.92c6.32,0 13.47,39.44 11.62,63.22 -0.68,8.63 -5.3,15.67 -11.62,15.67s-10.93,-7.05 -11.59,-15.67c-1.87,-23.77 5.28,-63.22 11.59,-63.22"
      android:strokeWidth="1.51"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M316.08,312.62a12.95,8.73 90,1 0,17.46 0a12.95,8.73 90,1 0,-17.46 0z"
      android:strokeWidth="1.51"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M295.05,369.05s0.99,-20.88 19.09,-50.83m-30.44,43.15s-2.82,-12.06 22.7,-47.72c0,0 -2.97,-8.51 6.11,-13.97M274.92,351.39s-0.8,-13.05 24.18,-42.83c0,0 -2.21,-7.68 6.83,-13.82m-38.31,47.39s0.93,-13.19 25.35,-38.98c0,0 -1.2,-7.89 7.53,-12.78m-41.85,44.08s1.76,-15.07 27.52,-37.94c0,0 -0.89,-5.93 7.69,-11.95"
      android:strokeWidth="1.51"
      android:fillColor="#00000000"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M59.65,91.24s20.22,11.71 83.24,23.43M68.62,107.35S123.38,125.2 148.88,127.4m-58.7,0s32.13,10.09 69.75,16.46m-42.33,4.56s36.28,8.72 53.63,10.16M141.96,165.55s30.7,6.4 38.47,7.17m-18.92,8.63s22.4,3.4 31.31,3.22m-3.49,10.39s9.06,-0.42 13.41,-1.14m-5.03,15.7s7.71,-2.08 12.24,-4.98m-0.89,20.05s5.93,-2.29 8.79,-8.52m5.68,14.09s4.58,-4.11 4.88,-8.69m12.28,12.27s-1.72,-1.49 -0.54,-7.93m-1.67,-9.45s-0.9,-1.84 -0.6,-4.76m-31.77,-87.51s10.36,4.16 13.81,9.88 1.19,11.28 0,14.45c-0.59,1.61 -7.89,15.69 0.69,25.88M206.24,185.33s0.06,-0.05 3.7,-1.2m-25.56,-13.76s6.17,1.23 12.24,1.29m-20.78,-16.53s7.26,0.74 14.08,0.63m-24.87,-16.53s10.46,2.08 16.08,2.18M156.36,121.5s12.9,3.67 22.49,4.98m21.32,7.59s-9.27,8.63 1.88,16.64c0,0 -4.64,7.27 4.64,14.86m-2.47,3.58s-1.39,13.88 15.6,13.88c0,0 -3.27,12.36 13.13,11.95 0,0 1.87,9.88 13.7,7.89M220.59,311.49s-2.69,0.6 -8.52,0m-16.38,7.27s-8.19,-1.25 -7.8,16.53m12.74,-12.48s-7.41,-0.93 -6.93,16.32m78.39,-57.89s-3.55,0.72 -6.02,3.54 -7.2,2.27 -7.2,2.27 2.56,-2.08 3.46,-7.27c0.87,-5.19 3.75,-6.76 3.75,-6.76m-8.48,-4.91s-3.55,0.72 -6.02,3.52c-2.47,2.82 -7.21,2.29 -7.21,2.29s2.56,-2.08 3.46,-7.27 3.75,-6.76 3.75,-6.76m3.57,28.38s-3.1,0.65 -5.25,3.09c-2.15,2.45 -6.29,2 -6.29,2s2.26,-1.81 3.01,-6.35c0.78,-4.53 3.28,-5.9 3.28,-5.9"
      android:strokeWidth="1.51"
      android:fillColor="#00000000"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M418.53,298.38s7.51,12.89 16.59,13.1c-0,0 15.61,-1.9 20.02,2.18 3.09,2.85 6.23,5.19 7.53,12.47 1.26,7.27 -2.48,8.84 -3.37,9.15 -0,0 -0.69,5.19 -5.83,3.84 -0,0 -0.3,5.42 -9.36,2.82s-12.84,-1.78 -14.6,-10.81c-1.79,-9.05 -3.43,-8.9 -5.04,-9.98 -2.17,-1.46 -6.25,-2.83 -9.27,-4.58 -2.71,-1.57 -13.13,-6.87 -16.68,-7.27 -3.55,-0.42 -2.77,-12.38 -2.77,-12.38l9.18,0l4.44,-6.55zM389.62,233.73s9.18,7.48 16.89,0c-0,0 3.1,3.73 9.33,2.65 6.2,-1.1 7.24,-4.37 7.83,-6.23 -0,0 5.39,3.13 9.94,0.65 4.52,-2.47 4.52,-6.22 4.52,-6.22s7.9,1.57 11.95,-3.79c4.05,-5.34 1.58,-10.64 -0.6,-11.28 -0,0 8.54,2.24 10.87,-5.09a8.43,8.43 0,0 0,-2.47 -9.45s17.98,-0.09 25.35,-5.72c-0,0 6.13,-3.85 2.47,-7.9 -0,0 16.18,-2.59 20.23,-9.03 -0,0 1.78,-1.78 1.19,-4.89 -0,0 -0.11,-1.76 -1.88,-1.87 -0,0 18.46,-2.86 23.79,-8.94 -0,0 2.56,-2.8 1.28,-6.55 -0,0 -0.27,-0.98 -0.71,-1.66 -0,0 16.8,-3.54 25.08,-11.02 -0,0 4.79,-4.26 2.32,-9.98 -0,0 28.08,-7.68 21.57,-20.05 -0,0 13.13,-4.26 8.97,-16.11 -0,0 10.85,-5.51 6.13,-14.24 -4.53,-8.36 -18.46,1.46 -34.93,6.02 -0,0 -44.62,14.03 -66.59,17.76l-62.42,92.93 -36.42,14.03z"
      android:strokeWidth="1.51"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M389.19,165.29s5.86,-4.4 16.29,0.77c-0,0 3.51,2.42 6.28,-0.23a5.62,5.62 135,0 0,-0.65 -8.66c-7.78,-5.77 -10.66,-23.06 1.17,-30.25 -0,0 17.16,-10.18 80.14,-26.2 -0,0 5.27,-1.48 8.1,-0.77a7.83,7.83 0,0 1,5.66 5.69S509.01,113.68 499.52,118.28c-0,0 -4.28,1.96 -8.69,3.22 -0,0 4.85,-1.05 6.93,3.73 1.96,4.58 -0.9,9.26 -9.79,12.27 -0,0 -2.41,0.93 -5.83,1.76 -0,0 7.6,2.18 3.94,8.33 -0,0 -3.25,6.44 -14.75,7.59 -0,0 8.58,0.93 2.53,9.09 -0,0 -2.66,4.47 -11.05,6.13 -0,0 6.35,0.21 3.16,5.92 -3.22,5.72 -10.76,8.94 -18.2,9.47 -0,0 -4.02,0.21 -6.83,-0.42 -0,0 7.08,3.54 2.92,9.45 -0,0 -3.06,4.22 -10.7,4 -0,0 1.97,6.62 -3.63,9.12 -4.37,1.96 -7.99,-1.17 -7.99,-1.17s1.48,7.09 -4.89,9.59c-0,0 -3.18,1.64 -7.92,0 -0,0 -3.61,8.11 -16.5,2.41 -12.89,-5.68 -3.04,-53.46 -3.04,-53.46zM388.43,238.97s1.88,10.15 16.86,22.91c-0,0 11.56,10.19 11.28,21.94 -0.3,11.97 2.06,13.31 6.61,17.36 -0,0 -9.38,0.74 -13.91,-5.51 -0,0 0.2,10.39 1.58,11.34 -0,0 -2.95,0.18 -7.21,-4.47 -0,0 -2.56,-2.71 -4.94,-3.75 -0,0 2.77,7.18 0.6,13.84 -0,0 -0.69,-2.6 -4.35,-3.75 -0,0 -6.62,-1.35 -9.18,-10.91 -0,0 -1.67,-6.32 -12.04,-11.95 -0,0 -11.74,-4.43 -11.44,-10.21 0.3,-5.8 26.14,-36.82 26.14,-36.82z"
      android:strokeWidth="1.51"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M333.99,369.79s2.69,11.86 12.04,10.91c-0,0 6.61,-0.63 6.11,-11.65 -0,0 9.77,5.21 11.35,-7.68 -0,0 8.88,2.06 8.78,-9.98 -0,0 8.28,1.45 7.3,-9.26 -0,0 8.99,2.41 8.99,-7.68 -0,0 21.02,-3.49 -22.79,-43.45l-39.08,23.28z"
      android:strokeWidth="1.51"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M329.64,316.51s6.52,5.78 11.16,-2.86c-0,0 6.32,-0.3 7.3,-5.09 -0,0 5.13,-0.51 6.13,-5.4 -0,0 7,-0.72 6.8,-6.64 -0,0 12.74,-0.99 -0,-15.42s-34.34,23.94 -34.34,23.94zM322.39,312.92c-6.32,0 -13.47,39.44 -11.62,63.22 0.68,8.63 5.3,15.67 11.62,15.67s10.93,-7.05 11.59,-15.67c1.87,-23.77 -5.28,-63.22 -11.59,-63.22"
      android:strokeWidth="1.51"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M331.12,312.62a12.95,8.73 90,1 1,-17.46 0a12.95,8.73 90,1 1,17.46 0z"
      android:strokeWidth="1.51"
      android:fillColor="#f4c53d"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M352.15,369.05s-0.99,-20.88 -19.09,-50.83m30.44,43.15s2.82,-12.06 -22.7,-47.72c-0,0 2.97,-8.51 -6.11,-13.97M372.28,351.39s0.8,-13.05 -24.18,-42.83c-0,0 2.21,-7.68 -6.83,-13.82m38.31,47.39s-0.93,-13.19 -25.35,-38.98c-0,0 1.2,-7.89 -7.53,-12.78m41.85,44.08s-1.76,-15.07 -27.52,-37.94c-0,0 0.89,-5.93 -7.69,-11.95"
      android:strokeWidth="1.51"
      android:fillColor="#00000000"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M587.55,91.24s-20.22,11.71 -83.24,23.43M578.58,107.35S523.82,125.2 498.32,127.4m58.7,0s-32.13,10.09 -69.75,16.46m42.33,4.56s-36.28,8.72 -53.63,10.16M505.24,165.55s-30.7,6.4 -38.47,7.17m18.92,8.63s-22.4,3.4 -31.31,3.22m3.49,10.39s-9.06,-0.42 -13.41,-1.14m5.03,15.7s-7.71,-2.08 -12.24,-4.98m0.89,20.05s-5.93,-2.29 -8.79,-8.52m-5.68,14.09s-4.58,-4.11 -4.88,-8.69m-12.28,12.27s1.72,-1.49 0.54,-7.93m1.67,-9.45s0.9,-1.84 0.6,-4.76m31.77,-87.51s-10.36,4.16 -13.81,9.88 -1.19,11.28 -0,14.45c0.59,1.61 7.89,15.69 -0.69,25.88M440.96,185.33s-0.06,-0.05 -3.7,-1.2m25.56,-13.76s-6.17,1.23 -12.24,1.29m20.78,-16.53s-7.26,0.74 -14.08,0.63m24.87,-16.53s-10.46,2.08 -16.08,2.18M490.84,121.5s-12.9,3.67 -22.49,4.98m-21.32,7.59s9.27,8.63 -1.88,16.64c-0,0 4.64,7.27 -4.64,14.86m2.47,3.58s1.39,13.88 -15.6,13.88c-0,0 3.27,12.36 -13.13,11.95 -0,0 -1.87,9.88 -13.7,7.89M426.61,311.49s2.69,0.6 8.52,0m16.38,7.27s8.19,-1.25 7.8,16.53m-12.74,-12.48s7.41,-0.93 6.93,16.32m-78.39,-57.89s3.55,0.72 6.02,3.54 7.2,2.27 7.2,2.27 -2.56,-2.08 -3.46,-7.27c-0.87,-5.19 -3.75,-6.76 -3.75,-6.76m8.48,-4.91s3.55,0.72 6.02,3.52c2.47,2.82 7.21,2.29 7.21,2.29s-2.56,-2.08 -3.46,-7.27 -3.75,-6.76 -3.75,-6.76m-3.57,28.38s3.1,0.65 5.25,3.09c2.15,2.45 6.29,2 6.29,2s-2.26,-1.81 -3.01,-6.35c-0.78,-4.53 -3.28,-5.9 -3.28,-5.9"
      android:strokeWidth="1.51"
      android:fillColor="#00000000"
      android:strokeColor="#010002"/>
  <path
      android:pathData="m466,256 l-14.3,61.7 -5.9,-4.2 14,-59.2 -9.8,0.2 23.1,-42.3 1.5,48.7zM503,269 L460,319.8 455.4,314.6 498.2,264.4 489.5,259.8 528.7,233.8 508.4,277.6z"
      android:strokeWidth="1.5"
      android:fillColor="#0081c6"
      android:strokeColor="#010002"/>
  <path
      android:pathData="m492.6,242 l-38.5,74 -5.9,-3.5 38.7,-73.8 -9.4,-2.4 32.7,-34.6 -10.5,47.5zM444.6,338.6 L441.7,350.3 449.7,358.3 438.5,405.3 428.5,392 413.9,398.8 425,351.8 435.4,348.8 438,337.1s3.2,-1.2 6.6,1.5z"
      android:strokeWidth="1.5"
      android:fillColor="#0081c6"
      android:strokeColor="#010002"/>
  <path
      android:pathData="m447.3,330.8 l-5.7,10.6 5.7,9.7 -22.4,42.5 -6.3,-15.6 -15.9,2.6 22.4,-42.4h10.7l5.5,-10.6s4,0 6,3.2z"
      android:strokeWidth="1.5"
      android:fillColor="#0081c6"
      android:strokeColor="#010002"/>
  <path
      android:pathData="m448.2,333.9 l-15,19.2 3.4,10.8 -30.9,36 -2.8,-16.6 -16,-1.2 30.8,-36 10.4,2.5 16,-20.4s4.4,2.2 4.1,5.7zM65.5,288.9c3.6,-1.5 6.2,-2.9 6.2,-6.7q0,-1.5 -1.5,-6.1L46,202a59,59 0,0 0,-3 -7.9c-1.1,-2 -3.2,-2.9 -6.1,-4h30.7c-3.3,1.5 -6.4,2.7 -6.3,6.3q0,2 1.1,5.7l18.6,58 18.5,-58a20,20 0,0 0,1.1 -5.7c0,-3.7 -3.2,-5 -6.3,-6.4h29.8c-2.7,1.1 -5,2 -6,4.1a63,63 0,0 0,-3.1 8L90.8,276a38,38 0,0 0,-1.5 6.1s-1.1,4.7 6.2,6.7zM580.8,281.3v-83.7q0,-2.1 -0.8,-3.3c-0.8,-1.2 -2.5,-3 -5.5,-4.2h27.7a12,12 0,0 0,-5.5 4.2q-0.7,1.2 -0.7,3.3v83.7q0,2.2 0.7,3.4c0.5,0.8 2.5,2.8 5.5,4.2h-27.7c3,-1.4 5,-3.4 5.5,-4.2q0.8,-1.2 0.8,-3.4z"
      android:strokeWidth="1.5"
      android:fillColor="#0081c6"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M324.8,309.8S399,276.9 399.1,201H250.5c0.2,76 74.3,108.8 74.3,108.8z"
      android:strokeWidth="1.5"
      android:fillColor="#fff"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M262,201v48.5s5.6,11.3 11.4,18.3L273.4,201zM284.8,201v80s7.2,7 11.4,10.4L296.2,201zM307.7,201v98.9s8.1,5.2 11.4,7v-106h-11.4zM387.7,201v48.5s-5.7,11.3 -11.5,18.3L376.2,201zM364.8,201v80s-7.1,7 -11.4,10.4L353.4,201zM342,201v98.9s-8.2,5.2 -11.5,7v-106L342,200.9z"
      android:strokeWidth="1.5"
      android:fillColor="#a60032"
      android:strokeColor="#010002"/>
  <path
      android:pathData="M399.1,145.8s-36.4,19 -74.3,-1.6c-37.9,20.6 -74.3,1.6 -74.3,1.6V201h148.6z"
      android:strokeWidth="1.5"
      android:fillColor="#162667"
      android:strokeColor="#010002"/>
</vector>
