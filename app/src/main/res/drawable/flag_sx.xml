<vector xmlns:android="http://schemas.android.com/apk/res/android"
    android:width="640dp"
    android:height="480dp"
    android:viewportWidth="640"
    android:viewportHeight="480">
  <group>
    <clip-path
        android:pathData="M0,0l640.03,0l0,480L0,480z"/>
    <path
        android:pathData="M0,0l720,0l0,240L319.97,240z"
        android:fillColor="#ed2939"
        android:fillType="evenOdd"/>
    <path
        android:pathData="M0,480l720,0L720,240L319.97,240z"
        android:fillColor="#002395"
        android:fillType="evenOdd"/>
    <path
        android:pathData="m0,0 l319.97,240L0,480z"
        android:fillColor="#fff"
        android:fillType="evenOdd"/>
    <path
        android:pathData="m161.53,181.13 l0.09,-3.75s-1.97,-3.19 0.38,-6.56c0,0 -4.97,-2.63 -3.75,-6.75 0,0 -4.5,-1.13 -4.22,-6 0,0 -4.69,-0.28 -5.25,-4.41 0,0 -4.78,0.75 -6.94,-3.19 0,0 -4.69,0.94 -6,-2.34 0,0 -4.59,1.5 -7.22,-2.34 0,0 -4.88,1.97 -6.84,-1.69 -1.97,3.66 -6.84,1.69 -6.84,1.69 -2.63,3.84 -7.22,2.25 -7.22,2.25 -1.31,3.28 -5.91,2.34 -5.91,2.34 -2.16,4.03 -6.94,3.19 -6.94,3.19 -0.47,4.13 -5.25,4.5 -5.25,4.5a5.63,5.63 0,0 1,-4.13 6c1.22,4.03 -3.75,6.66 -3.75,6.66 2.44,3.47 0.47,6.56 0.47,6.56l-0.09,3.47z"
        android:fillColor="#ff0"/>
    <path
        android:pathData="M161.65,181.1l0,-3.76s-1.93,-3.22 0.43,-6.45c0,0 -4.94,-2.69 -3.76,-6.77 0,0 -4.51,-1.07 -4.19,-6.02 0,0 -4.84,-0.32 -5.37,-4.41 0,0 -4.73,0.75 -6.77,-3.22 0,0 -4.73,0.86 -6.02,-2.36 0,0 -4.62,1.61 -7.31,-2.26 0,0 -4.84,1.93 -6.77,-1.72 -2.04,3.65 -6.88,1.72 -6.88,1.72 -2.58,3.76 -7.2,2.15 -7.2,2.15 -1.29,3.33 -5.91,2.47 -5.91,2.47 -2.15,3.98 -6.99,3.22 -6.99,3.22 -0.43,3.98 -5.27,4.3 -5.27,4.3a5.37,5.37 90,0 1,-3.98 6.02c1.07,4.08 -3.76,6.66 -3.76,6.66 2.36,3.44 0.32,6.66 0.32,6.66l0,3.44z"
        android:strokeWidth="0.32"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M159.66,176.44c-12.47,-1.22 -26.81,-4.22 -37.88,2.06 -7.5,-4.41 -17.81,-4.13 -26.63,-3.28 -4.78,0.94 -10.41,0.94 -15.47,1.69l-0.75,0.09c-9.09,0.66 -17.34,-2.06 -25.31,-5.63 2.63,14.25 5.34,28.78 6.75,43.69C63.38,237.19 49.97,255.66 55.31,277.5c3.28,10.5 14.81,16.59 25.31,17.81a201.56,201.56 57.74,0 1,30.19 4.22,30 30,70.35 0,1 5.16,1.78 18.75,18.75 56.02,0 1,6.19 4.41c5.63,-5.91 14.06,-7.59 22.22,-8.44 14.81,-1.78 33.09,-0.84 42.19,-14.25l0,-0.75c4.97,-7.5 4.31,-18.75 2.16,-27.19 -0.19,-2.91 -0.94,-5.53 -1.41,-8.34 -9,-23.91 -1.22,-50.81 2.63,-75a57.19,57.19 0,0 1,-15.38 5.16c-4.22,0.56 -8.91,0.38 -13.13,0.09z"
        android:fillColor="#ff0000"/>
    <path
        android:pathData="M178.31,245.16c0,1.5 0.84,2.81 1.13,4.22 1.78,10.5 3.38,24.38 -7.31,30.84 -12,7.5 -27.56,4.31 -41.34,8.34 -2.81,0.75 -6.56,3.28 -8.44,5.06 -1.31,-0.84 -2.53,-2.06 -4.13,-2.81 -11.34,-6 -26.16,-4.03 -38.91,-7.31 -6.47,-1.97 -13.13,-7.03 -14.81,-13.59 -4.13,-17.25 5.81,-31.97 4.69,-49.13 -0.75,-13.13 -3,-25.59 -6,-37.88 10.03,4.69 21.56,5.44 32.81,3.19A48.75,48.75 98.38,0 1,121.88 188.44c4.31,-1.88 9.38,-2.72 14.06,-3.19 8.72,0.94 18.19,2.44 27.28,2.44q9.09,-0.75 16.88,-4.41c-3.09,20.25 -9.19,41.44 -1.78,61.88"
        android:fillColor="#80cfe1"/>
    <path
        android:pathData="M160.31,249.19l0,-19.13l2.25,0l-29.06,-18.75 -0.09,-9.84l2.81,0l-14.63,-10.31 -14.44,10.31l2.81,0l0,9.84l-28.97,18.75l2.34,0l0,19.03L160.31,249.09"
        android:fillColor="#fff"/>
    <path
        android:pathData="M160.36,249.22l0,-19.13l2.26,0l-29.02,-18.8 -0.11,-9.67l2.79,0l-14.62,-10.53 -14.51,10.42l2.79,0l0.11,9.78 -29.02,18.8l2.36,0l0,19.02z"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M169.31,274.22l0,5.91L75.47,280.13l0,-6z"
        android:fillColor="#fff"/>
    <path
        android:pathData="M169.38,274.26l0,5.91L75.45,280.17l0,-6.02l93.93,0z"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M81.66,273.84l0,-20.16l3.75,0l-0.19,20.34 0.19,-20.25 -5.44,-0.09l0,-4.41l83.81,0.19l0,4.22l-5.06,0l0,20.44l-0.09,-20.44l3.75,0l0.19,20.25"
        android:fillColor="#fff"/>
    <path
        android:pathData="M81.68,273.83l0,-20.2l3.76,0l-0.21,20.41 0.21,-20.2 -5.37,-0.21l0,-4.3l83.83,0.11l0,4.3l-5.16,0l0.11,20.41 -0.21,-20.41l3.87,0l0.11,20.2"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M99.74,268.89l0,-14.08L88.35,254.81l0,13.97l11.28,0.11"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M91.57,257.71l0,8.17l5.16,0l0,-8.17l-5.27,0m63.41,11.17 l-0.11,-13.97l-11.18,0l0,13.97l11.28,0"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M146.6,257.82l0,8.17l5.16,0l0,-8.17l-5.16,0m8.17,-11.82l0,-13.97l-11.28,0l0,13.97z"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M146.49,234.93l0,8.17l5.16,0l0,-8.17l-5.16,0m-46.86,11.07l0,-14.08L88.35,231.93l0,13.97l11.28,0z"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M91.46,234.83l0,8.17l5.16,0l0,-8.17l-5.16,0m28.59,11.17l0,-14.08L108.77,231.93l0,13.97l11.28,0"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M111.78,234.83l0,8.17l5.16,0l0,-8.17l-5.16,0m23.97,11.17l0,-13.97l-11.28,-0.11l0,13.97l11.28,0.11"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M127.47,234.83l0,8.17l5.27,0l0,-8.17l-5.27,0"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m138.47,241.69 l0.19,32.44L105.94,274.13l-0.09,-32.44z"
        android:fillColor="#fff"/>
    <path
        android:pathData="m138.54,241.7 l0.11,32.45l-32.56,0l-0.11,-32.45zM105.76,250.94l32.89,0M109.84,274.15l0,-22.99m24.83,22.99l0,-22.89m15.8,-23.42 l-18.27,-13.32l-20.74,0l-18.27,13.32l57.28,0M113.07,203.34l0,8.17l17.41,0l0,-8.17L113.07,203.34"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M117.8,205.39l0,4.08l7.95,0l0,-4.08l-7.95,0M121.66,193.89l9.67,6.88l-19.24,0l9.67,-6.88"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M160.69,189.38c2.72,0 3.38,11.25 3.38,16.88 2.44,0.75 7.59,4.03 9.94,6.94l-26.44,0c2.25,-2.91 7.41,-6.19 9.75,-6.84 0,-5.63 0.56,-17.06 3.38,-16.88"
        android:fillColor="#fff"/>
    <path
        android:pathData="M160.68,189.38c2.79,0 3.44,11.28 3.44,16.98 2.36,0.64 7.63,3.98 9.89,6.88l-26.44,0c2.26,-2.9 7.52,-6.23 9.78,-6.88 0,-5.69 0.64,-16.98 3.33,-16.98m-53.2,53.19l2.36,0l-0.64,1.29s1.07,1.5 0,3.22l0.64,1.07l-2.36,0l0.64,-1.07s-1.07,-1.93 0,-3.22l-0.64,-1.29m4.84,0l2.36,0l-0.64,1.29s1.07,1.5 0,3.22l0.64,1.07l-2.36,0l0.64,-1.07s-1.07,-1.93 0,-3.22l-0.64,-1.29m5.37,0l2.26,0l-0.64,1.29s1.07,1.5 0,3.22l0.64,1.07l-2.26,0l0.54,-1.07s-1.07,-1.93 0,-3.22l-0.64,-1.29zM123.6,242.56l2.36,0l-0.75,1.4s1.18,1.4 0,3.12l0.64,1.07l-2.26,0l0.64,-1.07s-1.07,-1.93 0,-3.22zM128.87,242.56l2.36,0l-0.75,1.4s1.18,1.4 0,3.12l0.64,1.07l-2.15,0l0.54,-1.07s-1.07,-1.93 0,-3.22l-0.64,-1.18m5.27,0l2.36,0l-0.75,1.29s1.18,1.4 0,3.12l0.64,1.07l-2.15,0l0.54,-1.07s-1.07,-1.93 0,-3.22l-0.64,-1.18"
        android:strokeWidth="1.29"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M122.34,315.75c12.19,0 27.19,-2.53 34.22,-6.09l15.47,-1.41 0.38,18.84c-13.13,7.22 -43.31,8.81 -49.97,8.72s-37.22,-1.5 -50.44,-8.72l0.19,-18.94 15.47,1.41a91.88,91.88 0,0 0,34.69 6.19"
        android:fillColor="#ff0"/>
    <path
        android:pathData="M122.42,315.73c12.25,0 27.19,-2.47 34.28,-6.12l15.37,-1.29 0.32,18.8c-13,7.2 -43.31,8.81 -49.87,8.7s-37.29,-1.5 -50.51,-8.81l0.21,-18.8 15.48,1.4c7.09,3.65 22.57,6.12 34.71,6.12"
        android:strokeWidth="0.32"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m32.25,235.31 l13.59,4.31 -4.13,22.03 -12.84,2.06s-2.16,-11.63 3.38,-28.41"
        android:fillColor="#ff0"/>
    <path
        android:pathData="m32.35,235.36 l13.54,4.3 -4.08,22.03 -12.9,2.04s-2.15,-11.6 3.44,-28.37z"
        android:strokeWidth="0.32"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m41.72,261.47 l-12.84,2.25 8.16,-11.63z"
        android:fillColor="#7e7e7e"/>
    <path
        android:pathData="m41.81,261.47 l-12.9,2.15 8.17,-11.5 4.73,9.35"
        android:strokeWidth="0.32"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m24.09,251.25 l13.13,0.94c0.28,11.06 1.69,53.44 52.31,55.78L87.94,323.44c-61.59,0.56 -66.28,-52.03 -63.75,-72.19"
        android:fillColor="#ff0"/>
    <path
        android:pathData="m24.19,251.27 l13.11,0.86c0.32,11.17 1.61,53.51 52.34,55.87l-1.61,15.47c-61.69,0.64 -66.31,-52 -63.84,-72.2zM72.33,326.59 L87.81,323.26"
        android:strokeWidth="0.32"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m211.31,235.5 l-13.5,4.31 4.31,21.94 12.94,2.06s2.06,-11.53 -3.75,-28.31"
        android:fillColor="#ff0"/>
    <path
        android:pathData="m211.41,235.47 l-13.54,4.3 4.3,22.03 12.9,2.15s2.15,-11.6 -3.65,-28.47z"
        android:strokeWidth="0.32"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m202.13,261.56 l12.84,2.34 -8.25,-11.63 -4.69,9.28"
        android:fillColor="#7e7e7e"/>
    <path
        android:pathData="m202.16,261.58 l12.9,2.26 -8.28,-11.5 -4.62,9.24"
        android:strokeWidth="0.32"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="m219.66,251.34 l-13.13,0.94c-0.19,11.06 -1.22,53.44 -51.84,55.78l1.88,15.38c61.59,0.75 65.81,-51.94 63.19,-72.09"
        android:fillColor="#ff0"/>
    <path
        android:pathData="m219.68,251.37 l-13.11,0.86c-0.21,11.17 -1.07,53.62 -51.8,55.87l1.72,15.36c61.69,0.75 65.88,-51.9 63.2,-71.99zM172.18,326.59 L156.7,323.36"
        android:strokeWidth="0.32"
        android:fillColor="#00000000"
        android:strokeColor="#000"/>
    <path
        android:pathData="M114.19,325.03q-0.66,0.28 -1.41,0.19l-2.44,-0.09 0.19,-3.09 2.44,0.19q0.75,0 1.13,0.28 0.66,0.28 0.66,1.31 0,0.84 -0.66,1.22m-4.03,4.69 l0.19,-3.66 2.81,0.19q1.41,0.09 2.16,-0.66 0.75,-0.56 0.75,-1.69a2.81,2.81 0,0 0,-0.56 -1.88,2.81 2.81,0 0,0 -1.88,-0.84l-4.13,-0.28 -0.56,8.81zM118.87,322.5l3,0l1.03,0.19q0.66,0.38 0.66,1.31t-0.47,1.22a2.81,2.81 0,0 1,-1.31 0.28l-2.91,0zM118.87,330.19l0,-3.75l2.81,0.09q0.75,0 1.13,0.19 0.66,0.28 0.66,1.13l0,2.16l0.19,0.28l1.5,0l0,-0.19l-0.38,-0.47 -0.09,-0.75l0,-1.22q0,-0.84 -0.38,-1.13l-0.75,-0.56q0.66,-0.28 0.94,-0.75t0.38,-1.31q0,-1.5 -1.31,-2.06l-1.69,-0.38l-4.22,0l0,8.81zM128.25,328.31a4.69,4.69 0,0 1,-0.94 -2.44q0,-1.88 0.75,-2.81a2.81,2.81 0,0 1,2.25 -0.94,2.81 2.81,0 0,1 2.25,0.75q0.94,0.94 1.03,2.44a4.69,4.69 0,0 1,-0.56 2.63q-0.66,1.22 -2.34,1.22c-1.69,0 -1.88,-0.19 -2.53,-0.84m-1.41,-5.53a5.63,5.63 0,0 0,-0.75 3.09q0.09,1.97 1.31,3.19a4.69,4.69 0,0 0,3.38 1.22,3.75 3.75,0 0,0 3.28,-1.69 5.63,5.63 0,0 0,0.94 -3.38,4.69 4.69,0 0,0 -1.13,-2.91q-1.22,-1.41 -3.66,-1.31 -2.25,0 -3.38,1.78m41.25,-12.66q1.41,-0.19 2.34,0.09 1.41,0.47 2.06,2.16l-1.22,0.19a1.88,1.88 0,0 0,-1.13 -1.22,3.75 3.75,0 0,0 -1.88,-0.19 2.81,2.81 0,0 0,-1.88 1.22q-0.75,0.94 -0.47,2.81a4.69,4.69 0,0 0,1.13 2.44q0.94,0.84 2.44,0.66a2.81,2.81 0,0 0,1.88 -1.03q0.75,-0.84 0.47,-2.34l-2.81,0.47 -0.09,-0.94 3.94,-0.66 0.75,4.69l-0.84,0l-0.47,-0.94 -0.94,0.94q-0.75,0.66 -1.88,0.84a3.75,3.75 0,0 1,-3 -0.66,4.69 4.69,0 0,1 -1.78,-3.19 4.69,4.69 0,0 1,0.66 -3.75,3.75 3.75,0 0,1 2.81,-1.59m8.81,-0.66 l2.81,-1.03q0.56,-0.19 1.03,-0.19 0.84,0 1.13,0.94t0,1.41q-0.38,0.47 -1.22,0.75l-2.63,0.94zM179.72,316.69 L178.41,313.22 181.03,312.28 182.06,312q0.75,0.09 1.13,0.94l0.56,1.31 0.28,0.66 0.19,0.28 1.41,-0.56l0,-0.19q-0.47,0 -0.56,-0.38 -0.28,-0.19 -0.38,-0.66l-0.47,-1.13q-0.28,-0.66 -0.66,-0.94l-0.94,-0.19 0.56,-1.13q0,-0.56 -0.19,-1.31 -0.56,-1.5 -1.88,-1.5a4.69,4.69 0,0 0,-1.78 0.28l-3.94,1.41 3.19,8.25 1.13,-0.38m10.31,-4.41 l-4.97,-7.31 5.44,-3.56 0.66,0.94 -4.5,2.81 1.5,2.25 4.22,-2.63 0.56,0.84 -4.22,2.63 1.69,2.53 4.69,-3 0.56,0.94zM194.44,299.44 L195.84,297.84q1.13,-1.13 2.16,-0.94 1.22,0 2.44,1.22a4.69,4.69 0,0 1,1.31 1.88q0.19,0.84 0,1.5l-0.66,0.94 -1.41,1.5zM202.03,302.81q1.5,-1.59 0.66,-3.75a6.56,6.56 0,0 0,-1.59 -1.97,4.69 4.69,0 0,0 -2.91,-1.31q-1.69,-0.19 -2.91,1.22l-2.44,2.63 6.75,5.91 2.44,-2.63m5.25,-6.84 l-7.78,-4.69 0.66,-0.94 7.78,4.59zM210.38,290.81 L201.94,287.44 204.38,281.53 205.41,281.91 203.44,286.78 205.97,287.72 207.84,283.31 208.78,283.69 206.91,288.19 209.72,289.31 211.88,284.44 212.81,284.81zM214.13,280.5 L205.31,278.62 205.69,277.22 213.84,274.41 206.63,272.81 206.91,271.78 215.72,273.66 215.44,274.97 207.28,277.78 214.5,279.37zM213.38,269.25 L213.47,268.12q0.75,0 1.31,-0.19 0.94,-0.56 1.13,-1.97l-0.09,-1.31q-0.28,-1.03 -1.22,-1.13t-1.13,0.28q-0.38,0.38 -0.66,1.41l-0.47,1.13q-0.28,1.13 -0.75,1.59 -0.56,0.84 -1.69,0.66a2.81,2.81 0,0 1,-1.88 -0.94q-0.84,-0.94 -0.66,-2.44 0.09,-1.41 0.94,-2.25 0.75,-0.94 2.25,-0.75l-0.09,1.13q-0.75,0 -1.13,0.28 -0.84,0.47 -0.94,1.78 -0.09,0.94 0.38,1.5t0.94,0.56q0.75,0.09 1.03,-0.38l0.66,-1.59 0.38,-1.22q0.28,-0.84 0.75,-1.31 0.75,-0.66 1.88,-0.66 1.59,0.19 2.06,1.31t0.47,2.53a3.75,3.75 0,0 1,-1.13 2.44q-0.94,0.84 -2.44,0.66M30,268.78l0.28,1.13q-0.84,0.09 -1.22,0.56 -0.75,0.75 -0.47,2.25l0.38,1.13q0.56,0.94 1.59,0.84 0.66,-0.09 0.94,-0.66l0.28,-1.5l0,-1.22q0,-1.22 0.28,-1.69 0.47,-0.84 1.59,-1.13 1.22,-0.19 2.06,0.56 0.94,0.56 1.22,2.06a3.75,3.75 0,0 1,-0.28 2.44q-0.47,1.03 -1.97,1.31l-0.19,-1.03 0.94,-0.56q0.66,-0.66 0.47,-1.97 -0.19,-1.13 -0.75,-1.41a1.88,1.88 0,0 0,-1.13 -0.28,0.94 0.94,0 0,0 -0.94,0.66l-0.09,1.69 -0.09,1.22 -0.38,1.5a1.88,1.88 0,0 1,-1.59 1.13q-1.5,0.28 -2.34,-0.75a4.69,4.69 0,0 1,-1.13 -2.25,3.75 3.75,0 0,1 0.47,-2.72 2.81,2.81 0,0 1,2.06 -1.22m-0.56,11.53 l8.72,-2.63 1.97,6.09 -1.03,0.38 -1.69,-4.97 -2.63,0.75 1.59,4.69 -1.03,0.28 -1.5,-4.69 -3,0.94 1.69,5.06 -0.94,0.28zM33.28,291 L41.25,286.78 42.19,288.28 36.66,294 44.63,292.69 45.47,294.09 37.5,298.31 36.94,297.38 41.63,294.84 42.47,294.47 43.69,293.81 35.72,295.22 35.16,294.19 40.59,288.38 40.31,288.56 39.38,289.03 38.53,289.5 33.84,291.94zM48.09,302.72q-0.56,-0.19 -1.13,-0.75l-1.69,-1.69 2.25,-2.06 1.69,1.69 0.66,0.94q0.28,0.75 -0.47,1.41t-1.31,0.47m-6.38,0.75 l2.81,-2.53 1.88,1.97q1.13,0.94 2.06,0.94a2.81,2.81 0,0 0,1.88 -0.75,2.81 2.81,0 0,0 0.75,-1.88q0,-0.94 -0.84,-1.78l-2.81,-2.81 -6.56,6zM49.59,310.5 L54.38,303 60,306.38 59.44,307.31 54.75,304.5 53.34,306.84 57.56,309.38 57.09,310.31 52.78,307.69 51.19,310.22 55.88,313.03 55.31,313.97zM63.66,309 L66.56,309.94 67.5,310.41q0.56,0.56 0.19,1.41 -0.19,0.94 -0.84,1.03l-1.41,-0.09 -2.72,-0.94zM61.22,316.41 L62.44,312.84 65.06,313.69q0.75,0.19 1.03,0.47 0.47,0.47 0.28,1.31l-0.47,1.41 -0.09,0.66l0,0.28l1.41,0.47l0,-0.19l-0.19,-0.66 0.19,-0.66 0.38,-1.13q0.19,-0.84 0,-1.13 0,-0.47 -0.56,-0.84 0.75,0 1.22,-0.47t0.66,-1.13q0.56,-1.41 -0.56,-2.34l-1.5,-0.84 -4.03,-1.22 -2.81,8.34z"
        android:fillColor="#009fc5"/>
    <path
        android:pathData="M73.59,164.63c0.94,-2.53 10.59,-9.38 42,-6.38 0,0 5.34,3.28 8.44,2.81 1.88,-0.19 -0.94,0.19 -3.38,-1.78 -2.34,-1.88 -2.81,-5.91 2.53,-5.72 5.44,0.09 25.59,1.22 25.97,3.09s-10.69,2.53 -14.72,2.44c-4.13,-0.09 -3.75,1.97 0.38,1.88 12.84,-0.56 26.81,-5.06 41.53,5.44 1.78,1.22 -4.41,1.41 -9.19,-0.56 0,0 -13.13,0.66 -18.75,-0.28 0,0 -4.22,3.56 -9.94,3.19 0.75,1.88 -2.06,8.63 -19.69,3.47 -3,0.94 -13.59,2.72 -12.47,-0.47 -2.81,0 -7.69,0.94 -8.44,-0.66s8.63,-3.94 10.31,-5.63c0,0 -19.88,0.19 -23.63,-1.22 0,0 -11.72,2.63 -10.97,0.38"
        android:fillColor="#bc715f"/>
    <path
        android:pathData="M79.97,192.38c0.38,-0.56 0.56,-1.69 1.5,-1.59 1.88,0.47 3.28,4.13 5.63,1.78 0.94,-0.19 1.22,0.94 1.88,1.13 0.47,1.97 2.16,-0.47 3.28,-0.28q4.03,-1.69 8.81,-0.94c0,2.06 -1.5,4.22 -3.38,5.34 0,1.88 1.88,3.19 1.78,5.16 -0.28,0.75 0,1.88 -1.13,2.16 -1.5,-0.28 -2.81,-0.38 -4.03,-1.22q-0.19,-0.28 -0.56,-0.28c-0.19,0.66 0.94,1.31 1.31,1.97 -0.75,1.22 -1.88,0.09 -2.91,0.09 -0.28,0.75 0,1.59 -0.75,2.06 -1.22,0 -1.5,-1.41 -2.25,-2.06 -0.84,0.84 0.56,1.31 0.75,1.97 -0.56,2.63 -0.47,5.16 -3,6.84l-1.78,0.84c-0.47,-1.31 -0.19,-3.28 -0.47,-4.69q-1.31,2.53 -3.28,4.59c-1.88,-0.56 -2.81,-2.63 -3.19,-4.41a5.63,5.63 0,0 1,1.22 -5.81c-0.47,-0.94 -2.34,-0.75 -2.63,-2.25 -2.81,-1.88 -4.22,3.09 -6,0.56a8.44,8.44 0,0 1,1.88 -7.13c-0.47,-0.94 -1.78,-0.75 -2.53,-1.31 -1.69,-1.5 -4.22,-3 -4.03,-5.63a13.13,13.13 0,0 1,13.78 3.09"
        android:fillColor="#008737"/>
    <path
        android:pathData="M82.41,192.09c-0.09,2.16 1.41,0.56 2.63,1.88 -0.94,-0.38 -1.59,1.59 -2.53,0.47l-0.56,0.56c0.38,0.75 0.94,0.84 0.38,1.69 -0.94,0.38 -1.22,-0.94 -1.69,-1.41q0.19,-0.38 -0.19,-0.75 -0.75,0.66 -1.88,0.56c-0.19,-0.38 -0.66,-0.94 -0.19,-1.31 0.75,-0.94 1.88,0.38 2.44,-0.28 0.09,-0.66 -0.47,-1.41 0.28,-1.88 0.47,0.09 1.13,-0.19 1.31,0.47m5.81,1.78c0.09,0.38 -0.19,0.94 0.38,1.31q1.22,-0.47 1.88,0.56 0.19,0.75 -0.38,1.13l-1.13,-0.28q0,-0.19 -0.38,-0.56 -0.75,0 -1.13,0.56c0.28,0.38 0.94,0.66 0.75,1.13 -0.66,0.66 -0.94,-0.56 -1.69,-0.19 -0.56,-0.19 0.09,-1.22 -0.66,-1.59 -0.94,0 -0.28,1.5 -1.5,0.94q-0.75,-0.75 -0.38,-1.59a1.88,1.88 0,0 1,1.78 0.19c0.75,-0.84 0.75,-3.84 2.53,-1.59m-7.31,3.09c0.47,0.75 -0.19,1.69 0.47,2.44 0.94,-1.88 1.88,0.38 3,0.19 0.38,0.47 0.09,1.03 0.19,1.5 -1.41,0.38 -3.09,0.84 -4.31,-0.28l-0.38,0.38c0.75,0.66 1.41,1.5 0.84,2.63 -1.03,0.19 -2.72,0.28 -3.19,-0.84 -0.28,-0.94 0.66,-1.5 -0.38,-2.25 -0.66,0.94 -0.66,2.06 -2.06,1.22a1.88,1.88 0,0 1,-0.75 -2.34c0.94,-1.5 2.63,0 3.56,-0.66 0.19,-1.31 -0.28,-2.44 0.94,-3.09 0.94,-0.19 1.41,0.56 2.06,1.13"
        android:fillColor="#ff0"/>
    <path
        android:pathData="M84.94,197.81c-0.47,2.16 -2.06,-0.09 -2.81,-0.19 0.94,-1.88 1.59,0.56 2.81,0.19m5.06,2.34c-1.13,0.28 -0.94,-1.13 -1.5,-1.69l0.47,-0.38q0.56,0.94 1.03,2.06"
        android:fillColor="#fff"/>
    <path
        android:pathData="M88.5,199.69c0.28,0.75 -0.75,1.41 0.19,1.88l0.75,-0.66q1.31,0.09 2.25,1.13c0.56,1.59 -1.03,1.59 -1.88,2.06l-0.94,0c0.19,-0.56 -0.47,-0.75 -0.56,-0.94a0.94,0.94 0,0 0,-1.03 0.28c0.19,0.94 1.41,1.88 0.19,2.72 -1.13,0 -2.81,0.38 -2.72,-1.13 -0.38,-0.94 1.13,-2.16 -0.47,-2.25 -0.56,1.22 -1.13,2.25 -2.53,1.22l0.19,-1.69c1.31,0 2.91,0.28 3.47,-1.22 0.38,-1.03 -0.66,-2.06 0.28,-2.81 1.41,-0.38 2.16,0.56 2.81,1.41"
        android:fillColor="#ff0"/>
    <path
        android:fillColor="#FF000000"
        android:pathData="M87.56,202.13c0.28,0.84 -0.56,0.94 -1.03,1.31q-0.75,0 -1.31,-0.56c-0.28,-0.94 0.47,-1.13 0.94,-1.5 0.66,-0.28 0.94,0.47 1.41,0.75"/>
    <path
        android:pathData="M86.72,202.69q-0.19,0.19 -0.56,0.19l-0.19,-0.94q0.94,-0.09 0.75,0.75"
        android:fillColor="#fff"/>
  </group>
</vector>
