<?xml version="1.0" encoding="utf-8"?>
<menu xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:android="http://schemas.android.com/apk/res/android">

    <item android:id="@+id/new_game"
        android:icon="@drawable/ic_icon_more"
        android:orderInCategory="101"
        app:showAsAction="always"
        android:title="@string/nav_item_more">
        <menu>
            <item
                android:id="@+id/menu_scanner"
                android:icon="@drawable/ic_qr_code_scanner"
                android:title="@string/scan_reservation_title"
                app:showAsAction="ifRoom"/>
            <item
                android:id="@+id/daily_notes"
                android:icon="@drawable/ic_notes_menu"
                android:title="@string/daily_notes"
                app:showAsAction="ifRoom" />
            <item android:id="@+id/block_hours"
                android:icon="@drawable/ic_block_hours_menu"
                android:title="@string/block_hours"
                app:showAsAction="ifRoom" />
            <item android:id="@+id/block_tables"
                android:icon="@drawable/ic_icon_table_2"
                android:title="@string/block_tables"
                app:showAsAction="ifRoom" />
        </menu>
    </item>

    <item
        android:icon="@drawable/ic_icon_notification_dot"
        android:title="@string/notifications"
        app:showAsAction="always"
        android:id="@+id/menu_notifications">
    </item>

</menu>