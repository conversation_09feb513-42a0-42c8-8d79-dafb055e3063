<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/tables_nav_graph"
    app:startDestination="@id/tablesFragment">

    <fragment
        android:id="@+id/tablesFragment"
        android:name="com.eatapp.clementine.ui.common.tables.TablesFragment"
        android:label="TablesFragment">
        <argument
            android:name="tables"
            app:argType="com.eatapp.clementine.data.network.response.room.Table[]" />
    </fragment>

</navigation>