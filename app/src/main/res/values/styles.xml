<?xml version="1.0" encoding="utf-8"?>
<resources>

    <style name="SplashTheme" parent="Theme.AppCompat.Light.NoActionBar">

        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="android:navigationBarColor">@color/colorDark100</item>
        <item name="bottomSheetDialogTheme">@style/AppBottomSheetDialogTheme</item>
    </style>

    <!-- Base application theme. -->
    <style name="AppTheme" parent="Theme.AppCompat.Light.NoActionBar">

        <!-- Customize your theme here. -->
        <item name="colorPrimary">@color/grey800</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDarko</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="colorControlHighlight">@color/colorControlHighlight</item>
        <item name="bottomSheetDialogTheme">@style/AppBottomSheetDialogTheme</item>
        <item name="android:navigationBarColor">@color/colorDark100</item>
        <item name="android:textDirection">locale</item>
        <item name="android:fontFamily">@font/inter_regular</item>

    </style>

    <style name="EatTab" parent="TextAppearance.AppCompat">
        <item name="android:textSize">14sp</item>
        <item name="android:textAllCaps">false</item>
        <item name="android:fontFamily">@font/inter_medium</item>
    </style>

    <style name="BottomNavigationView" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">10sp</item>
        <item name="android:fontFamily">@font/inter_regular</item>
    </style>

    <style name="BottomNavigationView.Active" parent="@style/TextAppearance.AppCompat.Caption">
        <item name="android:textSize">10sp</item>
        <item name="android:fontFamily">@font/inter_semibold</item>
    </style>

    <style name="AppBottomSheetDialogTheme" parent="Theme.MaterialComponents.DayNight.BottomSheetDialog">
        <item name="bottomSheetStyle">@style/AppModalStyle</item>
    </style>

    <style name="AppModalStyle" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="behavior_peekHeight">650dp</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="shapeAppearanceOverlay">@style/BottomSheetShapeAppearance</item>
    </style>

    <style name="BottomSheetShapeAppearance" parent="Widget.MaterialComponents.BottomSheet.Modal">
        <item name="cornerFamily">rounded</item>
        <item name="cornerSizeTopLeft">16dp</item>
        <item name="cornerSizeTopRight">16dp</item>
    </style>

    <style name="OmniSearchChipStyle" parent="Widget.MaterialComponents.Chip.Choice">
        <item name="chipBackgroundColor">@color/omnisearch_background_color_selector</item>
        <item name="chipStrokeColor">@color/omnisearch_stroke_color_selector</item>
        <item name="android:textColor">@color/omnisearch_text_color_selector</item>
        <item name="chipStrokeWidth">1dp</item>
    </style>

</resources>
