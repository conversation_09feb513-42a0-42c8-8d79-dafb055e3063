package com.eatapp.clementine.adapter

import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.eatapp.clementine.data.network.response.closing.Closing
import com.eatapp.clementine.data.network.response.room.Room
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.databinding.ListItemClosingBinding
import com.eatapp.clementine.internal.endOfTheEatDay
import com.eatapp.clementine.internal.startOfTheEatDay
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale

class ClosingsAdapter(val date: Date, val rooms: List<Room>?, val permission: Permission, val listener: (Closing, Int) -> Unit)
    : ListAdapter<Closing, ClosingsAdapter.ItemViewHolder>(ClosingsItemDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ItemViewHolder {

        return ItemViewHolder(date, rooms, ListItemClosingBinding.inflate(
            LayoutInflater.from(parent.context), parent, false))

    }

    override fun onBindViewHolder(holder: ItemViewHolder, position: Int) {

        val item = getItem(position)
        holder.bind(createOnClickListener(item, position), permission, item)
    }

    private fun createOnClickListener(closing: Closing, position: Int): View.OnClickListener {
        return View.OnClickListener {

            if (closing.longerRange || !permission.boolValue) {
                return@OnClickListener
            }

            closing.deleting = true

            listener(closing, position)
        }
    }

    class ItemViewHolder(
        private val date: Date,
        private val rooms: List<Room>?,
        private val binding: ListItemClosingBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(l: View.OnClickListener, p: Permission, i: Closing) {

            when (i.tableIds.isEmpty()) {
                true -> {
                    val c: Calendar = Calendar.getInstance()
                    c.time = endOfTheEatDay(date)
                    c.add(Calendar.MINUTE, -15)

                    val lastSlotForToday = c.time

                    val startTime = if (i.timeRangeBegin.before(startOfTheEatDay(date))) {
                        i.longerRange = true
                        startOfTheEatDay(date)
                    } else { i.timeRangeBegin }

                    val endTime = if (i.timeRangeEnd.after(endOfTheEatDay(date))) {
                        i.longerRange = true
                        lastSlotForToday
                    } else { i.timeRangeEnd }

                    binding.range.text = String.format(
                        "%s - %s",
                        SimpleDateFormat("hh:mm a", Locale.US).format(startTime),
                        SimpleDateFormat("hh:mm a", Locale.US).format(endTime)
                    )
                    binding.types.text = i.types
                }
                false -> {
                    binding.range.text = String.format(
                        "%s - %s",
                        SimpleDateFormat("dd MMM (hh:mm a)", Locale.US).format(i.timeRangeBegin),
                        SimpleDateFormat("dd MMM (hh:mm a)", Locale.US).format(i.timeRangeEnd)
                    )
                    val rAndT = rooms?.map { room ->
                        val tables = room.tables?.filter { it.id in i.tableIds }
                        if (!tables.isNullOrEmpty()) {
                            String.format("%s - %s", room.name, tables.joinToString { it.number })
                        } else{
                            ""
                        }
                    }?.filter {
                        it.isNotBlank()
                    }

                    binding.types.text = rAndT?.joinToString(separator = "\n") { it }
                }
            }

            binding.apply {
                deleteClickListener = l
                closing = i
                permission = p
                executePendingBindings()
            }
        }
    }
}

private class ClosingsItemDiffCallback : DiffUtil.ItemCallback<Closing>() {

    override fun areItemsTheSame(oldItem: Closing, newItem: Closing): Boolean {
        return oldItem.id == newItem.id
    }

    override fun areContentsTheSame(oldItem: Closing, newItem: Closing): Boolean {
        return oldItem == newItem
    }
}