package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignment
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type


class VoucherAssignmentConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<VoucherAssignment>? {
        val listType: Type = object : TypeToken<List<VoucherAssignment>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(voucherAssignments: List<VoucherAssignment>?): String {
        return gson.toJson(voucherAssignments)
    }
}