package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.body.RegisterBody
import com.eatapp.clementine.data.network.response.auth.AuthResponse
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldResponse
import com.eatapp.clementine.data.network.response.guest.GuestTagsResponse
import com.eatapp.clementine.data.network.response.pizzaslicer.PizzaSlicerResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantsResponse
import com.eatapp.clementine.data.network.response.room.RoomsResponse
import com.eatapp.clementine.data.network.response.room.TablesResponse
import com.eatapp.clementine.data.network.response.shift.ShiftsResponse
import com.eatapp.clementine.data.network.response.tag.TagsResponse
import com.eatapp.clementine.data.network.response.templates.MessageTemplatesResponse
import com.eatapp.clementine.data.network.response.templates.WhatsappTemplatesResponse
import com.eatapp.clementine.data.network.response.user.UserResponse
import com.eatapp.clementine.data.network.response.user.UsersResponse
import com.eatapp.clementine.data.network.response.vouchers.VouchersResponse

interface FabricateRepository {

    suspend fun login(email: String, password: String, captchaToken: String): AuthResponse

    suspend fun twoFactor(token: String, otp: String): AuthResponse

    suspend fun register(registerBody: RegisterBody): AuthResponse

    suspend fun restaurants(): RestaurantsResponse

    suspend fun restaurant(restaurantId: String): RestaurantResponse

    suspend fun guestTags(): GuestTagsResponse

    suspend fun rooms(restaurantId: String): RoomsResponse

    suspend fun tables(restaurantId: String): TablesResponse

    suspend fun user(restaurantId: String, userId: String): UserResponse

    suspend fun users(restaurantId: String): UsersResponse

    suspend fun tags(restaurantId: String): TagsResponse

    suspend fun shifts(restaurantId: String): ShiftsResponse

    suspend fun pizzaSlicer(): PizzaSlicerResponse

    suspend fun customFields(): CustomFieldResponse

    suspend fun messageTemplates(restaurantId: String): MessageTemplatesResponse

    suspend fun whatsappTemplates(restaurantId: String): WhatsappTemplatesResponse

    suspend fun vouchers(): VouchersResponse
}