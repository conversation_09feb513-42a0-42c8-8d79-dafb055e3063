package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.body.ClosingBody
import com.eatapp.clementine.data.network.response.closing.ClosingResponse
import com.eatapp.clementine.data.network.response.closing.ClosingsResponse
import java.util.Date

interface ClosingsRepository {

    suspend fun closings(date: Date): ClosingsResponse

    suspend fun insertClosing(closingBody: ClosingBody): ClosingResponse

    suspend fun deleteClosing(closingId: String)

}