package com.eatapp.clementine.data.database.converters

import androidx.room.TypeConverter
import com.eatapp.clementine.data.network.response.room.Table
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import java.lang.reflect.Type;


class TablesConverter {

    private val gson = Gson()

    @TypeConverter
    fun fromString(value: String): List<Table>? {
        val listType: Type = object : TypeToken<List<Table>?>() {}.type
        return gson.fromJson(value, listType)
    }

    @TypeConverter
    fun toString(tables: List<Table>?): String {
        return gson.toJson(tables)
    }
}