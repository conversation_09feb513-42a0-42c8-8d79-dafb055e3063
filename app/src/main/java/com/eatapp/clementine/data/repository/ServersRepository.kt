package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.body.ServerBody
import com.eatapp.clementine.data.network.response.server.ServerResponse
import com.eatapp.clementine.data.network.response.server.ServersResponse

interface ServersRepository {

    suspend fun servers() : ServersResponse

    suspend fun updateServer(serverId: String, body: ServerBody) : ServerResponse

}