package com.eatapp.clementine.data.network

import com.eatapp.clementine.data.network.body.AssignVouchersRequest
import com.eatapp.clementine.data.network.body.ClosingBody
import com.eatapp.clementine.data.network.body.GuestBody
import com.eatapp.clementine.data.network.body.RedeemVoucherAssignmentRequest
import com.eatapp.clementine.data.network.body.RegisterBody
import com.eatapp.clementine.data.network.body.ReservationBody
import com.eatapp.clementine.data.network.body.ServerBody
import com.eatapp.clementine.data.network.body.StatusBody
import com.eatapp.clementine.data.network.response.apiresources.ApiResourceResponse
import com.eatapp.clementine.data.network.response.auth.AuthResponse
import com.eatapp.clementine.data.network.response.closing.ClosingResponse
import com.eatapp.clementine.data.network.response.closing.ClosingsResponse
import com.eatapp.clementine.data.network.response.custom_fields.CustomFieldResponse
import com.eatapp.clementine.data.network.response.daynote.DayNoteResponse
import com.eatapp.clementine.data.network.response.daynote.DayNotesResponse
import com.eatapp.clementine.data.network.response.guest.GuestResponse
import com.eatapp.clementine.data.network.response.guest.GuestTagsResponse
import com.eatapp.clementine.data.network.response.guest.GuestsResponse
import com.eatapp.clementine.data.network.response.notification.NotificationsResponse
import com.eatapp.clementine.data.network.response.omnisearch.OmniSearchResponse
import com.eatapp.clementine.data.network.response.pizzaslicer.PizzaSlicerResponse
import com.eatapp.clementine.data.network.response.reports.ReportsResponse
import com.eatapp.clementine.data.network.response.reservation.ReservationResponse
import com.eatapp.clementine.data.network.response.reservation.ReservationsResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantResponse
import com.eatapp.clementine.data.network.response.restaurant.RestaurantsResponse
import com.eatapp.clementine.data.network.response.room.RoomsResponse
import com.eatapp.clementine.data.network.response.room.TablesResponse
import com.eatapp.clementine.data.network.response.server.ServerResponse
import com.eatapp.clementine.data.network.response.server.ServersResponse
import com.eatapp.clementine.data.network.response.shift.ShiftsResponse
import com.eatapp.clementine.data.network.response.survey.SurveyData
import com.eatapp.clementine.data.network.response.tag.TagsResponse
import com.eatapp.clementine.data.network.response.templates.MessageTemplatesResponse
import com.eatapp.clementine.data.network.response.templates.WhatsappTemplatesResponse
import com.eatapp.clementine.data.network.response.user.UserResponse
import com.eatapp.clementine.data.network.response.user.UsersResponse
import com.eatapp.clementine.data.network.response.vouchers.VouchersResponse
import com.eatapp.clementine.data.network.response.websocket.WebsocketMessage
import com.eatapp.clementine.internal.Constants
import retrofit2.http.Body
import retrofit2.http.DELETE
import retrofit2.http.GET
import retrofit2.http.Header
import retrofit2.http.PATCH
import retrofit2.http.POST
import retrofit2.http.PUT
import retrofit2.http.Path
import retrofit2.http.Query


interface EatApiRestaurant {

    @POST("authentication")
    suspend fun loginAsync(
        @Query("email") email: String,
        @Query("password") password: String,
        @Query("captcha_token") captchaToken: String
    ): AuthResponse

    @POST("authentication/signup")
    suspend fun registerAsync(
        @Body registerBody: RegisterBody
    ): AuthResponse

    @GET("restaurants")
    suspend fun restaurantsAsync(
        @Query("light") light: Boolean,
        @Query("limit") limit: Int
    ): RestaurantsResponse

    @GET("restaurants/{restaurant_id}")
    suspend fun restaurantAsync(
        @Path("restaurant_id") restaurantId: String
    ): RestaurantResponse

    @GET("reservations")
    suspend fun reservationsAsync(
        @Query("limit") limit: Int,
        @Query("start_time_on") date: String
    ): ReservationsResponse

    @GET("reservations/{eat_id}")
    suspend fun reservationAsync(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Path("eat_id") eatId: String
    ): ReservationResponse

    @PUT("reservations/{reservation_id}")
    suspend fun updateReservationAsync(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Path("reservation_id") reservationId: String,
        @Body reservationBody: ReservationBody
    ): ReservationResponse

    @PUT("reservations/{reservation_id}")
    suspend fun updateReservationStatusAsync(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Path("reservation_id") reservationId: String,
        @Body reservationBody: StatusBody
    ): ReservationResponse

    @PUT("reservations/{reservation_id}")
    suspend fun redeemVoucherForReservation(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Path("reservation_id") reservationId: String,
        @Body body: RedeemVoucherAssignmentRequest
    ): ReservationResponse

    @PUT("reservations/{reservation_id}")
    suspend fun updateVouchersForReservation(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Path("reservation_id") reservationId: String,
        @Body body: AssignVouchersRequest
    ): ReservationResponse

    @POST("reservations")
    suspend fun createReservationAsync(
        @Body reservationBody: ReservationBody
    ): ReservationResponse

    @DELETE("reservations/{reservation_id}")
    suspend fun deleteReservationAsync(
        @Path("reservation_id") reservationId: String
    )

    @GET("reservations")
    suspend fun guestReservationsAsync(
        @Query("limit") limit: Int,
        @Query("guest_id") date: String
    ): ReservationsResponse

    @GET("rooms")
    suspend fun roomsAsync(@Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String): RoomsResponse

    @GET("tables")
    suspend fun tablesAsync(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Query("limit") limit: Int,
    ): TablesResponse

    @GET("restaurant_users/{restaurant_user_id}")
    suspend fun userAsync(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Path("restaurant_user_id") userId: String
    ): UserResponse

    @GET("restaurant_users")
    suspend fun users(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Query("limit") limit: Int = 1000,
    ): UsersResponse

    @GET("day_notes")
    suspend fun dayNoteAsync(
        @Query("date") date: String
    ): DayNotesResponse

    @POST("day_notes")
    suspend fun insertDayNoteAsync(
        @Query("date") date: String,
        @Query("content") content: String
    ): DayNoteResponse

    @PUT("day_notes/{note_id}")
    suspend fun updateDayNoteAsync(
        @Path("note_id") noteId: String,
        @Query("content") content: String
    ): DayNoteResponse

    @GET("guests")
    suspend fun guestsAsync(
        @Query("q") query: String?,
        @Query("page") page: Int,
        @Query("limit") limit: Int
    ): GuestsResponse

    @PATCH("guests/{guest_id}")
    suspend fun updateGuestAsync(
        @Path("guest_id") guestId: String,
        @Body guestBody: GuestBody
    ): GuestResponse

    @POST("guests")
    suspend fun createGuestAsync(
        @Body guestBody: GuestBody
    ): GuestResponse

    @DELETE("guests/{guest_id}")
    suspend fun deleteGuestAsync(
        @Path("guest_id") guestId: String
    )

    @GET("tags?limit=999")
    suspend fun guestTagsAsync(): GuestTagsResponse

    @GET("analytics/shifts")
    suspend fun reportsAsync(
        @Query("from") from: String,
        @Query("to") to: String
    ): ReportsResponse

    @GET("tags")
    suspend fun tagsAsync(
        @Query("limit") limit: Int
    ): TagsResponse

    @GET("shift_hours")
    suspend fun shiftsAsync(
        @Header(Constants.RESTAURANT_ID_HEADER) restaurantId: String,
        @Query("status") status: String,
        @Query("limit") limit: Int
    ): ShiftsResponse

    @GET("closing_periods")
    suspend fun closingsAsync(
        @Query("date") date: String,
    ): ClosingsResponse

    @POST("closing_periods")
    suspend fun insertClosingAsync(
        @Body closingBody: ClosingBody
    ): ClosingResponse

    @DELETE("closing_periods/{closing_id}")
    suspend fun deleteClosingAsync(
        @Path("closing_id") closingId: String
    )

    @GET("restaurant_servers")
    suspend fun serversAsync(): ServersResponse

    @PATCH("restaurant_servers/{restaurant_servers_id}")
    suspend fun updateServerAsync(
        @Path("restaurant_servers_id") serverId: String,
        @Body serverBody: ServerBody
    ): ServerResponse

    @GET("surveys")
    suspend fun surveysAsync(): SurveyData

    @GET("resources")
    suspend fun resourcesAsync(): ApiResourceResponse

    @GET("pizza_slicer")
    suspend fun pizzaSliceAsync(): PizzaSlicerResponse

    @GET("hydra")
    suspend fun hydraAsync(): WebsocketMessage

    @GET("search")
    suspend fun search(@Query("q") query: String): OmniSearchResponse

    @GET("custom_fields")
    suspend fun customFields(@Query("limit") limit: Int = 999): CustomFieldResponse

    @POST("authentication/two_factor")
    suspend fun loginTwoFactor(
        @Header("Authorization") token: String,
        @Query("otp_attempt") otp: String
    ): AuthResponse

    @POST("reservations/{reservation_id}/table_ready")
    suspend fun tableReady(
        @Path("reservation_id") reservationId: String
    ): ReservationResponse

    @GET("message_templates")
    suspend fun messageTemplatesAsync(
        @Query("limit") limit: Int = 999
    ): MessageTemplatesResponse

    @GET("whatsapp_templates")
    suspend fun whatsappTemplatesAsync(
        @Query("limit") limit: Int = 999
    ): WhatsappTemplatesResponse

    @GET("vouchers")
    suspend fun vouchers(): VouchersResponse

    @PUT("guests/{guest_id}")
    suspend fun redeemVoucherForGuest(
        @Path("guest_id") guestId: String,
        @Body body: RedeemVoucherAssignmentRequest
    ): GuestResponse

    @PUT("guests/{guest_id}")
    suspend fun updateVouchersForGuest(
        @Path("guest_id") reservationId: String,
        @Body body: AssignVouchersRequest
    ): GuestResponse

    @GET("notifications")
    suspend fun notificationsAsync(
        @Query("page") page: Int,
        @Query("limit") limit: Int = 30
    ): NotificationsResponse

}