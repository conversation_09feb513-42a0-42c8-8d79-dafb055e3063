package com.eatapp.clementine.data.repository

import com.eatapp.clementine.data.network.EatApiRestaurant
import com.eatapp.clementine.data.network.body.ClosingBody
import com.eatapp.clementine.data.network.response.closing.ClosingResponse
import com.eatapp.clementine.data.network.response.closing.ClosingsResponse
import com.eatapp.clementine.internal.simpleDate
import java.util.Date
import javax.inject.Inject

class ClosingsRepositoryImpl @Inject constructor(
    private val eatApiRestaurant: EatApiRestaurant
) : ClosingsRepository {

    override suspend fun closings(date: Date): ClosingsResponse
        = eatApiRestaurant.closingsAsync(simpleDate(date))

    override suspend fun insertClosing(closingBody: ClosingBody): ClosingResponse
        = eatApiRestaurant.insertClosingAsync(closingBody)

    override suspend fun deleteClosing(closingId: String)
        = eatApiRestaurant.deleteClosingAsync(closingId)

}