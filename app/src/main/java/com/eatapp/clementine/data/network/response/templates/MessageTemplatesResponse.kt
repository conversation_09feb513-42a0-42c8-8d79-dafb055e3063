package com.eatapp.clementine.data.network.response.templates

import android.os.Parcelable
import com.eatapp.clementine.data.network.response.message.ChannelType
import com.google.gson.annotations.SerializedName
import kotlinx.parcelize.IgnoredOnParcel
import kotlinx.parcelize.Parcelize

data class MessageTemplatesResponse(
    @SerializedName("data")
    val data: List<MessageTemplate>
)

@Parcelize
data class MessageTemplate(
    @SerializedName("id")
    override val id: String,
    @SerializedName("type")
    val type: String,
    @SerializedName("attributes")
    val attributes: MessageTemplateAttributes
) : Template, Parcelable {
    override val body: String
        get() = attributes.body

    override val name: String
        get() = attributes.name

    override val messageType: String
        get() = attributes.messageType

    override val updatedAt: String
        get() = attributes.updatedAt

    override val channel: ChannelType
        get() = when (attributes.channel) {
            in 0..99 -> ChannelType.SMS
            in 100..199 -> ChannelType.EMAIL
            else -> ChannelType.SMS
        }

    @IgnoredOnParcel
    override var previewExpanded: Boolean = false

    @IgnoredOnParcel
    override var disabled: Boolean = false

    val isDefault: Boolean
        get() = attributes.isDefault

    val externalTemplateId: String?
        get() = attributes.externalTemplateId

    val reservationOrigin: String?
        get() = attributes.reservationOrigin

    val messageScope: String
        get() = attributes.messageScope
}

@Parcelize
data class MessageTemplateAttributes(
    @SerializedName("body")
    val body: String,
    @SerializedName("channel")
    val channel: Int,
    @SerializedName("default")
    val isDefault: Boolean,
    @SerializedName("external_template_id")
    val externalTemplateId: String?,
    @SerializedName("message_type")
    val messageType: String,
    @SerializedName("name")
    val name: String,
    @SerializedName("reservation_origin")
    val reservationOrigin: String?,
    @SerializedName("message_scope")
    val messageScope: String,
    @SerializedName("updated_at")
    val updatedAt: String
) : Parcelable