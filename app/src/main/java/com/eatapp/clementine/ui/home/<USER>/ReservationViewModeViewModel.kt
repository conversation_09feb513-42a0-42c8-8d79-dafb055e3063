package com.eatapp.clementine.ui.home.more

import androidx.lifecycle.ViewModel
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.EatManager
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class ReservationViewModeViewModel @Inject constructor(
    private val eatManager: EatManager,
    private val analyticsManager: AnalyticsManager
): ViewModel() {

    var isReservationCompactMode: <PERSON>olean
        get() {
            return eatManager.reservationCompactMode
        }
        set(value) {
            eatManager.reservationCompactMode = value
            if (value) {
                analyticsManager.reservationListViewCompactMode()
            } else {
                analyticsManager.reservationListViewDetailMode()
            }
        }
}