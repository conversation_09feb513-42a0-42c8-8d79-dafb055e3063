package com.eatapp.clementine.ui.common.messages

import SingleLiveEvent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.data.network.response.message.ChannelType
import com.eatapp.clementine.data.network.response.message.Message
import com.eatapp.clementine.data.network.response.message.MessagesErrorDetails
import com.eatapp.clementine.data.network.response.reservation.Channel
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.templates.Template
import com.eatapp.clementine.data.repository.MessagingRepository
import com.eatapp.clementine.internal.Constants.GUEST_CUSTOM
import com.eatapp.clementine.internal.EatException
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import javax.inject.Inject
import kotlin.concurrent.fixedRateTimer

@HiltViewModel
class MessagesViewModel @Inject constructor(
    val eatManager: EatManager,
    private val messagingRepository: MessagingRepository
) : BaseViewModel() {

    companion object {
        private const val ERROR_INVALID_EMAIL = "messaging.invalid_email"
        private const val ERROR_INVALID_PHONE = "messaging.invalid_phone"
        private const val ERROR_ALREADY_SENT = "messaging.already_invoked"
        private const val ERROR_RESERVATION_ENDED = "messaging.reservation_ended"
        private const val ERROR_RESERVATION_ENDED_YESTERDAY =
            "messaging.reservation_ended_yesterday"
        private const val ERROR_RESERVATION_IN_PROGRESS = "messaging.reservation_in_progress"
        private const val ERROR_RESERVATION_NOT_STARTED = "messaging.reservation_not_started"
        private const val ERROR_RESERVATION_NOT_FOUND = "messaging.reservation_not_found"
        private const val ERROR_SMS_QUOTA_EXCEEDED = "messaging.sms_quota_exceeded"
        private const val ERROR_UNREASONABLE = "messaging.unreasonable"
    }

    var reservation: Reservation? = null
    var restaurantId: String = eatManager.restaurantId()
    var guest: Guest? = null
    var preferredChannel: ChannelType = ChannelType.WHATSAPP

    private val _messageError = SingleLiveEvent<Pair<String, String>>()
    val messageError: LiveData<Pair<String, String>> = _messageError

    private val _messages = MutableLiveData<List<Message>>()
    val messages: LiveData<List<Message>> = _messages

    private val _canSendWhatsapp = MutableLiveData<Boolean>()
    val canSendWhatsapp: LiveData<Boolean> = _canSendWhatsapp

    private val _canSendSms = MutableLiveData<Boolean>()
    val canSendSms: LiveData<Boolean> = _canSendSms

    private val _canSendEmail = MutableLiveData<Boolean>()
    val canSendEmail: LiveData<Boolean> = _canSendEmail

    private val _errorDetails = MutableLiveData<MessagesErrorDetails>()
    val errorDetails: LiveData<MessagesErrorDetails> = _errorDetails

    var initialFetch: Boolean = true
    var newMessage: Boolean = true

    init {
        timers.add(fixedRateTimer("default", false, 10L, 5000L) {
            fetchMessages()
        })
    }

    fun reservation(reservation: Reservation) {
        this.reservation = reservation
    }

    fun restaurantId(restaurantId: String) {
        this.restaurantId = restaurantId
    }

    private fun fetchMessages() {
        if (initialFetch) {
            loading(true)
        }

        launch({
            val response = messagingRepository.messages(
                restaurantId = restaurantId,
                guestId = null,
                reservationId = reservation?.id
            )

            _canSendEmail.value = response.meta.canSendEmail
            _canSendSms.value = response.meta.canSendSms
            _canSendWhatsapp.value = response.meta.canSendWhatsapp
            _errorDetails.value = response.meta.errorDetails

            val previousMessagesCount = _messages.value?.size ?: 0
            val newMessagesCount = response.messages.size
            newMessage = newMessagesCount > previousMessagesCount

            reservation?.guest?.id?.let { guestId ->
                val convo = eatManager.conversations()?.find { conversation ->
                    conversation.guestId == guestId
                }

                if (newMessage && convo != null && convo.unreadMessagesCount > 0) {
                    messagingRepository.markAsRead(restaurantId, guestId)

                    eatManager.conversations()?.find { conversation ->
                        conversation.guestId == guestId
                    }?.let { conversation ->
                        conversation.unreadMessagesCount = 0
                    }
                }
            }

            loading(false)

            _messages.postValue(response.messages.reversed())

            delay(10)
            initialFetch = false

        }, true)
    }

    fun sendMessage(
        template: Template?,
        text: String?
    ) {
        // Add temporary message to UI while waiting for server response
        text?.let {
            val tempMessage = Message(
                id = null,
                content = it,
                inbound = false,
                createdAt = System.currentTimeMillis(),
                channel = preferredChannel
            )

            val currentMessages = _messages.value?.toMutableList() ?: mutableListOf()
            currentMessages.add(tempMessage)
            newMessage = true
            _messages.value = currentMessages
        }

        viewModelScope.launch {
            try {
                // Determine which ID to use
                val reservationId = reservation?.id
                val guestId = if (reservationId == null) guest?.id else null

                when (preferredChannel) {
                    ChannelType.EMAIL -> messagingRepository.sendEmail(
                        restaurantId = restaurantId,
                        reservationId = reservationId,
                        guestId = guestId,
                        templateId = template?.id,
                        text = text
                    )

                    ChannelType.SMS -> messagingRepository.sendSms(
                        restaurantId = restaurantId,
                        reservationId = reservationId,
                        guestId = guestId,
                        templateId = template?.id,
                        text = text
                    )

                    ChannelType.WHATSAPP -> messagingRepository.sendWhatsapp(
                        restaurantId = restaurantId,
                        reservationId = reservationId,
                        guestId = guestId,
                        templateId = template?.id,
                        text = text
                    )
                }
            } catch (e: Exception) {
                handleError(EatException(e, false), template)
            } finally {
                delay(1500)
                fetchMessages()
            }
        }
    }

    private fun handleError(
        e: EatException,
        template: Template?
    ) {
        val eventType = when (template?.messageType) {
            "reservation_confirmed" -> "Confirmation"
            "wait_list_table_ready" -> "Table ready"
            "reservation_requested" -> "Requested"
            "reservation_late" -> "Late"
            "reservation_no_show" -> "No show"
            "reservation_denied" -> "Denied"
            "reservation_canceled" -> "Canceled"
            "reservation_finished" -> "Finished"
            else -> "Confirmation"
        }

        val (title, message) = when (e.key) {
            ERROR_ALREADY_SENT -> {
                "Text message already sent" to
                        "You have already sent this guest a $eventType text message."
            }

            ERROR_INVALID_PHONE -> {
                "Invalid phone" to
                        "The guest has an invalid phone number. Please review and try again."
            }

            ERROR_INVALID_EMAIL -> {
                "Invalid email" to
                        "The guest has an invalid email. Please review and try again."
            }

            ERROR_RESERVATION_NOT_STARTED -> {
                "Unable to send message" to
                        "The reservation has not started yet."
            }

            ERROR_RESERVATION_IN_PROGRESS -> {
                "Unable to send message" to
                        "The reservation is in progress."
            }

            ERROR_RESERVATION_ENDED -> {
                "Unable to send message" to
                        "This reservation has already ended."
            }

            ERROR_RESERVATION_ENDED_YESTERDAY -> {
                "Unable to send message" to
                        "You can not send messages for a reservation that ended more than 24 hours ago."
            }

            ERROR_RESERVATION_NOT_FOUND -> {
                "Reservation not found" to
                        "Unable to send $eventType message since this reservation has been deleted or removed."
            }

            ERROR_SMS_QUOTA_EXCEEDED -> {
                "SMS quota exceeded" to
                        "Your SMS quota has been exceeded and guest will no longer receive SMSes"
            }

            ERROR_UNREASONABLE -> {
                "Unable to send message" to
                        "This reservation has already past it's start time or is in a status that conflicts with the $eventType message"
            }

            else -> {
                "No internet" to
                        "Unable to send $eventType message, please check your Internet connection and try again."
            }
        }

        _messageError.postValue(Pair(title, message))
    }

    fun messageTypes(channel: ChannelType = preferredChannel): List<String> {
        return _messages.value?.filter { message ->
            message.channel == channel
        }?.map { message ->
            message.messageType
        }?.distinct() ?: emptyList()
    }
}