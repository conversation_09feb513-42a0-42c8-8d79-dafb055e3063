package com.eatapp.clementine.ui.launch

import SingleLiveEvent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.eatapp.clementine.data.network.response.auth.Auth
import com.eatapp.clementine.data.repository.FabricateRepository
import com.eatapp.clementine.internal.EatException
import com.eatapp.clementine.internal.isValidEmail
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.CaptchaManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import com.google.android.recaptcha.RecaptchaAction
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class SignInViewModel @Inject constructor(
    private val eatManager: EatManager,
    private val analyticsManager: AnalyticsManager,
    private val captchaManager: CaptchaManager,
    private val fabricateRepository: FabricateRepository
) : BaseViewModel() {

    private val reCaptchaLoginAction = "clementine_login"

    val email = MutableLiveData<String>()
    val password = MutableLiveData<String>()
    val authentication = MutableLiveData<SharedLaunchViewModel.AuthenticationState>()

    private val _twoFactorTriggered = SingleLiveEvent<String>()
    val twoFactorTriggered: LiveData<String> = _twoFactorTriggered

    private val _captchaError = SingleLiveEvent<Boolean>()
    val captchaError: LiveData<Boolean> = _captchaError

    init {
        viewModelScope.launch {
            captchaManager.recaptchaClient = captchaManager.fetchRecaptchaClient().getOrNull()
        }
    }

    fun signInBtnClick() {

        if (validate()) return

        loading(true)

        viewModelScope.launch {
            try {
                val client = captchaManager.fetchRecaptchaClient().getOrThrow()
                val token =
                    client.execute(RecaptchaAction.custom(reCaptchaLoginAction)).getOrThrow()

                launch({
                    val auth = fabricateRepository.login(
                        email.value.toString(),
                        password.value.toString(),
                        token
                    )

                    val twoFactorToken = auth.data.attributes.twoFactorToken

                    if (!twoFactorToken.isNullOrBlank()) {
                        _twoFactorTriggered.value = twoFactorToken!!
                    } else if (!auth.data.token.isNullOrBlank()) {
                        completeLogin(auth.data)
                    }

                    loading(false)
                })

            } catch (e: Exception) {
                _captchaError.postValue(true)
                loading(false)
            }
        }
    }

    private fun completeLogin(auth: Auth) {
        eatManager.userData(auth)
        analyticsManager.trackLogin()

        authentication.postValue(SharedLaunchViewModel.AuthenticationState.AUTHENTICATED)
    }

    private fun validate(): Boolean {
        val email = email.value?.toString()
        val password = password.value?.toString()

        return when {
            email.isNullOrBlank() || password.isNullOrBlank() -> {
                setError(EatException("Validation", "Email or password cannot be empty"))
                true
            }
            !email.isValidEmail() -> {
                setError(EatException("Validation", "Please enter a valid email address"))
                true
            }
            password.length < 8 -> {
                setError(EatException("Validation", "Password must contain at least 8 characters"))
                true
            }
            else -> false
        }
    }

}
