package com.eatapp.clementine.ui.reservation.payments.refund

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.data.network.response.user.User
import com.eatapp.clementine.data.repository.PaymentsRepository
import com.eatapp.clementine.internal.SelectorItem
import com.eatapp.clementine.internal.asMutableLiveData
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class RefundPaymentViewModel @Inject constructor(
    private val repository: PaymentsRepository,
    val eatManager: EatManager
) : BaseViewModel() {

    private val _payment = MutableLiveData<Payment>()
    val payment: LiveData<Payment> = _payment

    private val _updatedPayment = MutableLiveData<Payment>()
    val updatedPayment: LiveData<Payment> = _updatedPayment

    private val _validationError = false.asMutableLiveData()
    val validationError: LiveData<Boolean> = _validationError

    private val _isFullAmount = true.asMutableLiveData()
    val isFullAmount: LiveData<Boolean> = _isFullAmount

    private val _refundEligibleUsers = MutableLiveData<List<User>>()
    val refundEligibleUsers: LiveData<List<User>> = _refundEligibleUsers

    private val selectedUser = MutableLiveData<User>()

    init {
        _refundEligibleUsers.value = eatManager.users()?.filter { it.allowRefund }
    }

    fun setValidationError(hasError: Boolean) {
        _validationError.value = hasError
    }

    fun setFullAmount(isFullAmount: Boolean) {
        _isFullAmount.value = isFullAmount
    }

    fun setPayment(payment: Payment) {
        _payment.value = payment
    }

    fun setSelectedUser(user: User) {
        selectedUser.value = user
    }

    fun validateAmount(amount: String) {
        val remainingAmount = payment.value?.attributes?.refundBalance ?: 0.0
        val amountToRefund = amount.toDoubleOrNull()

        if (amountToRefund == null) {
            _validationError.value = true
            return
        }

        _validationError.value = amountToRefund > remainingAmount || amountToRefund <= 0
    }

    fun userSelectionList(): MutableList<SelectorItem> {
        return _refundEligibleUsers.value?.map {
            SelectorItem(
                it.id,
                it.name ?: "",
                it,
                icon = null,
                color = 0,
                isSelected = false,
                isHeader = false,
                isDisabled = false
            )
        }?.toMutableList() ?: mutableListOf()
    }

    fun refundPayment(refundAmount: Double) {
        launch({
            loading(true)
            val response = repository.refundPayment(
                paymentId = payment.value!!.id,
                refundAmount = refundAmount,
                userId = selectedUser.value!!.id,
                pin = selectedUser.value?.pinCode
            )
            _updatedPayment.value = response.payment
            loading(false)
        })
    }

    fun refundAllowed(): Boolean {
        return refundEligibleUsers.value?.isNotEmpty() ?: false
    }
}