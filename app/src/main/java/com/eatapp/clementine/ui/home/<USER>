package com.eatapp.clementine.ui.home

import SingleLiveEvent
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.viewModelScope
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemCategory
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemModel
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemNativeScreens
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemType
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.restaurant.AccountStateType
import com.eatapp.clementine.data.network.response.user.Permission
import com.eatapp.clementine.data.repository.FabricateRepository
import com.eatapp.clementine.data.repository.ReservationsRepository
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.Commands
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.ShiftManager
import com.eatapp.clementine.internal.managers.manageReservations
import com.eatapp.clementine.ui.launch.FabricateViewModel
import com.eatapp.clementine.views.LockdownView
import com.google.firebase.messaging.FirebaseMessaging
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import java.util.*
import javax.inject.Inject

@HiltViewModel
class HomeActivityViewModel @Inject constructor(
    val analyticsManager: AnalyticsManager,
    val eatManager: EatManager,
    private val reservationsRepository: ReservationsRepository,
    private val dataManager: DataManager,
    private val fabricateRepository: FabricateRepository,
    shiftManager: ShiftManager
) : FabricateViewModel(
    eatManager,
    shiftManager,
    dataManager,
    analyticsManager,
    fabricateRepository
) {

    private val _sharedDate = SingleLiveEvent<Date>()
    val sharedDate: LiveData<Date> = _sharedDate

    private val _navItems = MutableLiveData<List<NavigationItemCategory>>()
    val navItems: LiveData<List<NavigationItemCategory>> = _navItems

    private val _restaurantChanged = SingleLiveEvent<Void>()
    val restaurantChanged: LiveData<Void> = _restaurantChanged

    var permission: Permission = eatManager.permission(identifier = manageReservations)

    val isFreemium = eatManager.accountState() == AccountStateType.IN_HOUSE

    init {
        val result = eatManager.navItems.filter { it.path != HomeActivity.CATEGORY_SETUP_PATH }
        filterNavigationItems(result)
        _navItems.value = result
    }

    fun updateSelectedRestaurant() {
        eatManager.restaurants()?.forEach {
            it.isSelected = it.id == eatManager.restaurantId()
        }
    }

    fun updateSharedDate(date: Date) {
        _sharedDate.postValue(date)
    }

    fun toggleSectionExpanded(categoryName: String) {
        val list = _navItems.value?.map { it.copy() } ?: emptyList()

        list.firstOrNull { it.expanded }?.let {
            if (it.title != categoryName) {
                it.expanded = false
            }
        }
        list.firstOrNull { it.title == categoryName }?.let {
            it.expanded = it.expanded.not()
        }

        _navItems.value = list
    }

    fun toggleItemSelected(categoryName: String, itemName: String) {
        val list = navItems.value?.map { it.copy() } ?: emptyList()

        // Deselect category
        list.firstOrNull { it.selected }?.let { category ->
            if (category.title != categoryName) {
                category.selected = false
            }

            category.subCategories?.let { subcategories ->
                subcategories.forEach { subcategory ->
                    subcategory.items?.firstOrNull { it.selected }?.selected = false
                }
            } ?: run {
                category.items?.firstOrNull { it.selected }?.selected = false
            }
        }

        // Select category and item
        list.firstOrNull { it.title == categoryName }?.let { category ->
            category.selected = true

            category.subCategories?.map { subcategory ->
                subcategory.copy(
                    items = subcategory.items?.map { item ->
                        // Select the desired item and deselect the others
                        item.copy(selected = item.title == itemName)
                    }
                )
            }?.let { updatedSubCategories ->
                category.subCategories = updatedSubCategories
            } ?: run {
                category.items?.map { item ->
                    // Select the desired item and deselect the others
                    item.copy(selected = item.title == itemName)
                }?.let { updatedItems ->
                    category.items = updatedItems
                }
            }
        }

        _navItems.value = list
    }

    fun deselectAllItems() {
        val list = navItems.value?.map { it.copy() } ?: emptyList()

        val selectedCategory =
            list.firstOrNull { it.selected }

        selectedCategory?.selected = false

        selectedCategory?.subCategories?.map { subcategory ->
            subcategory.copy(
                items = subcategory.items?.map { item ->
                    // Select the desired item and deselect the others
                    item.copy(selected = false)
                }
            )
        }?.let { updatedSubCategories ->
            selectedCategory.subCategories = updatedSubCategories
        } ?: run {
            selectedCategory?.items?.map { item ->
                // Select the desired item and deselect the others
                item.copy(selected = false)
            }?.let { updatedItems ->
                selectedCategory.items = updatedItems
            }
        }

        _navItems.value = list
    }

    fun collapseCategory(): String? {
        _navItems.value?.firstOrNull { it.expanded }?.let {
            it.expanded = false
            return it.title
        }
        return null
    }

    fun findCategory(categoryName: String): NavigationItemCategory? {
        return navItems.value?.firstOrNull { it.title == categoryName }
    }

    fun findItemAndCategory(path: String): Pair<NavigationItemCategory, NavigationItemModel?>? {
        val categories = navItems.value

        categories?.forEach { category ->
            category.subCategories?.let { subcategories ->
                subcategories.forEach { subcategory ->
                    return Pair(category, subcategory.items?.firstOrNull { it.path == path })
                }
            } ?: run {
                return Pair(category, category.items?.firstOrNull { it.path == path })
            }
        }

        return null
    }

    fun unRegisterUser() {
        FirebaseMessaging.getInstance().unsubscribeFromTopic(
            "restaurant_${eatManager.restaurantId()}"
        )
        dataManager.updateSubscription(Commands.Unsubscribe.type, eatManager.restaurantId())
        dataManager.disconnect()
        eatManager.flush()
        analyticsManager.unregisterUser()
    }

    fun changeRestaurant(restaurantId: String) {

        loading(true)

        if (dataManager.isConnected()) {
            dataManager.updateSubscription(Commands.Unsubscribe.type, eatManager.restaurantId())
        }

        launch({

            val restaurant = fabricateRepository.restaurant(restaurantId)
            eatManager.restaurant(restaurant)

            LockdownView.messageDisplayedAt = null

            registerUser()

            fetchRestaurantData(eatManager.restaurantId())

        }, false)
    }

    override fun restaurantDataFetched() {
        super.restaurantDataFetched()
        _restaurantChanged.call()
    }

    private fun filterNavigationItems(categories: List<NavigationItemCategory>) {

        val isNativeScreenSupported: (NavigationItemModel) -> Boolean = { item ->
            NavigationItemNativeScreens.entries.map { it.path }.contains(item.path)
        }

        val shouldRemove: (NavigationItemModel) -> Boolean = { item ->
            item.type == NavigationItemType.APP && !isNativeScreenSupported(item)
        }

        categories.forEach { category ->
            category.subCategories?.let {
                it.forEach { subcategory ->
                    subcategory.items = subcategory.items?.filterNot { item ->
                        shouldRemove(item)
                    }
                }
            } ?: run {
                category.items = category.items?.filterNot { item ->
                    shouldRemove(item)
                }
            }
        }
    }
}