package com.eatapp.clementine.ui.home

import android.Manifest
import android.content.Intent
import android.content.pm.PackageManager
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.Gravity
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup.LayoutParams.WRAP_CONTENT
import android.widget.PopupWindow
import androidx.activity.OnBackPressedCallback
import androidx.activity.result.contract.ActivityResultContracts
import androidx.activity.viewModels
import androidx.core.content.ContextCompat
import androidx.core.view.updateLayoutParams
import androidx.navigation.NavController
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.NavigationUI
import androidx.recyclerview.widget.SimpleItemAnimator
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemCategory
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemModel
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemNativeScreens
import com.eatapp.clementine.data.network.response.apiresources.NavigationItemType
import com.eatapp.clementine.databinding.ActivityHomeBinding
import com.eatapp.clementine.databinding.NavigationPopupLayoutBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.Constants.RESERVATION_KEY_EXTRA
import com.eatapp.clementine.internal.Endpoints
import com.eatapp.clementine.internal.isTablet
import com.eatapp.clementine.internal.px
import com.eatapp.clementine.internal.showConfirmationAlert
import com.eatapp.clementine.internal.showErrorAlert
import com.eatapp.clementine.internal.slideView
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.ui.common.hubspot.HubspotActivity
import com.eatapp.clementine.ui.home.overview.OverviewFragment
import com.eatapp.clementine.ui.home.reports.ReportsFragment
import com.eatapp.clementine.ui.home.tab_bar.NavigationCategoriesAdapter
import com.eatapp.clementine.ui.launch.LaunchActivity
import com.eatapp.clementine.ui.reservation.ReservationActivity
import com.eatapp.clementine.views.TabBarVenueView
import dagger.hilt.android.AndroidEntryPoint
import java.util.Calendar
import java.util.Date
import kotlin.math.min
import androidx.navigation.findNavController
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.internal.showAlert
import com.eatapp.clementine.ui.base.BaseActivity
import com.eatapp.clementine.ui.home.overflow.NotificationsFragment
import com.eatapp.clementine.ui.home.overview.ReservationsFragment
import com.eatapp.clementine.views.BottomSheetFragment
import com.google.firebase.messaging.FirebaseMessaging

@AndroidEntryPoint
class HomeActivity : BaseActivity<ActivityHomeBinding>() {

    val vm by viewModels<HomeActivityViewModel>()

    override fun getLayoutId() = R.layout.activity_home

    private lateinit var navigationCategoriesAdapter: NavigationCategoriesAdapter

    private var tabBarExpanded = false
    private var restaurantSelectorExpanded = false

    companion object {
        val SIDE_BAR_EXPANDED_WIDTH = 320.px
        val SIDE_BAR_COLLAPSED_WIDTH = 72.px
        val RESERVE_BUTTON_EXPANDED_WIDTH = 116.px
        val RESERVE_BUTTON_COLLAPSED_WIDTH = 48.px
        val RESTAURANT_SELECTOR_MAX_HEIGHT = 246.px
        val POPUP_WIDTH = 304.px
        const val RESTAURANT_SELECTOR_COLLAPSED_HEIGHT = 0
        const val CATEGORY_SUPPORT_PATH = "support"
        const val CATEGORY_SETUP_PATH = "setup"
    }

    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission(),
    ) { isGranted: Boolean ->
        if (!isGranted) {
            showErrorAlert(getString(R.string.notifications_permission_title),
                getString(R.string.notifications_permission_description))
        } else {
            subscribeToChannel()
        }
    }

    override fun onCreated(savedInstanceState: Bundle?) {

        setupUI()
        observe()

        /*
         In case app is killed and intent is
         propagated through LaunchActivity
         */
        checkIntentForReservation(intent)

        vm.updateSelectedRestaurant()

        (binding.container.parent as? View)?.setBackgroundColor(ContextCompat.getColor(binding.container.context, R.color.grey800))

        // Disable back navigation to support pineapple navigation
        if (isTablet) {
            onBackPressedDispatcher.addCallback(this, object : OnBackPressedCallback(true) {
                override fun handleOnBackPressed() {}
            })
        }

        askNotificationPermission()
    }

    private fun setupUI() = with(binding) {
        val navController = <EMAIL>(R.id.nav_host_fragment)
        setupBottomNavigation(navController)

        binding.btnMenu?.setOnClickListener {
            expandSideBar(!tabBarExpanded)
        }

        preselectFloor()

        navigationCategoriesAdapter = NavigationCategoriesAdapter(
            itemClickListener = { category, item ->
                if (item.selected) {
                    expandSideBar(false)
                } else {
                    loadNavigationItem(category.icon, item)
                    vm.toggleItemSelected(category.title, item.title)
                }

            }, categoryClickListener = { category, view ->

                if (category.path == CATEGORY_SUPPORT_PATH) {
                    openSupportScreen()
                    return@NavigationCategoriesAdapter
                }

                vm.toggleSectionExpanded(category.title)
                if (!tabBarExpanded) {
                    showCategoryPopup(view, category)
                }
            }
        )

        containerVenueView?.restaurantClickListener = {
            if (it != vm.eatManager.restaurantId()) {
                expandRestaurantSelector(false)
                expandSideBar(false)
                changeRestaurant(it)
            }
        }

        containerVenueView?.submitList(vm.eatManager.restaurants() ?: emptyList())

        rvNavigationCategories?.adapter = navigationCategoriesAdapter
        (rvNavigationCategories?.itemAnimator as? SimpleItemAnimator)?.supportsChangeAnimations =
            false

        btnReserve?.isEnabled = !vm.isFreemium
        btnReserve?.setOnClickListener {
            createReservation()
        }

        reserve?.isEnabled = !vm.isFreemium
        reserve?.setOnClickListener {
            createReservation()
        }

        containerLogout?.setOnClickListener {
            <EMAIL>(R.string.logout_title, R.string.logout_desc) {
                vm.unRegisterUser()
                redirectToLogin()
            }
        }

        containerVenueView?.setOnContainerSelectorClickListener {
            if (tabBarExpanded) {
                expandRestaurantSelector(!restaurantSelectorExpanded)
            } else {
                showRestaurantSelectorPopup(it)
            }
        }

        loadRestaurantName()
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        /*
         Launches when qr code is scanned or user taps
         the push notification (if HomeActivity is running)
        */
        checkIntentForReservation(intent)
    }

    private fun redirectToLaunch(reservationKey: String?) {
        Intent(this, LaunchActivity::class.java).apply {
            putExtra(RESERVATION_KEY_EXTRA, reservationKey)
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            startActivity(this)
            finish()
        }
    }

    @Deprecated("Deprecated in Java")
    override fun onBackPressed() {
        val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment)
        val fr = navHostFragment?.childFragmentManager!!.fragments[0]
        if (fr is OverviewFragment) fr.disconnectWs()
        super.onBackPressed()
    }

    private fun askNotificationPermission() {
        // This is only necessary for API level >= 33 (TIRAMISU)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(this, Manifest.permission.POST_NOTIFICATIONS) != PackageManager.PERMISSION_GRANTED) {
                if (shouldShowRequestPermissionRationale(Manifest.permission.POST_NOTIFICATIONS)) {
                   showAlert(resources.getString(R.string.enable_notifications_title),
                       resources.getString(R.string.enable_notifications_desc),
                       resources.getString(R.string.yes),
                       resources.getString(R.string.no_thanks),
                       positiveButtonListener =  {
                           requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                       })
                } else {
                    requestPermissionLauncher.launch(Manifest.permission.POST_NOTIFICATIONS)
                }
            } else {
                subscribeToChannel()
            }
        } else {
            subscribeToChannel()
        }
    }

    private fun subscribeToChannel() {
        FirebaseMessaging.getInstance().subscribeToTopic("restaurant_${vm.eatManager.restaurantId()}")
            .addOnCompleteListener { task ->
                if (task.isSuccessful) {
                    Log.d("FCM", "Subscribed to restaurant topic")
                }
            }
    }

    fun updateSharedDate(date: Date) {
        vm.updateSharedDate(date)
    }

    fun navigateToOmniSearch() {
        val navController = this.findNavController(R.id.nav_host_fragment)
        navController.navigate(R.id.omnisearch_fragment)
        vm.deselectAllItems()
    }

    private fun observe() {
        vm.navItems.observe(this) {
            navigationCategoriesAdapter.submitList(it)
        }

        vm.restaurantChanged.observe(this) {
            val navHostFragment =
                supportFragmentManager.findFragmentById(R.id.nav_host_fragment) as NavHostFragment
            navHostFragment.navController.popBackStack()
            recreate()
        }
    }

    private fun expandSideBar(expanded: Boolean) = with(binding) {
        <EMAIL> = expanded

        if (!expanded) {
            expandRestaurantSelector(false)
            vm.collapseCategory()?.let {
                navigationCategoriesAdapter.reloadItem(it)
            }
        }

        btnMenu?.setImageResource(
            if (expanded) R.drawable.ic_navigation_menu_open else R.drawable.ic_icon_menu
        )

        containerNavigation?.let {

            it.slideView(
                it.layoutParams.width,
                if (expanded) SIDE_BAR_EXPANDED_WIDTH else SIDE_BAR_COLLAPSED_WIDTH,
                animateHeight = false
            )

            btnReserve?.let { reserve ->
                if (!expanded) {
                    tvReserve?.visibleOrGone = false
                    containerVenueView?.binding?.ivChevron?.visibleOrGone = false
                    containerLogout?.visibleOrGone = false
                    navigationCategoriesAdapter.toggleSideBarState(false)
                }
                reserve.slideView(
                    reserve.layoutParams.width,
                    if (expanded) RESERVE_BUTTON_EXPANDED_WIDTH else RESERVE_BUTTON_COLLAPSED_WIDTH,
                    animateHeight = false
                ) {
                    tvReserve?.visibleOrGone = expanded
                    containerVenueView?.binding?.ivChevron?.visibleOrGone = expanded
                    containerLogout?.visibleOrGone = expanded
                    containerVenueView?.binding?.containerIconArrow?.visibleOrGone = !expanded
                    navigationCategoriesAdapter.toggleSideBarState(expanded)
                }
            }
        }
    }

    private fun expandRestaurantSelector(expanded: Boolean) {
        restaurantSelectorExpanded = expanded
        binding.containerVenueView?.expandRestaurantSelector(restaurantSelectorExpanded)
    }

    private fun checkIntentForReservation(intent: Intent?) {
        val reservationIdOrKey = intent?.getStringExtra(RESERVATION_KEY_EXTRA)
            ?: intent?.data?.host

        /*
          Executes if process is killed (key or id is passed to LaunchActivity)
        */
        if (vm.eatManager.restaurants().isNullOrEmpty()) {
            redirectToLaunch(reservationIdOrKey)
            return
        }

        /*
          Executes if HomeActivity is running or relaunched (in case process is killed)
        */
        reservationIdOrKey?.let {
            loadReservation(it)
        }
    }

    private fun loadReservation(keyOrId: String) {
        if (vm.permission.boolValue) {

            intent?.data?.host?.let {
                vm.analyticsManager.qrScannerSuccess()
            }?: run {
                vm.analyticsManager.pushNotificationReservation()
            }

            val intent = Intent(this, ReservationActivity::class.java)
            intent.putExtra(RESERVATION_KEY_EXTRA, keyOrId)
            startActivityIntent.launch(intent)

        } else {

            showErrorAlert(
                resources.getString(R.string.edit_reservation),
                vm.permission.errorMessage
            )
        }
    }

    private fun setupBottomNavigation(navController: NavController) {
        binding.navView?.let {
            NavigationUI.setupWithNavController(it, navController)
        }
    }

    private fun loadNavigationItem(categoryIcon: String?, item: NavigationItemModel) {
        if (item.type == NavigationItemType.APP) {
            loadNativeScreen(item.path)
        } else {
            loadWebView(categoryIcon, item)
        }
    }

    private fun loadWebView(categoryIcon: String?, item: NavigationItemModel) {
        val navController = this.findNavController(R.id.nav_host_fragment)
        navController.popBackStack()
        val url =
            Endpoints.adminEndpoint + "/auth?token=${vm.eatManager.token()}&restaurant_id=${vm.eatManager.restaurantId()}&path=${item.path}&iframe=cactus&type=dynamic"
        val bundle = Bundle().apply {
            putString(Constants.WEBVEW_URL, url)
            putString(Constants.KEY_NAVIGATION_CATEGORY_ICON, categoryIcon)
            putString(Constants.KEY_NAVIGATION_ITEM_NAME, item.title)
            putBoolean(Constants.KEY_IS_HTML, false)
        }
        navController.navigate(R.id.web_view_fragment, bundle)
    }

    private fun loadNativeScreen(path: String) {
        val navController = this.findNavController(R.id.nav_host_fragment)
        navController.popBackStack()
        when (path) {
            NavigationItemNativeScreens.FLOOR.path -> {
                navController.navigate(R.id.navigation_overview)
            }
            NavigationItemNativeScreens.GUESTS.path -> {
                navController.navigate(R.id.navigation_guests)
            }
            NavigationItemNativeScreens.REPORTS.path -> {
                navController.navigate(R.id.navigation_reports)
            }
            NavigationItemNativeScreens.PRINT_SETTINGS.path -> {
                navController.navigate(R.id.printer_fragment)
            }
        }
    }

    private fun createReservation() {
        if (!vm.permission.boolValue) {
            this.showErrorAlert(
                resources.getString(R.string.create_reservation),
                vm.permission.errorMessage
            )
            return
        }

        val navHostFragment = supportFragmentManager.findFragmentById(R.id.nav_host_fragment)
        val fr = navHostFragment?.childFragmentManager!!.fragments[0]
        val date = when (fr) {
            is OverviewFragment -> {
                fr.vm.date.value
            }
            is ReportsFragment -> {
                fr.vm.date.value
            }
            else -> {
                Date()
            }
        }

        vm.analyticsManager.trackAddReservationFromTabBar()

        val dateCalendar = Calendar.getInstance()
        dateCalendar.time = date ?: Date()

        val timeCalendar = Calendar.getInstance()
        timeCalendar.time = Date()

        dateCalendar[Calendar.HOUR_OF_DAY] = timeCalendar[Calendar.HOUR_OF_DAY]
        dateCalendar[Calendar.MINUTE] = timeCalendar[Calendar.MINUTE]
        dateCalendar[Calendar.SECOND] = timeCalendar[Calendar.SECOND]

        val intent = Intent(this, ReservationActivity::class.java)
        intent.putExtra(Constants.DATE, dateCalendar.time)
        if (fr is OverviewFragment) {
            intent.putExtra(Constants.IS_WAITLIST, fr.binding.tabLayout.selectedTabPosition == fr.waitlistIndex)
        }

        startActivityIntent.launch(intent)
    }

    var startActivityIntent = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        val reservation = result.data?.getParcelableExtra<Reservation>(Constants.RESERVATION_EXTRA)

        this.supportFragmentManager.fragments.forEach { fragment ->
            if (fragment is ReservationsFragment) {
                fragment.reservationForResult(reservation, result.resultCode)
            } else if (fragment is BottomSheetFragment) {
                fragment.childFragmentManager.fragments.forEach {
                    if (it is ReservationsFragment) {
                        it.reservationForResult(reservation, result.resultCode)
                    } else  if (it is NotificationsFragment) {
                        it.reservationForResult(reservation, result.resultCode)
                    }
                }
            }
        }

        reservation?.let {
            if (result.resultCode == Constants.RESERVATION_RESULT_ADDED) {
                checkIfPrintIsNeeded(reservation = it)
            } else if (result.resultCode == Constants.RESERVATION_RESULT_UPDATED) {
                val oldStatus = result.data?.getStringExtra(Constants.INITIAL_STATUS_EXTRA)
                checkIfPrintIsNeeded(oldStatus, it)
            }
        }
    }

    private fun showCategoryPopup(anchorView: View, category: NavigationItemCategory) {
        val popupView = NavigationPopupLayoutBinding.inflate(
            LayoutInflater.from(anchorView.context), null, false
        )

        val popupWindow = showPopup(popupView.root, anchorView)

        val adapter = NavigationCategoriesAdapter(
            sideBarExpanded = tabBarExpanded,
            isPopup = true,
            itemClickListener = { selectedCategory, item ->
                if (item.selected) {
                    popupWindow.dismiss()
                } else {
                    vm.toggleItemSelected(selectedCategory.title, item.title)
                    loadNavigationItem(selectedCategory.icon, item)
                    popupWindow.dismiss()
                }
            },
            categoryClickListener = {_, _ -> }
        )
        popupView.rvNavigationItems.adapter = adapter
        vm.findCategory(category.title)?.let {
            adapter.submitList(listOf(it))
        }
    }

    private fun showRestaurantSelectorPopup(anchorView: View) {
        val view = TabBarVenueView(this)
        view.updateRestaurantName(vm.eatManager.restaurant(), vm.eatManager.userEmail())
        view.showIconArrow(false)
        view.showLogout(true)
        view.setOnLogoutClickListener {
            <EMAIL>(R.string.logout_title, R.string.logout_desc) {
                vm.unRegisterUser()
                redirectToLogin()
            }
        }

        val popupWindow = showPopup(view, anchorView)

        view.restaurantClickListener = {
            popupWindow.dismiss()
            if (it != vm.eatManager.restaurantId()) {
                changeRestaurant(it)
            }
        }

        view.submitList( vm.eatManager.restaurants() ?: emptyList())

        // 40 for row height, 46 for search button, 64 for logout button
        val contentHeight = view.currentList().count().times(40.px).plus(46.px).plus(64.px)

        // 64 is for logout button
        val finalHeight = min(contentHeight, (RESTAURANT_SELECTOR_MAX_HEIGHT + 64.px))
        view.binding.containerRestaurants.updateLayoutParams { height = finalHeight }
    }

    private fun showPopup(viewToShow: View, anchorView: View): PopupWindow {
        // Create the PopupWindow
        val popupWindow = PopupWindow(viewToShow,
            POPUP_WIDTH,
            WRAP_CONTENT
        )

        // Set focusable to true so the popup can be dismissed by touching outside
        popupWindow.isFocusable = true

        // Position the popup window next to the clicked item
        val location = IntArray(2)
        anchorView.getLocationOnScreen(location)

        // Show the popup window with the custom view
        popupWindow.showAtLocation(anchorView, Gravity.NO_GRAVITY, SIDE_BAR_COLLAPSED_WIDTH + 8.px, location[1])

        return popupWindow
    }

    private fun loadRestaurantName() {
        binding.containerVenueView?.updateRestaurantName(
            vm.eatManager.restaurant(),
            vm.eatManager.userEmail()
        )
    }

    private fun redirectToLogin() {
        val intent = Intent(this, LaunchActivity::class.java)
        startActivity(intent)
        this.finish()
    }

    private fun preselectFloor() {
        vm.findItemAndCategory(NavigationItemNativeScreens.FLOOR.path)?.let {
            it.second?.let { item ->
                loadNavigationItem(it.first.icon, item)
                vm.toggleItemSelected(it.first.title, item.title)
            }
        }
    }

    private fun changeRestaurant(restaurantId: String) {
        binding.containerProgress?.visibleOrGone = true
        vm.changeRestaurant(restaurantId)
    }

    private fun openSupportScreen() {
        val intent = Intent(this@HomeActivity, HubspotActivity::class.java)
        startActivity(intent)
    }
}