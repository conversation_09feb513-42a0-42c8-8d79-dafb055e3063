package com.eatapp.clementine.ui.home.reports

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.reports.ReportsResponse
import com.eatapp.clementine.internal.ChartItem
import com.eatapp.clementine.internal.asLiveData
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.home.overview.DatesViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
open class PerformanceViewModel @Inject constructor(
    val eatManager: EatManager,
    analyticsManager: AnalyticsManager
) : DatesViewModel() {

    private val _shifts = MutableLiveData<List<ChartItem>>()
    val shifts: LiveData<List<ChartItem>> by lazy {
        _shifts.asLiveData()
    }

    private val _source = MutableLiveData<List<ChartItem>>()
    val source: LiveData<List<ChartItem>> by lazy {
        _source.asLiveData()
    }

    private val _type = MutableLiveData<List<ChartItem>>()
    val type: LiveData<List<ChartItem>> by lazy {
        _type.asLiveData()
    }

    private val _status = MutableLiveData<List<ChartItem>>()
    val status: LiveData<List<ChartItem>> by lazy {
        _status.asLiveData()
    }

    init {

        analyticsManager.trackViewReportsView()

        loading(true)

    }

    fun updateReports(reports: ReportsResponse) {

        processReports(reports)
    }

    private fun processReports(response: ReportsResponse) {

        loading(false)

        val attrs = response.data.attributes

        val cover0 = ChartItem(
            attrs.totalReservations.breakfast, attrs.totalReservations.total,
            R.string.breakfast, R.color.chart_blue, R.color.chart_title_blue
        )
        val cover1 = ChartItem(
            attrs.totalReservations.lunch, attrs.totalReservations.total,
            R.string.lunch, R.color.chart_orange, R.color.chart_title_orange
        )
        val cover2 = ChartItem(
            attrs.totalReservations.dinner, attrs.totalReservations.total,
            R.string.dinner, R.color.chart_purple, R.color.chart_title_purple
        )

        _shifts.postValue(listOf(cover0, cover1, cover2))

        val sourceTotal =
            attrs.bySource.inHouse.total + attrs.bySource.mobile.total + attrs.bySource.web.total

        val source0 = ChartItem(
            attrs.bySource.inHouse.total, sourceTotal,
            R.string.in_house, R.color.colorGrey25, R.color.colorGrey250
        )
        val source1 = ChartItem(
            attrs.bySource.mobile.total + attrs.bySource.web.total, sourceTotal,
            R.string.online, R.color.chart_green, R.color.chart_title_green
        )

        _source.postValue(listOf(source0, source1))

        val type0 = ChartItem(
            attrs.totalReservations.total - attrs.walkins.total, attrs.totalReservations.total,
            R.string.reservations, R.color.chart_green, R.color.chart_title_green
        )
        val type1 = ChartItem(
            attrs.walkins.total, attrs.totalReservations.total,
            R.string.walk_ins, R.color.chart_purple, R.color.chart_title_purple
        )

        _type.postValue(listOf(type0, type1))

        val statusTotal = attrs.byStatus.cancelations.total + attrs.byStatus.denied.total +
                attrs.byStatus.noShows.total + attrs.byStatus.otherStatuses.total

        val status0 = ChartItem(
            attrs.byStatus.otherStatuses.total, statusTotal,
            R.string.materialised, R.color.chart_green, R.color.chart_title_green
        )

        val status1 = ChartItem(
            attrs.byStatus.cancelations.total + attrs.byStatus.denied.total, statusTotal,
            R.string.cancelled, R.color.chart_red, R.color.chart_title_red
        )

        val status2 = ChartItem(
            attrs.byStatus.noShows.total, statusTotal,
            R.string.no_show, R.color.chart_grey, R.color.chart_title_grey
        )

        _status.postValue(listOf(status0, status1, status2))

    }
}