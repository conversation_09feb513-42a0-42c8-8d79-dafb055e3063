package com.eatapp.clementine.ui.reservation.payments.details

import android.view.View
import android.widget.Toast
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.DividerItemDecoration
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.payment.Payment
import com.eatapp.clementine.databinding.ReservationPaymentDetailsFragmentBinding
import com.eatapp.clementine.internal.managers.ActionType
import com.eatapp.clementine.internal.visible
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.reservation.ReservationActivity
import com.eatapp.clementine.views.LoadingButton
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ReservationPaymentDetailsFragment :
    BaseFragment<ReservationPaymentDetailsViewModel, ReservationPaymentDetailsFragmentBinding>() {

    private lateinit var gatewayActionsAdapter: GatewayActionsAdapter

    override fun viewModelClass() = ReservationPaymentDetailsViewModel::class.java

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    override fun inflateLayout() = ReservationPaymentDetailsFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {

        arguments?.let {
            vm.setPayment(ReservationPaymentDetailsFragmentArgs.fromBundle(it).payment)
        }

        (activity as? ReservationActivity)?.let {
            it.updateToolbarTitle(getString(R.string.payment_details_title))
            it.showToolbarButtons(false)
        }

        bindUI()
        observeData()
    }

    override fun onDestroy() {
        super.onDestroy()

        (activity as? ReservationActivity)?.let {
            it.setDefaultToolbarTitle()
            it.showToolbarButtons(true)
        }
    }

    private fun bindUI() = with(binding) {

        gatewayActionsAdapter = GatewayActionsAdapter()
        binding.recyclerGatewayActions.adapter = gatewayActionsAdapter
        binding.recyclerGatewayActions.addItemDecoration(
            DividerItemDecoration(
                context,
                DividerItemDecoration.VERTICAL
            )
        )

        btnRefund.setOnClickListener {
            val directions =
                ReservationPaymentDetailsFragmentDirections.actionReservationPaymentDetailsToReservationPaymentRefund(vm.payment.value!!)
            findNavController().navigate(directions)
        }

        btnSendReminder.setOnClickListener {
            vm.sendReminder(it as LoadingButton)
        }

        btnEdit.setOnClickListener {
            val directions = ReservationPaymentDetailsFragmentDirections.actionReservationPaymentDetailsToCreatePaymentFragment(vm.payment.value)
            findNavController().navigate(directions)
        }
    }

    private fun observeData() {
        vm.payment.observe(viewLifecycleOwner) { payment ->
            val sortedActions = payment.attributes.gatewayActions?.sortedByDescending { it.date }
            payment.attributes.gatewayActions = sortedActions
            gatewayActionsAdapter.updateData(payment)
            updateUI(payment)
        }

        vm.dataManager.update.observe(viewLifecycleOwner) { update ->
            if (update.contains(ActionType.Payment.type)) {
                vm.loadPayment()
            }
        }

        vm.reminderSent.observe(viewLifecycleOwner) {
            if (it) {
                Toast.makeText(requireContext(), "Payment reminder sent", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun updateUI(payment: Payment) {
        updateEditButtonState()
        updateButtonsVisibility()

        if (payment.attributes.gatewayActions.isNullOrEmpty()) {
            binding.paymentListItem.update(
                payment,
                paymentLinkVisible = false,
                chevronVisible = false
            )
            binding.paymentListItem.visible = true
        } else {
            binding.paymentListItem.visibleOrGone = false
        }
    }

    private fun updateEditButtonState() {
        if (vm.payment.value?.paymentRule?.id != null || vm.payment.value?.paymentRule?.id == "") {
            binding.btnEdit.state = LoadingButton.LoadingButtonState.Disabled
        }
    }

    private fun updateButtonsVisibility() {
        if (vm.eatManager.restaurant()?.attributes?.paymentGateway == getString(R.string.external_label)) {
            binding.containerButtons.visible = false
            return
        }

        vm.payment.value?.let {
            binding.run {
                btnCapture.visibleOrGone = it.attributes.captureUrl != null
                btnSendReminder.visibleOrGone = it.attributes.maySendReminder
                btnVoid.visibleOrGone = it.attributes.voidUrl != null
                btnRefund.visibleOrGone = it.attributes.refundUrl != null
                btnCancel.visibleOrGone = it.attributes.cancelUrl != null
                btnEdit.visibleOrGone = it.attributes.mayUpdate
            }
        }
    }
}