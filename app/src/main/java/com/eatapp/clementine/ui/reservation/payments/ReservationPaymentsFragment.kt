package com.eatapp.clementine.ui.reservation.payments

import android.os.Bundle
import android.view.View
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.ConcatAdapter
import androidx.recyclerview.widget.DividerItemDecoration
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.data.network.response.restaurant.Restaurant
import com.eatapp.clementine.databinding.ReservationPaymentsFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.internal.managers.ActionType
import com.eatapp.clementine.internal.visible
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.reservation.ReservationActivity
import com.eatapp.clementine.ui.reservation.ReservationFragmentDirections
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class ReservationPaymentsFragment :
    BaseFragment<ReservationPaymentsViewModel, ReservationPaymentsFragmentBinding>() {

    private lateinit var paymentsAdapter: ReservationPaymentsAdapter
    private lateinit var paymentsHeaderAdapter: ReservationPaymentsHeaderAdapter

    override fun viewModelClass() = ReservationPaymentsViewModel::class.java

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    override fun inflateLayout() = ReservationPaymentsFragmentBinding.inflate(layoutInflater)

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        (activity as? ReservationActivity)?.reservation
            ?.let { reservation ->
                vm.reservation(reservation)
                vm.payments(reservation.payments)
            }
    }

    override fun viewCreated() {
        (activity as? ReservationActivity)?.let {
            it.setDefaultToolbarTitle()
            it.showToolbarButtons(true)
        }
        bindUI()
        observe()
        checkButtonStatus()
    }

    private fun observe() {
        vm.payments.observe(viewLifecycleOwner) { payments ->
            if (!payments.isNullOrEmpty()) {

                payments[0].amountUI = vm.calculateAmount()
                binding.paymentEmptyState.root.visibleOrGone = false
                binding.guestEmptyState.root.visibleOrGone = false

                paymentsHeaderAdapter.submitList(listOf(payments[0]))
                paymentsAdapter.submitList(payments)

                // This means payment was added
                if (payments.size != paymentsAdapter.itemCount) {
                    binding.recyclerPayments.post {
                        binding.recyclerPayments.scrollToPosition(0)
                    }
                }
            }
        }

        vm.dataManager.update.observe(viewLifecycleOwner) { update ->
            if (update.contains(ActionType.Payment.type)) {
                vm.loadPayments()
            }
        }
    }

    private fun bindUI() = with(binding) {
        paymentsAdapter = ReservationPaymentsAdapter()
        paymentsHeaderAdapter = ReservationPaymentsHeaderAdapter()
        paymentsAdapter.paymentLinkClickListener = {
            openBrowser(it)
        }

        paymentsAdapter.paymentItemClickListener = {
            val directions =
                ReservationFragmentDirections.actionReservationToReservationPaymentDetails(it)
            findNavController().navigate(directions)
        }

        recyclerPayments.apply {
            adapter = ConcatAdapter(paymentsHeaderAdapter, paymentsAdapter)
            addItemDecoration(DividerItemDecoration(context, DividerItemDecoration.VERTICAL))
        }

        btnCreatePayment.setOnClickListener {
            findNavController().navigate(ReservationFragmentDirections.actionReservationFragmentToCreatePaymentFragment(null))
        }

        if (vm.reservation.value?.guest == null) {
            guestEmptyState.root.visible = true
            return@with
        }

        if (vm.reservation.value?.payments.isNullOrEmpty()) {
            paymentEmptyState.root.visible = true
        }
    }

    private fun checkButtonStatus() {
        binding.btnCreatePayment.visibleOrGone = vm.eatManager.restaurant()?.paymentsActivated == true
                && vm.reservation.value?.guest != null
                && vm.eatManager.restaurant()?.attributes?.paymentGateway != Restaurant.externalPaymentGateway

        binding.paymentEmptyState.textSubtitle.visibleOrGone = vm.eatManager.restaurant()?.attributes?.paymentGateway != Restaurant.externalPaymentGateway
    }
}