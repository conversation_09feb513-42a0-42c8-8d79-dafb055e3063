package com.eatapp.clementine.ui.reservation.loyalty

import android.annotation.SuppressLint
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.core.content.ContextCompat
import androidx.core.view.children
import androidx.core.widget.addTextChangedListener
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.viewModels
import com.eatapp.clementine.R
import com.eatapp.clementine.data.network.response.guest.Guest
import com.eatapp.clementine.databinding.PopupDialogLoyaltyBinding
import com.eatapp.clementine.internal.hideKeyboard
import com.eatapp.clementine.internal.visibleOrGone
import com.eatapp.clementine.views.LoyaltyModificationListener
import com.eatapp.clementine.views.LoyaltyModificationView
import dagger.hilt.android.AndroidEntryPoint
import kotlin.math.abs


@AndroidEntryPoint
class LoyaltyPopupDialog : DialogFragment() {

    private lateinit var binding: PopupDialogLoyaltyBinding
    private lateinit var guest: Guest

    private val loyaltySharedViewModel by viewModels<LoyaltySharedViewModel>(
        ownerProducer = { requireParentFragment() }
    )

    companion object {

        private const val ARG_GUEST = "arg_guest"

        fun newInstance(guest: Guest): LoyaltyPopupDialog {
            val fragment = LoyaltyPopupDialog()
            val bundle = Bundle().apply {
                putParcelable(ARG_GUEST, guest)
            }
            fragment.arguments = bundle
            return fragment
        }
    }

    override fun onStart() {
        super.onStart()
        // Resize the dialog
        dialog?.window?.setLayout(
            (resources.displayMetrics.widthPixels * 0.85).toInt(),
            ViewGroup.LayoutParams.WRAP_CONTENT
        )
        dialog?.window?.setBackgroundDrawableResource(R.drawable.shape_rounded_dialog_background)
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        arguments?.let {
            guest = it.getParcelable(ARG_GUEST)!!
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        binding = PopupDialogLoyaltyBinding.inflate(layoutInflater)
        setupUI()
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        loyaltySharedViewModel.dismissPopup.observe(viewLifecycleOwner) {
            dismiss()
        }

        loyaltySharedViewModel.showPopupLoading.observe(viewLifecycleOwner) {
            showLoading(it)
        }
    }

    fun showLoading(loading: Boolean) {
        binding.btnAction.showLoading(loading)
        binding.btnAction.isEnabled = !loading
    }

    private fun setupUI() = with(binding) {
        (containerIncrementsPlus.children + containerIncrementsMinus.children)
            .filterIsInstance<LoyaltyModificationView>().forEach {
                it.listener = LoyaltyModificationListenerImpl()
            }

        btnAction.setTitle(getString(R.string.send_message_confirmed_label).replaceFirstChar {
            it.toString().uppercase()
        })
        etPoints.setText(guest.attributes?.loyaltyPoints.toString())
        tvCurrentState.text =
            getString(R.string.current_points, guest.firstName, guest.attributes?.loyaltyPoints)
        ivClose.setOnClickListener {
            dismiss()
        }
        btnAction.setOnClickListener {
            val currentPoints = guest.attributes?.loyaltyPoints
            val updatedPoints = etPoints.text.toString().toInt()
            if (currentPoints != updatedPoints) {
                loyaltySharedViewModel.updatePoints(updatedPoints)
            } else {
                dismiss()
            }
        }

        etPoints.addTextChangedListener {
            updateModificationText(it.toString())
        }
    }

    @SuppressLint("SetTextI18n")
    private fun updateModificationText(points: String) {
        // If input text is empty set text to 0
        if (points.isEmpty()) {
            binding.etPoints.setText("0")
            binding.etPoints.setSelection(binding.etPoints.text.length)
            return
        }

        // If user starts typing while 0 is leading, remove 0
        if (points != "0" && points.startsWith("0") && points.length > 1) {
            binding.etPoints.setText(points.trimStart('0'))
            binding.etPoints.setSelection(binding.etPoints.text.length)
            return
        }

        // If input is larger than Int.MAX, set it to Int.MAX
        val value = points.toLong()
        if (value > Int.MAX_VALUE) {
            binding.etPoints.setText(Int.MAX_VALUE.toString())
            binding.etPoints.setSelection(binding.etPoints.text.length)
            return
        }

        // If no change has been made, hide the updated points view
        val updatedPoints = points.toInt()
        val currentPoints = guest.attributes?.loyaltyPoints ?: 0
        if (updatedPoints == currentPoints) {
            binding.tvModificationInfo.visibleOrGone = false
            return
        }

        binding.tvModificationInfo.run {
            visibleOrGone = true

            val isAddition = updatedPoints > currentPoints
            val diffPoints = abs(updatedPoints - currentPoints)

            text = getString(
                if (isAddition) R.string.added_points else R.string.removed_points,
                diffPoints
            )

            setBackgroundResource(
                if (isAddition) R.drawable.shape_dotted_background_selected
                else R.drawable.shape_dotted_background_error
            )

            setTextColor(
                ContextCompat.getColor(
                    requireContext(),
                    if (isAddition) R.color.green900 else R.color.amber900
                )
            )
        }
    }

    inner class LoyaltyModificationListenerImpl : LoyaltyModificationListener {
        @SuppressLint("SetTextI18n")
        override fun onPlusValueClicked(value: Int) {
            val currentPoints = binding.etPoints.text.toString().toInt()
            val updatedPoints = currentPoints + value
            binding.etPoints.setText(updatedPoints.toString())
            binding.etPoints.clearFocus()
            requireContext().hideKeyboard(binding.etPoints)
        }

        @SuppressLint("SetTextI18n")
        override fun onMinusValueClicked(value: Int) {
            val currentPoints = binding.etPoints.text.toString().toInt()
            val updatedPoints = (currentPoints - value).coerceAtLeast(0)
            binding.etPoints.setText(updatedPoints.toString())
            binding.etPoints.clearFocus()
            requireContext().hideKeyboard(binding.etPoints)
        }
    }
}