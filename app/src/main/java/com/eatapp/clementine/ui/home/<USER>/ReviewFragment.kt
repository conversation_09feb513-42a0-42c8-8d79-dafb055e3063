package com.eatapp.clementine.ui.home.reports

import android.os.Bundle
import android.view.View
import com.eatapp.clementine.data.network.response.reservation.Reservation
import com.eatapp.clementine.databinding.ReviewFragmentBinding
import com.eatapp.clementine.internal.Constants
import com.eatapp.clementine.ui.base.BaseFragment
import dagger.hilt.android.AndroidEntryPoint


@AndroidEntryPoint
class ReviewFragment : BaseFragment<ReviewViewModel, ReviewFragmentBinding>() {

    companion object {

        fun newInstance(p: Reservation) = ReviewFragment().apply {
            arguments = Bundle().apply {
                putParcelable(Constants.RESERVATION_EXTRA, p)
            }
        }
    }

    override fun viewModelClass() = ReviewViewModel::class.java

    override fun inflateLayout() = ReviewFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {
        arguments?.getParcelable<Reservation>(Constants.RESERVATION_EXTRA)
            ?.let { reservation ->
                vm.reservation(reservation)
            }
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

}
