package com.eatapp.clementine.ui.common.selector

import android.Manifest
import android.app.Activity.RESULT_OK
import android.content.pm.PackageManager
import android.os.Build
import android.util.Log
import android.view.View
import androidx.core.content.ContextCompat
import com.eatapp.clementine.R
import com.eatapp.clementine.adapter.RestaurantsAdapter
import com.eatapp.clementine.databinding.RestaurantsFragmentBinding
import com.eatapp.clementine.internal.showAlert
import com.eatapp.clementine.internal.showConfirmationAlert
import com.eatapp.clementine.ui.base.BaseFragment
import com.google.firebase.messaging.FirebaseMessaging
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RestaurantsFragment : BaseFragment<RestaurantsViewModel, RestaurantsFragmentBinding>() {

    private lateinit var adapter: RestaurantsAdapter

    override fun inflateLayout() = RestaurantsFragmentBinding.inflate(layoutInflater)

    override fun viewModelClass() = RestaurantsViewModel::class.java

    override fun viewCreated() {
        bindUI()
        observe()
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    private fun bindUI() = with(binding) {
        adapter = RestaurantsAdapter(vm.restaurants) { vm.selectRestaurant(it.id) }
        adapter.submitList(vm.restaurants)
        restaurantsList.adapter = adapter
        restaurantsList.scrollToPosition(vm.selectedRestaurantIndex)

        cancelIcon.setOnClickListener {

            if (searchEditText.text.isEmpty()) return@setOnClickListener

            searchEditText.setText("")
            searchEditText.clearFocus()
            hideKeyboard()
        }

        changeBtn.setOnClickListener {
            requireContext().showConfirmationAlert(
                R.string.switch_restaurant_title,
                R.string.switch_restaurant_desc
            ) {
                FirebaseMessaging.getInstance().unsubscribeFromTopic(
                    "restaurant_${vm.eatManager.restaurantId()}"
                )
                vm.changeRestaurant()
                hideKeyboard()
            }
        }
    }

    private fun observe() {
        vm.search.observe(viewLifecycleOwner) { query ->
            vm.filterRestaurants(query)
            adapter.submitList(vm.restaurants)
        }

        vm.loading.observe(viewLifecycleOwner) {
            when (it) {
                true -> binding.changeBtn.showLoading()
                else -> {
                    binding.changeBtn.hideLoading()
                    checkNotificationsPermissionAndSubscribe()

                    activity?.setResult(RESULT_OK)
                    activity?.finish()
                }
            }
        }
    }

    private fun checkNotificationsPermissionAndSubscribe() {
        // This is only necessary for API level >= 33 (TIRAMISU)
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            if (ContextCompat.checkSelfPermission(requireActivity(), Manifest.permission.POST_NOTIFICATIONS) == PackageManager.PERMISSION_GRANTED) {
                subscribeToChannel()
            }
        } else {
            subscribeToChannel()
        }
    }

    private fun subscribeToChannel() {
        FirebaseMessaging.getInstance().subscribeToTopic(
            "restaurant_${vm.eatManager.restaurantId()}"
        )
    }
}
