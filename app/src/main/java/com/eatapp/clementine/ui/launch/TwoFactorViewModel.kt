package com.eatapp.clementine.ui.launch

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.auth.Auth
import com.eatapp.clementine.data.repository.FabricateRepository
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.ui.base.BaseViewModel
import dagger.hilt.android.lifecycle.HiltViewModel
import javax.inject.Inject

@HiltViewModel
class TwoFactorViewModel @Inject constructor(
    private val fabricateRepository: FabricateRepository,
    private val eatManager: EatManager,
    private val analyticsManager: AnalyticsManager,
): BaseViewModel() {

    val authentication = MutableLiveData<SharedLaunchViewModel.AuthenticationState>()

    private var token: String? = null

    fun performTwoFactorLogin(otp: String) {
        loading(true)
        launch({
            val auth = fabricateRepository.twoFactor(token!!, otp)

            completeLogin(auth.data)
            loading(false)
        })
    }

    private fun completeLogin(auth: Auth) {
        eatManager.userData(auth)
        analyticsManager.trackLogin()

        authentication.postValue(SharedLaunchViewModel.AuthenticationState.AUTHENTICATED)
    }

    fun token(token: String) {
        this.token = token
    }
}