package com.eatapp.clementine.ui.reservation

import com.eatapp.clementine.data.network.response.comment.Comment
import com.eatapp.clementine.data.network.response.reservation.Channel
import com.eatapp.clementine.data.network.response.room.Table
import com.eatapp.clementine.data.network.response.tagging.Tagging
import com.eatapp.clementine.data.network.response.vouchers.VoucherAssignment
import java.util.Date

data class ReservationState(
    val guestId: String?,
    val tempGuestName: String?,
    val preference: Channel?,
    val status: String?,
    val walkIn: Boolean?,
    val date: Date?,
    val time: Date?,
    val duration: Int?,
    val covers: Int?,
    val waitQuote: Int?,
    val tables: List<Table>?,
    val notes: String?,
    val comments: List<Comment>?,
    val taker: String?,
    val tags: List<Tagging>?,
    val customFields: Map<String, Any>?
)