package com.eatapp.clementine.ui.common.selector

import android.os.Bundle
import com.eatapp.clementine.R
import com.eatapp.clementine.databinding.RestaurantsActivityBinding
import com.eatapp.clementine.ui.base.BaseActivity
import dagger.hilt.android.AndroidEntryPoint

@AndroidEntryPoint
class RestaurantsActivity : BaseActivity<RestaurantsActivityBinding>() {

    override fun getLayoutId() = R.layout.restaurants_activity

    override fun onCreated(savedInstanceState: Bundle?) {
        if (savedInstanceState == null) {
            supportFragmentManager
                .beginTransaction()
                .replace(R.id.container, RestaurantsFragment())
                .commitNow()
        }

        binding.backBtnRS.setOnClickListener {
            finish()
        }
    }
}
