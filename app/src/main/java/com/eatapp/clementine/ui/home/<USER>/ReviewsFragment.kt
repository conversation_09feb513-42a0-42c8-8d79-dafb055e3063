package com.eatapp.clementine.ui.home.reports

import android.app.DatePickerDialog
import android.view.View
import androidx.lifecycle.LifecycleOwner
import com.eatapp.clementine.adapter.ReviewsAdapter
import com.eatapp.clementine.databinding.ReviewsFragmentBinding
import com.eatapp.clementine.internal.managers.ActionType
import com.eatapp.clementine.internal.startOfTheEatDay
import com.eatapp.clementine.ui.base.BaseFragment
import com.eatapp.clementine.ui.home.HomeActivity
import dagger.hilt.android.AndroidEntryPoint
import java.text.SimpleDateFormat
import java.util.*
import kotlin.concurrent.schedule


@AndroidEntryPoint
class ReviewsFragment : BaseFragment<ReviewsViewModel, ReviewsFragmentBinding>() {

    private lateinit var adapter: ReviewsAdapter
    private var timer: TimerTask? = null

    override fun viewModelClass() = ReviewsViewModel::class.java

    override fun inflateLayout() = ReviewsFragmentBinding.inflate(layoutInflater)

    override fun viewCreated() {
        bindUI()
        observe()

        vm.setDate((activity as? HomeActivity)?.vm?.sharedDate?.value ?: startOfTheEatDay(Date()))
    }

    override fun onShowLockdownView(view: View) {}

    override fun onHideLockdownView(view: View) {}

    private fun bindUI() {
        progress = binding.progress

        adapter = ReviewsAdapter {
            bottomSheetFragment(
                String.format("%s's review", it.guestName),
                ReviewFragment.newInstance(it)
            )
        }

        binding.reviewsList.adapter = adapter

        binding.date.setOnClickListener {

            val c = Calendar.getInstance()
            c.time = vm.date.value!!

            val datePicker = DatePickerDialog(
                requireContext(), { _, y, m, d ->

                    vm.updateDate(y, m, d)

                }, c.get(Calendar.YEAR), c.get(Calendar.MONTH), c.get(Calendar.DAY_OF_MONTH)
            )

            datePicker.show()
        }
    }

    private fun observe() {

        vm.date.observe(viewLifecycleOwner) { d ->

            binding.date.text = SimpleDateFormat("EEE, d MMM", Locale.US)
                .format(d.time)

           showProgress(true)

            timer?.cancel()

            timer = Timer().schedule(500) {
                vm.updateDate()
                hideKeyboard()
            }
        }

        vm.reservations.observe(viewLifecycleOwner) { res ->

            if (vm.loading.value == false && (res == null || res.second.isEmpty())) {

                binding.run {
                    emptyList.visibility = if (res.first) View.VISIBLE else View.GONE
                    emptySearchList.visibility = if (res.first) View.GONE else View.VISIBLE
                    reviewsList.visibility = View.INVISIBLE
                    searchEditText.visibility = if (res.first) View.GONE else View.VISIBLE
                    listSeparator.visibility = if (res.first) View.GONE else View.VISIBLE
                    totalReviewsMain.text = "0"
                }
                adapter.submitList(null)

            } else {

                binding.run {
                    emptyList.visibility = View.GONE
                    emptySearchList.visibility = View.GONE
                    reviewsList.visibility = View.VISIBLE
                    searchEditText.visibility = View.VISIBLE
                    listSeparator.visibility = View.VISIBLE
                    totalReviewsMain.text = String.format("%s", res.second.size)
                }
                adapter.submitList(res.second)
            }

            showProgress(false)
        }

        vm.dataManager.update.observe(viewLifecycleOwner) { update ->
            if (update.contains(ActionType.Reservation.type)) {
                vm.reservations(false)
            }
        }

        vm.dataManager.polling.observe(viewLifecycleOwner) { poll ->
            if (poll) vm.initTimers()
        }
    }

    private fun showProgress(show: Boolean) = with(binding) {
        when (show) {
            true -> {
                date.visibility = View.INVISIBLE
                progressReviews.visibility = View.VISIBLE
            }
            false -> {
                date.visibility = View.VISIBLE
                progressReviews.visibility = View.GONE
            }
        }
    }
}
