package com.eatapp.clementine.ui.common.selector

import androidx.lifecycle.MutableLiveData
import com.eatapp.clementine.data.network.response.restaurant.Restaurant
import com.eatapp.clementine.data.repository.FabricateRepository
import com.eatapp.clementine.internal.managers.AnalyticsManager
import com.eatapp.clementine.internal.managers.Commands
import com.eatapp.clementine.internal.managers.DataManager
import com.eatapp.clementine.internal.managers.EatManager
import com.eatapp.clementine.internal.managers.ShiftManager
import com.eatapp.clementine.ui.launch.FabricateViewModel
import com.eatapp.clementine.views.LockdownView
import dagger.hilt.android.lifecycle.HiltViewModel
import java.util.Locale
import javax.inject.Inject

@HiltViewModel
class RestaurantsViewModel @Inject constructor(
    val eatManager: EatManager,
    private val shiftManager: ShiftManager,
    private val dataManager: DataManager,
    private val analyticsManager: AnalyticsManager,
    private val fabricateRepository: FabricateRepository
) : FabricateViewModel(
    eatManager,
    shiftManager,
    dataManager,
    analyticsManager,
    fabricateRepository
) {

    var preSelectedRestaurant = MutableLiveData<String>()
    var selectedRestaurant = MutableLiveData<String>()
    var search = MutableLiveData<String>()

    var restaurants: List<Restaurant>? = eatManager.restaurants()

    var selectedRestaurantIndex: Int = 0

    init {

        restaurants?.forEachIndexed { index, restaurant ->
            when (restaurant.id == eatManager.restaurantId()) {
                true -> {
                    restaurant.isSelected = true
                    preSelectedRestaurant.value = restaurant.id
                    selectedRestaurant.value = restaurant.id
                    selectedRestaurantIndex = index - 2
                }
                else -> restaurant.isSelected = false
            }
        }

        search.value = ""
    }

    fun selectRestaurant(restaurantId: String) {

        selectedRestaurant.value = restaurantId
    }

    fun filterRestaurants(query: String) {

        restaurants = eatManager.restaurants()?.filter { restaurant ->
            restaurant.name.lowercase(Locale.getDefault())
                .contains(query.lowercase(Locale.getDefault()))
        }
    }

    fun changeRestaurant() {

        loading(true)

        if (dataManager.isConnected()) {
            dataManager.updateSubscription(Commands.Unsubscribe.type, eatManager.restaurantId())
        }

        launch({

            val restaurant = fabricateRepository.restaurant(selectedRestaurant.value!!)
            eatManager.restaurant(restaurant)

            LockdownView.messageDisplayedAt = null

            registerUser()

            fetchRestaurantData(eatManager.restaurantId())

        }, false)
    }
}