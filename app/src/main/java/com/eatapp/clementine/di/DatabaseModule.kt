package com.eatapp.clementine.di

import android.app.Application
import androidx.room.Room
import com.eatapp.clementine.data.database.dao.ReservationDao
import com.eatapp.clementine.data.database.db.ReservationDatabase
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent

@Module
@InstallIn(SingletonComponent::class)
class DatabaseModule {

    @Provides
    fun provideReservationDatabase(app: Application) : ReservationDatabase {
        return Room.databaseBuilder(
            app, ReservationDatabase::
            class.java, "reservation_database"
        ).fallbackToDestructiveMigration().build()
    }

    @Provides
    fun provideReservationDao(db: ReservationDatabase) : ReservationDao {
        return db.reservationDao()
    }
}